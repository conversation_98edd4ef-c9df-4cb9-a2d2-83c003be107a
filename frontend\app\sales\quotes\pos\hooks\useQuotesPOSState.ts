import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Product } from '../types';
import type { Customer } from '@/components/CustomerModal';

// Simple UUID generator for draft quotes
const generateDraftId = () => `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Quote item interface
export interface QuoteItem {
  productUuid: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  order: number;
}

// Internal quote state interface
export interface POSQuoteState {
  // Quote identifiers
  quoteUuid: string | null;
  customerUuid: string | null;
  
  // Quote data
  items: QuoteItem[];
  customer: Customer | null;
  
  // Pricing and totals
  subtotal: number;
  tax: number;
  taxRate: number;
  taxEnabled: boolean;
  total: number;
  
  // Additional info
  notes: string;
  notesEnabled: boolean;
  
  // Quote specific fields
  quoteDate: string | null;
  expiryDate: string | null;
  
  // Status
  isDirty: boolean; // Has unsaved changes
  isSubmitting: boolean;
  isLoading: boolean; // Loading cart/quote data
  error: string | null;
  
  // Mode tracking
  mode: 'new' | 'edit' | 'load';
  originalQuoteUuid?: string; // For edit mode
}

export interface UseQuotesPOSStateReturn {
  // State
  quoteState: POSQuoteState;
  
  // Quote management
  startNewQuote: () => void;
  loadQuoteForEdit: (quoteUuid: string, quoteData?: any) => void;
  loadQuoteForContinue: (quoteUuid: string, quoteData?: any) => void;
  clearQuote: () => void;
  
  // Customer management
  setCustomer: (customer: Customer | null) => void;
  
  // Item management
  addItem: (product: Product, quantity: number, unitPrice: number) => void;
  removeItem: (productUuid: string) => void;
  updateItemQuantity: (productUuid: string, quantity: number) => void;
  updateItemPrice: (productUuid: string, unitPrice: number) => void;
  clearItems: () => void;
  
  // Pricing controls
  setTaxEnabled: (enabled: boolean) => void;
  setTaxRate: (rate: number) => void;
  recalculateTotals: () => void;
  
  // Quote specific controls
  setQuoteDate: (date: string | null) => void;
  setExpiryDate: (date: string | null) => void;
  
  // Notes
  setNotes: (notes: string) => void;
  setNotesEnabled: (enabled: boolean) => void;
  
  // Status management
  setError: (error: string | null) => void;
  setSubmitting: (isSubmitting: boolean) => void;
  setLoading: (isLoading: boolean) => void;
  markClean: () => void;
  
  // Validation
  validateQuote: () => { isValid: boolean; errors: string[] };
}

// Default state
const createDefaultState = (): POSQuoteState => ({
  quoteUuid: null,
  customerUuid: null,
  items: [],
  customer: null,
  subtotal: 0,
  tax: 0,
  taxRate: 0.1, // 10% default
  taxEnabled: false,
  total: 0,
  notes: '',
  notesEnabled: false,
  quoteDate: null,
  expiryDate: null,
  isDirty: false,
  isSubmitting: false,
  isLoading: false,
  error: null,
  mode: 'new'
});

export function useQuotesPOSState(): UseQuotesPOSStateReturn {
  const { user } = useAuth();
  const [quoteState, setQuoteState] = useState<POSQuoteState>(createDefaultState);
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  // Create a dependency that tracks item data changes
  const itemsDependency = useMemo(() => {
    return quoteState.items.map(item => ({
      productUuid: item.productUuid,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice
    }));
  }, [quoteState.items]);

  // Calculate totals whenever items, tax settings change
  const recalculateTotals = useCallback(() => {
    setQuoteState(prev => {
      const subtotal = prev.items.reduce((sum, item) => sum + item.totalPrice, 0);
      const tax = prev.taxEnabled ? subtotal * prev.taxRate : 0;
      const total = subtotal + tax;
      
      // Round all totals to 2 decimal places
      const roundedSubtotal = Math.round(subtotal * 100) / 100;
      const roundedTax = Math.round(tax * 100) / 100;
      const roundedTotal = Math.round(total * 100) / 100;
      
      return {
        ...prev,
        subtotal: roundedSubtotal,
        tax: roundedTax,
        total: roundedTotal,
        isDirty: true
      };
    });
  }, []);

  // Auto-recalculate when items or tax settings change
  useEffect(() => {
    recalculateTotals();
  }, [itemsDependency, quoteState.taxEnabled, quoteState.taxRate, recalculateTotals]);

  // Quote management functions
  const startNewQuote = useCallback(() => {
    const newQuoteUuid = generateDraftId();
    const today = new Date().toISOString().split('T')[0];
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30); // 30 days from now
    
    setQuoteState({
      ...createDefaultState(),
      quoteUuid: newQuoteUuid,
      quoteDate: today,
      expiryDate: expiryDate.toISOString().split('T')[0],
      mode: 'new'
    });
  }, []);

  const loadQuoteForEdit = useCallback((quoteUuid: string, quoteData?: any) => {
    // Transform and validate items from backend
    const transformItems = (items: any[]): QuoteItem[] => {
      return items.map((item, index) => {
        // Handle both backend response formats (itemsSnapshot and items)
        const productUuid = item.productUuidString || item.productUuid || `unknown_${index}`;
        
        // Ensure productName is a string
        let productName = 'Unknown Product';
        if (typeof item.productName === 'string') {
          productName = item.productName;
        } else if (typeof item.name === 'string') {
          productName = item.name;
        } else if (item.productName && typeof item.productName === 'object' && item.productName.name) {
          productName = item.productName.name;
        } else if (item.name && typeof item.name === 'object' && item.name.name) {
          productName = item.name.name;
        }
        
        // Ensure all numeric values are valid numbers (handle both string and number types)
        const quantity = (() => {
          if (typeof item.quantity === 'number' && !isNaN(item.quantity)) return item.quantity;
          if (typeof item.quantity === 'string') {
            const parsed = parseFloat(item.quantity);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        const unitPrice = (() => {
          if (typeof item.unitPrice === 'number' && !isNaN(item.unitPrice)) return item.unitPrice;
          if (typeof item.unitPrice === 'string') {
            const parsed = parseFloat(item.unitPrice);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        // Use lineTotal from backend if available, otherwise calculate
        const totalPrice = (() => {
          if (typeof item.lineTotal === 'number' && !isNaN(item.lineTotal)) return item.lineTotal;
          if (typeof item.lineTotal === 'string') {
            const parsed = parseFloat(item.lineTotal);
            return !isNaN(parsed) ? parsed : quantity * unitPrice;
          }
          return quantity * unitPrice;
        })();
        
        // Use order from backend if available, otherwise use index
        const order = (() => {
          if (typeof item.order === 'number' && !isNaN(item.order)) return item.order;
          if (typeof item.order === 'string') {
            const parsed = parseInt(item.order);
            return !isNaN(parsed) ? parsed : index;
          }
          return index;
        })();
        
        return {
          productUuid,
          name: productName,
          quantity,
          unitPrice,
          totalPrice,
          order
        };
      });
    };

    setQuoteState(prev => {
      const items = quoteData?.itemsSnapshot || quoteData?.items || quoteData?.quoteItems || [];
      
      const transformedItems = transformItems(items);
      
      // Ensure numeric values are valid
      const taxRate = typeof quoteData?.taxRate === 'number' && !isNaN(quoteData.taxRate) ? quoteData.taxRate : 0.1;
      
      return {
        ...prev,
        quoteUuid,
        originalQuoteUuid: quoteUuid,
        mode: 'edit',
        isDirty: false,
        // Load quote data if provided
        ...(quoteData && {
          customerUuid: quoteData.customerUuid || null,
          customer: quoteData.customer || null,
          items: transformedItems,
          taxEnabled: quoteData.useTax || false,
          taxRate,
          notes: quoteData.notes || '',
          quoteDate: quoteData.quoteDate || null,
          expiryDate: quoteData.expiryDate || null
        })
      };
    });
  }, []);

  const loadQuoteForContinue = useCallback((quoteUuid: string, quoteData?: any) => {
    // Same implementation as loadQuoteForEdit for now
    loadQuoteForEdit(quoteUuid, quoteData);
  }, [loadQuoteForEdit]);

  const clearQuote = useCallback(() => {
    setQuoteState(createDefaultState());
  }, []);

  // Customer management
  const setCustomer = useCallback((customer: Customer | null) => {
    setQuoteState(prev => ({
      ...prev,
      customer,
      customerUuid: customer?.uuid || null,
      isDirty: true
    }));
  }, []);

  // Item management
  const addItem = useCallback((product: Product, quantity: number, unitPrice: number) => {
    if (quantity <= 0 || unitPrice < 0) return;

    // Round quantity and unitPrice to 2 decimal places
    const roundedQuantity = Math.round(quantity * 100) / 100;
    const roundedUnitPrice = Math.round(unitPrice * 100) / 100;

    setQuoteState(prev => {
      const existingItemIndex = prev.items.findIndex(item => item.productUuid === product.uuid);
      
      let newItems: QuoteItem[];
      if (existingItemIndex >= 0) {
        // Update existing item - preserve its order
        newItems = [...prev.items];
        const existingItem = newItems[existingItemIndex];
        const newQuantity = Math.round((existingItem.quantity + roundedQuantity) * 100) / 100;
        const newTotalPrice = Math.round(newQuantity * roundedUnitPrice * 100) / 100;
        
        newItems[existingItemIndex] = {
          ...existingItem,
          quantity: newQuantity,
          unitPrice: roundedUnitPrice, // Update unit price in case it changed
          totalPrice: newTotalPrice
        };
      } else {
        // Add new item - assign next order number
        const totalPrice = Math.round(roundedQuantity * roundedUnitPrice * 100) / 100;
        const nextOrder = prev.items.length; // Order is 0-based index
        const newItem: QuoteItem = {
          productUuid: product.uuid,
          name: product.name,
          quantity: roundedQuantity,
          unitPrice: roundedUnitPrice,
          totalPrice: totalPrice,
          order: nextOrder
        };
        newItems = [...prev.items, newItem];
      }

      return {
        ...prev,
        items: newItems,
        isDirty: true
      };
    });
  }, []);

  const removeItem = useCallback((productUuid: string) => {
    setQuoteState(prev => {
      const updatedItems = prev.items.filter(item => item.productUuid !== productUuid);
      
      // Reorder remaining items to maintain sequential order
      const reorderedItems = updatedItems.map((item, index) => ({
        ...item,
        order: index
      }));
      
      return {
        ...prev,
        items: reorderedItems,
        isDirty: true
      };
    });
  }, []);

  const updateItemQuantity = useCallback((productUuid: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(productUuid);
      return;
    }

    // Round quantity to 2 decimal places
    const roundedQuantity = Math.round(quantity * 100) / 100;

    setQuoteState(prev => {
      const updatedItems = prev.items.map(item => 
        item.productUuid === productUuid 
          ? { 
              ...item, 
              quantity: roundedQuantity, 
              totalPrice: Math.round(roundedQuantity * item.unitPrice * 100) / 100 
            }
          : item
      );
      
      return {
        ...prev,
        items: updatedItems,
        isDirty: true
      };
    });
  }, [removeItem]);

  const updateItemPrice = useCallback((productUuid: string, unitPrice: number) => {
    if (unitPrice < 0) return;

    // Round unitPrice to 2 decimal places
    const roundedUnitPrice = Math.round(unitPrice * 100) / 100;

    setQuoteState(prev => {
      const updatedItems = prev.items.map(item => 
        item.productUuid === productUuid 
          ? { 
              ...item, 
              unitPrice: roundedUnitPrice, 
              totalPrice: Math.round(item.quantity * roundedUnitPrice * 100) / 100 
            }
          : item
      );
      
      return {
        ...prev,
        items: updatedItems,
        isDirty: true
      };
    });
  }, []);

  const clearItems = useCallback(() => {
    setQuoteState(prev => ({
      ...prev,
      items: [],
      isDirty: true
    }));
  }, []);

  // Pricing controls
  const setTaxEnabled = useCallback((enabled: boolean) => {
    setQuoteState(prev => ({
      ...prev,
      taxEnabled: enabled,
      isDirty: true
    }));
  }, []);

  const setTaxRate = useCallback((rate: number) => {
    const safeRate = typeof rate === 'number' && !isNaN(rate) ? rate : 0.1;
    // Round tax rate to 2 decimal places
    const roundedRate = Math.round(safeRate * 100) / 100;
    setQuoteState(prev => ({
      ...prev,
      taxRate: roundedRate,
      isDirty: true
    }));
  }, []);

  // Quote specific controls
  const setQuoteDate = useCallback((date: string | null) => {
    setQuoteState(prev => ({
      ...prev,
      quoteDate: date,
      isDirty: true
    }));
  }, []);

  const setExpiryDate = useCallback((date: string | null) => {
    setQuoteState(prev => ({
      ...prev,
      expiryDate: date,
      isDirty: true
    }));
  }, []);

  // Notes
  const setNotes = useCallback((notes: string) => {
    setQuoteState(prev => ({
      ...prev,
      notes,
      isDirty: true
    }));
  }, []);

  const setNotesEnabled = useCallback((enabled: boolean) => {
    setQuoteState(prev => ({
      ...prev,
      notesEnabled: enabled,
      isDirty: true
    }));
  }, []);

  // Status management
  const setError = useCallback((error: string | null) => {
    setQuoteState(prev => ({
      ...prev,
      error
    }));
  }, []);

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    setQuoteState(prev => ({
      ...prev,
      isSubmitting
    }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setQuoteState(prev => ({
      ...prev,
      isLoading
    }));
  }, []);

  const markClean = useCallback(() => {
    setQuoteState(prev => ({
      ...prev,
      isDirty: false
    }));
  }, []);

  // Validation
  const validateQuote = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (quoteState.items.length === 0) {
      errors.push('Please add at least one item to the quote');
    }

    if (quoteState.total <= 0) {
      errors.push('Quote total must be greater than zero');
    }

    // Validate customer selection
    if (!quoteState.customer) {
      errors.push('Please select a customer for this quote');
    }

    // Validate individual items
    quoteState.items.forEach((item, index) => {
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than zero`);
      }
      if (item.unitPrice < 0) {
        errors.push(`Item ${index + 1}: Unit price cannot be negative`);
      }
    });

    // Validate quote date
    if (!quoteState.quoteDate) {
      errors.push('Please set a quote date');
    }

    // Validate expiry date
    if (!quoteState.expiryDate) {
      errors.push('Please set an expiry date');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [quoteState]);

  return {
    quoteState,
    startNewQuote,
    loadQuoteForEdit,
    loadQuoteForContinue,
    clearQuote,
    setCustomer,
    addItem,
    removeItem,
    updateItemQuantity,
    updateItemPrice,
    clearItems,
    setTaxEnabled,
    setTaxRate,
    recalculateTotals,
    setQuoteDate,
    setExpiryDate,
    setNotes,
    setNotesEnabled,
    setError,
    setSubmitting,
    setLoading,
    markClean,
    validateQuote
  };
} 