import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ProductsService } from "./products.service.typeorm";
import { Product } from "./product.entity";
import {
  ApiTags,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
} from "@nestjs/swagger";
import { UpdateProductDto } from "./dto/update-product.dto";
import { CreateProductDto } from "./dto/create-product.dto";
import { toProductDto, toProductDtoArray, ProductDto } from "./dto/product.dto";
import { PaginationQueryDto, PaginatedResponseDto } from "../dto/pagination.dto";
import { CustomerPricingResponseDto } from "./dto/customer-pricing.dto";
// Debug delay utility function
const debugDelay = (ms: number = 3000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

@ApiTags("products")
@Controller("products")
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({
    summary: "Create a new product",
    description: "Creates a new product with the provided data",
  })
  @ApiResponse({ status: 201, description: "Product created successfully" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  async create(@Body() createProductDto: CreateProductDto) {
    // Debug delay for testing
    await debugDelay();
    
    const product = await this.productsService.create(createProductDto);
    return toProductDto(product);
  }

  @Get()
  @ApiOperation({
    summary: "Get all products with pagination",
    description:
      "Returns a paginated list of products, excluding deleted ones. Now includes pack information by default.",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiQuery({
    name: "includePackInfo",
    type: Boolean,
    required: false,
    description: "Include pack information (isPack, isComponent, packInfo, componentCount). Defaults to true.",
    example: true,
  })
  @ApiResponse({ status: 200, description: "Paginated list of products with pack information." })
  async findAll(@Query() paginationQuery: PaginationQueryDto, @Query("includePackInfo") includePackInfo?: boolean) {
    // Debug delay for testing
    await debugDelay();
    
    const { page = 1, limit = 10 } = paginationQuery;
    
    // Use the updated findAll method that includes pack information
    const result = await this.productsService.findAll(
      page,
      limit,
      includePackInfo !== false // Default to true
    );
    
    return new PaginatedResponseDto(
      toProductDtoArray(result.data),
      result.total,
      result.page,
      result.limit,
    );
  }

  @Get("count")
  @ApiOperation({
    summary: "Get total count of products",
    description: "Returns the total count of non-deleted products",
  })
  @ApiResponse({ status: 200, description: "Product count" })
  async count() {
    // Debug delay for testing
    await debugDelay();
    
    const count = await this.productsService.count();
    return { count };
  }

  @Get("filter")
  @ApiOperation({
    summary: "Product filtering with multiple criteria",
    description:
      "Filter products by warehouse, name, product category UUID, SKU, barcode, price range, customer type, and search terms with pagination. This endpoint replaces the old /by-warehouse, /categories, /pricing, and /search endpoints. Now includes pack information by default.",
  })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: true,
    description: "UUIDv7 string of the warehouse",
  })
  @ApiQuery({
    name: "name",
    type: String,
    required: false,
    description: "Product name (partial, case-insensitive)",
  })
  @ApiQuery({
    name: "productCategoryUuid",
    type: String,
    required: false,
    description: "Product category UUID (exact match)",
  })
  @ApiQuery({
    name: "sku",
    type: String,
    required: false,
    description: "Product SKU (partial, case-insensitive)",
  })
  @ApiQuery({
    name: "barcode",
    type: String,
    required: false,
    description: "Product barcode (exact match)",
  })
  @ApiQuery({
    name: "search",
    type: String,
    required: false,
    description: "Search query for name, SKU, or barcode. Works in combination with other filters (AND logic)",
  })
  @ApiQuery({
    name: "customerType",
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
    required: false,
    description: "Customer type for pricing (replaces old /pricing endpoint)",
  })
  @ApiQuery({
    name: "minRetailPrice",
    type: Number,
    required: false,
    description: "Minimum retail price filter",
  })
  @ApiQuery({
    name: "maxRetailPrice",
    type: Number,
    required: false,
    description: "Maximum retail price filter",
  })
  @ApiQuery({
    name: "minWholesalePrice",
    type: Number,
    required: false,
    description: "Minimum wholesale price filter",
  })
  @ApiQuery({
    name: "maxWholesalePrice",
    type: Number,
    required: false,
    description: "Maximum wholesale price filter",
  })
  @ApiQuery({
    name: "minMidWholesalePrice",
    type: Number,
    required: false,
    description: "Minimum mid-wholesale price filter",
  })
  @ApiQuery({
    name: "maxMidWholesalePrice",
    type: Number,
    required: false,
    description: "Maximum mid-wholesale price filter",
  })
  @ApiQuery({
    name: "minInstitutionalPrice",
    type: Number,
    required: false,
    description: "Minimum institutional price filter",
  })
  @ApiQuery({
    name: "maxInstitutionalPrice",
    type: Number,
    required: false,
    description: "Maximum institutional price filter",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiQuery({
    name: "returnCategoriesOnly",
    type: Boolean,
    required: false,
    description: "If true, returns only unique category UUIDs (replaces old /categories endpoint)",
  })
  @ApiQuery({
    name: "includePackInfo",
    type: Boolean,
    required: false,
    description: "Include pack information (isPack, isComponent, packInfo, componentCount). Defaults to true.",
    example: true,
  })
  @ApiResponse({
    status: 200,
    description: "Paginated filtered list of products or categories with pack information.",
    schema: {
      type: "object",
      properties: {
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              warehouseUuidString: { type: "string" },
              productCategoryUuidString: { type: "string" },
              name: { type: "string" },
              description: { type: "string" },
              sku: { type: "string" },
              barcode: { type: "string" },
              retailPrice: { type: "number" },
              wholesalePrice: { type: "number" },
              midWholesalePrice: { type: "number" },
              institutionalPrice: { type: "number" },
              cost: { type: "number" },
              isDeleted: { type: "boolean" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
              isPack: { type: "boolean" },
              isComponent: { type: "boolean" },
              componentCount: { type: "number" },
              packInfo: {
                type: "object",
                properties: {
                  packUuid: { type: "string" },
                  packName: { type: "string" },
                  packSku: { type: "string" },
                  quantityInPack: { type: "number" },
                },
              },
            },
          },
        },
        total: { type: "number" },
        page: { type: "number" },
        limit: { type: "number" },
        hasNext: { type: "boolean" },
        hasPrev: { type: "boolean" },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description:
      "Bad request - invalid UUID format or missing required parameters",
  })
  async filter(
    @Query("warehouseUuid", ParseUUIDPipe) warehouseUuid: string,
    @Query("name") name?: string,
    @Query("productCategoryUuid") productCategoryUuid?: string,
    @Query("sku") sku?: string,
    @Query("barcode") barcode?: string,
    @Query("search") search?: string,
    @Query("customerType") customerType?: "retail" | "wholesale" | "mid-wholesale" | "institutional",
    @Query("minRetailPrice") minRetailPrice?: number,
    @Query("maxRetailPrice") maxRetailPrice?: number,
    @Query("minWholesalePrice") minWholesalePrice?: number,
    @Query("maxWholesalePrice") maxWholesalePrice?: number,
    @Query("minMidWholesalePrice") minMidWholesalePrice?: number,
    @Query("maxMidWholesalePrice") maxMidWholesalePrice?: number,
    @Query("minInstitutionalPrice") minInstitutionalPrice?: number,
    @Query("maxInstitutionalPrice") maxInstitutionalPrice?: number,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
    @Query("returnCategoriesOnly") returnCategoriesOnly?: boolean,
    @Query("includePackInfo") includePackInfo?: boolean,
  ) {
    // Debug delay for testing
    await debugDelay();
    
    if (!warehouseUuid) {
      throw new Error("warehouseUuid is required");
    }

    // Handle categories-only request (replaces old /categories endpoint)
    if (returnCategoriesOnly) {
      try {
        const categories = await this.productsService.getCategoriesByWarehouse(warehouseUuid);
        return { categories };
      } catch (error) {
        if (error instanceof Error && error.message.includes("Invalid")) {
          throw new Error(error.message);
        }
        throw error;
      }
    }

    const pageNum = page || 1;
    const limitNum = limit || 20;

    try {
      // Always use filterAdvanced for all filtering scenarios
      const result = await this.productsService.filterAdvanced(
        warehouseUuid,
        {
          name,
          productCategoryUuid,
          sku,
          barcode,
          search,
          minRetailPrice,
          maxRetailPrice,
          minWholesalePrice,
          maxWholesalePrice,
          minMidWholesalePrice,
          maxMidWholesalePrice,
          minInstitutionalPrice,
          maxInstitutionalPrice,
          includePackInfo: includePackInfo !== false, // Default to true
        },
        pageNum,
        limitNum,
      );

      // Apply customer pricing if requested (replaces old /pricing endpoint)
      let productsWithPricing = toProductDtoArray(result.data);
      if (customerType) {
        if (!["retail", "wholesale", "mid-wholesale", "institutional"].includes(customerType)) {
          throw new Error("Valid customerType is required: retail, wholesale, mid-wholesale, institutional");
        }

        productsWithPricing = productsWithPricing.map((product, index) => ({
          ...product,
          customerPrice: this.productsService.getPriceForCustomerType(
            result.data[index],
            customerType,
          ),
          customerType: customerType,
        }));
      }

      return {
        data: productsWithPricing,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    } catch (error) {
      // Re-throw validation errors as HTTP errors
      if (error instanceof Error && error.message.includes("Invalid")) {
        throw new Error(error.message);
      }
      throw error;
    }
  }

  @Get(":uuid/pack-info")
  @ApiOperation({
    summary: "Get pack information for a specific product",
    description: "Returns detailed pack information including whether the product is a pack, component, and related pack details",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product to get pack information for",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Pack information for the product",
    schema: {
      type: "object",
      properties: {
        isPack: { type: "boolean" },
        isComponent: { type: "boolean" },
        componentCount: { type: "number" },
        packInfo: {
          type: "object",
          properties: {
            packUuid: { type: "string" },
            packName: { type: "string" },
            packSku: { type: "string" },
            quantityInPack: { type: "number" },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Product not found" })
  async getPackInfo(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Debug delay for testing
    await debugDelay();
    
    const product = await this.productsService.findOne(uuid);
    const packInfo = await this.productsService.getPackInfo(uuid);
    
    return {
      product: toProductDto(product),
      ...packInfo,
    };
  }

  @Get(":uuid/pack-components")
  @ApiOperation({
    summary: "Get all components of a pack",
    description: "Returns all products that are components of the specified pack",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the pack to get components for",
  })
  @ApiResponse({ 
    status: 200, 
    description: "List of components in the pack",
    schema: {
      type: "object",
      properties: {
        packUuid: { type: "string" },
        packName: { type: "string" },
        components: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              componentProductUuid: { type: "string" },
              quantity: { type: "number" },
              componentProduct: { type: "object" },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Pack not found" })
  async getPackComponents(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Debug delay for testing
    await debugDelay();
    
    const product = await this.productsService.findOne(uuid);
    const components = await this.productsService.getPackComponents(uuid);
    
    return {
      packUuid: uuid,
      packName: product.name,
      components: components.map(comp => ({
        uuid: comp.id,
        componentProductUuid: comp.componentProductUuid,
        quantity: comp.quantity,
        componentProduct: toProductDto(comp.componentProduct),
      })),
    };
  }

  @Get(":uuid/product-packs")
  @ApiOperation({
    summary: "Get all packs that contain a specific product",
    description: "Returns all packs that include the specified product as a component",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product to find packs for",
  })
  @ApiResponse({ 
    status: 200, 
    description: "List of packs containing the product",
    schema: {
      type: "object",
      properties: {
        productUuid: { type: "string" },
        productName: { type: "string" },
        packs: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              assemblyProductUuid: { type: "string" },
              quantity: { type: "number" },
              assemblyProduct: { type: "object" },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Product not found" })
  async getProductPacks(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Debug delay for testing
    await debugDelay();
    
    const product = await this.productsService.findOne(uuid);
    const packs = await this.productsService.getProductPacks(uuid);
    
    return {
      productUuid: uuid,
      productName: product.name,
      packs: packs.map(pack => ({
        uuid: pack.id,
        assemblyProductUuid: pack.assemblyProductUuid,
        quantity: pack.quantity,
        assemblyProduct: toProductDto(pack.assemblyProduct),
      })),
    };
  }

  @Get(":uuid")
  @ApiOperation({
    summary: "Get a single product by UUID",
    description: "Returns a single product by its UUID with pack information included",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product to retrieve",
  })
  @ApiQuery({
    name: "includePackInfo",
    type: Boolean,
    required: false,
    description: "Include pack information (isPack, isComponent, packInfo, componentCount). Defaults to true.",
    example: true,
  })
  @ApiResponse({ status: 200, description: "Product found with pack information" })
  @ApiResponse({ status: 404, description: "Product not found" })
  async findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("includePackInfo") includePackInfo?: boolean
  ) {
    // Debug delay for testing
    await debugDelay();
    
    const product = await this.productsService.findOne(uuid);
    
    if (includePackInfo !== false) { // Default to true
      const packInfo = await this.productsService.getPackInfo(uuid);
      const productWithPackInfo = {
        ...product,
        ...packInfo,
      };
      return toProductDto(productWithPackInfo);
    }
    
    return toProductDto(product);
  }

  @Delete("all")
  @ApiOperation({
    summary: "Hard delete all products",
    description:
      "Removes all products from the database. This action is irreversible.",
  })
  @ApiResponse({
    status: 200,
    description:
      "All products have been permanently deleted from the database.",
  })
  async deleteAll() {
    // Debug delay for testing
    await debugDelay();
    
    const result = await this.productsService.deleteAll();
    return {
      message: "All products deleted",
      deletedCount: result.deletedCount,
    };
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete a product",
    description: "Marks a product as deleted without removing it from the database",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product to delete",
  })
  @ApiResponse({ status: 200, description: "Product soft deleted successfully" })
  @ApiResponse({ status: 404, description: "Product not found" })
  async softDelete(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Debug delay for testing
    await debugDelay();
    
    return this.productsService.softDelete(uuid);
  }

  @Put(":uuid")
  @ApiOperation({
    summary: "Update a product",
    description: "Update product details by UUID",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product to update",
  })
  @ApiBody({ type: UpdateProductDto })
  @ApiResponse({
    status: 200,
    description: "Product updated successfully",
    schema: {
      type: "object",
      properties: {
        uuid: {
          type: "string",
          example: "018f1234-5678-9abc-def0-123456789abc",
        },
        warehouseUuidString: {
          type: "string",
          example: "018f1234-5678-9abc-def0-123456789abc",
        },
        productCategoryUuidString: {
          type: "string",
          example: "018f1234-5678-9abc-def0-123456789abc",
        },
        name: { type: "string", example: "Product Name" },
        description: { type: "string", example: "Product description" },
        sku: { type: "string", example: "SKU123" },
        barcode: { type: "string", example: "1234567890123" },
        retailPrice: { type: "number", example: 29.99 },
        wholesalePrice: { type: "number", example: 26.99 },
        midWholesalePrice: { type: "number", example: 24.99 },
        institutionalPrice: { type: "number", example: 22.99 },
        cost: { type: "number", example: 20.0 },
        isDeleted: { type: "boolean", example: false },
        createdAt: { type: "string", format: "date-time" },
        updatedAt: { type: "string", format: "date-time" },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid data or UUID format",
  })
  @ApiResponse({ status: 404, description: "Product not found" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateProductDto: any, // allow extra fields for normalization
  ) {
    // Debug delay for testing
    await debugDelay();
    
    console.log('=== PRODUCT UPDATE REQUEST START ===');
    console.log('[Products Controller] Update request received');
    console.log('[Products Controller] UUID:', uuid);
    console.log('[Products Controller] Raw update data:', JSON.stringify(updateProductDto, null, 2));
    console.log('[Products Controller] Update data keys:', Object.keys(updateProductDto));
    console.log('[Products Controller] Price fields check:', {
      retailPrice: updateProductDto.retailPrice,
      wholesalePrice: updateProductDto.wholesalePrice,
      midWholesalePrice: updateProductDto.midWholesalePrice,
      institutionalPrice: updateProductDto.institutionalPrice,
      cost: updateProductDto.cost,
      price: updateProductDto.price
    });
    
    try {
      // Normalize: if only productCategoryUuidString is present, use it
      if (
        !updateProductDto.productCategoryUuid &&
        updateProductDto.productCategoryUuidString
      ) {
        updateProductDto.productCategoryUuid =
          updateProductDto.productCategoryUuidString;
      }
      
      console.log('[Products Controller] Normalized update data:', JSON.stringify(updateProductDto, null, 2));
      console.log('[Products Controller] About to call productsService.update');
      
      const product = await this.productsService.update(uuid, updateProductDto);
      
      console.log('[Products Controller] Service update completed successfully');
      console.log('[Products Controller] Updated product returned:', {
        id: product.id,
        name: product.name,
        retailPrice: product.retailPrice,
        wholesalePrice: product.wholesalePrice,
        midWholesalePrice: product.midWholesalePrice,
        institutionalPrice: product.institutionalPrice,
        cost: product.cost
      });
      
      const response = toProductDto(product);
      console.log('[Products Controller] Final response:', JSON.stringify(response, null, 2));
      console.log('=== PRODUCT UPDATE REQUEST END ===');
      
      return response;
    } catch (error) {
      console.error('=== PRODUCT UPDATE ERROR ===');
      console.error('[Products Controller] Update error:', error);
      console.error('[Products Controller] Error stack:', error.stack);
      console.error('=== PRODUCT UPDATE ERROR END ===');
      // Re-throw validation errors as HTTP errors
      if (error instanceof Error && error.message.includes("Invalid")) {
        throw new Error(error.message);
      }
      throw error;
    }
  }
}
