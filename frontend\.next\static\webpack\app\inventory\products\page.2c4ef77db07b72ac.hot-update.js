"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/products/page",{

/***/ "(app-pages-browser)/./app/inventory/products/PackComponents.tsx":
/*!***************************************************!*\
  !*** ./app/inventory/products/PackComponents.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PackComponents; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _productStructureApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./productStructureApi */ \"(app-pages-browser)/./app/inventory/products/productStructureApi.ts\");\n/* harmony import */ var _components_ProductModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProductModal */ \"(app-pages-browser)/./components/ProductModal/index.ts\");\n// PackComponents.tsx - Component for managing pack components within product form\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction PackComponents(param) {\n    let { warehouseUuid, packProductUuid, components, onComponentsChange, availableProducts, isLoadingProducts } = param;\n    _s();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const [selectedProductUuid, setSelectedProductUuid] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const [quantity, setQuantity] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(1);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const [isProductModalOpen, setIsProductModalOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    // Create product structure mutation\n    const createStructureMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: async (data)=>{\n            if (!packProductUuid) return null;\n            return (0,_productStructureApi__WEBPACK_IMPORTED_MODULE_2__.createProductStructure)({\n                warehouseUuid,\n                assemblyProductUuid: packProductUuid,\n                componentProductUuid: data.componentProductUuid,\n                quantity: data.quantity\n            });\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setError(null);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            setError((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to add component to pack\");\n        }\n    });\n    // Filter out products that are already in the pack\n    const availableProductsForPack = availableProducts.filter((product)=>!components.some((comp)=>comp.productUuid === product.uuid));\n    const handleAddComponent = ()=>{\n        if (!selectedProductUuid || quantity <= 0) {\n            setError(\"Please select a product and enter a valid quantity\");\n            return;\n        }\n        const selectedProduct = availableProducts.find((p)=>p.uuid === selectedProductUuid);\n        if (!selectedProduct) {\n            setError(\"Selected product not found\");\n            return;\n        }\n        const newComponent = {\n            productUuid: selectedProductUuid,\n            productName: selectedProduct.name,\n            productSku: selectedProduct.sku,\n            quantity: quantity\n        };\n        const updatedComponents = [\n            ...components,\n            newComponent\n        ];\n        onComponentsChange(updatedComponents);\n        // If we have a pack product UUID (editing mode), create the structure in backend\n        if (packProductUuid) {\n            createStructureMutation.mutate({\n                componentProductUuid: selectedProductUuid,\n                quantity: quantity\n            });\n        }\n        // Reset form\n        setSelectedProductUuid(\"\");\n        setQuantity(1);\n        setError(null);\n    };\n    const handleRemoveComponent = (index)=>{\n        const updatedComponents = components.filter((_, i)=>i !== index);\n        onComponentsChange(updatedComponents);\n    };\n    const handleQuantityChange = (index, newQuantity)=>{\n        if (newQuantity <= 0) return;\n        const updatedComponents = components.map((comp, i)=>i === index ? {\n                ...comp,\n                quantity: newQuantity\n            } : comp);\n        onComponentsChange(updatedComponents);\n    };\n    // Handle product selection from modal\n    const handleProductSelect = (product)=>{\n        if (product) {\n            // Check if product is already in the pack\n            const isAlreadySelected = components.some((comp)=>comp.productUuid === product.uuid);\n            if (isAlreadySelected) {\n                setError(\"This product is already in the pack\");\n                return;\n            }\n            setSelectedProductUuid(product.uuid);\n            setIsProductModalOpen(false);\n            setError(null);\n        }\n    };\n    // Handle creating new product from modal\n    const handleCreateNewProduct = (productData)=>{\n        // This will be handled by the parent component through the availableProducts\n        // The modal will close and the new product should appear in the list\n        setIsProductModalOpen(false);\n    // Note: The new product will be available in the next render cycle\n    // through the availableProducts prop from the parent component\n    };\n    // Get selected product display name\n    const getSelectedProductDisplay = ()=>{\n        if (!selectedProductUuid) return \"Select a product...\";\n        const selectedProduct = availableProducts.find((p)=>p.uuid === selectedProductUuid);\n        if (!selectedProduct) return \"Product not found...\";\n        return \"\".concat(selectedProduct.name).concat(selectedProduct.sku ? \" (\".concat(selectedProduct.sku, \")\") : \"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"Pack Components\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\n                            components.length,\n                            \" component\",\n                            components.length !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsProductModalOpen(true),\n                                                className: \"flex-1 text-sm border border-gray-300 rounded-md px-3 py-2 text-left bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                disabled: isLoadingProducts,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: selectedProductUuid ? \"text-gray-900\" : \"text-gray-500\",\n                                                            children: getSelectedProductDisplay()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedProductUuid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setSelectedProductUuid(\"\"),\n                                                className: \"text-gray-400 hover:text-gray-600 p-2\",\n                                                title: \"Clear selection\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"Quantity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"1\",\n                                        value: quantity,\n                                        onChange: (e)=>setQuantity(parseInt(e.target.value) || 1),\n                                        className: \"w-full text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-400 focus:border-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleAddComponent,\n                        disabled: !selectedProductUuid || quantity <= 0 || isLoadingProducts,\n                        className: \"w-full bg-blue-600 text-white text-sm font-medium rounded-md py-2 px-3 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Add to Pack\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-red-600 bg-red-50 p-2 rounded\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModal__WEBPACK_IMPORTED_MODULE_3__.ProductModal, {\n                isOpen: isProductModalOpen,\n                onSelect: handleProductSelect,\n                onClose: ()=>setIsProductModalOpen(false),\n                onCreateNew: handleCreateNewProduct,\n                disabled: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            components.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xs font-medium text-gray-700\",\n                        children: \"Pack Contents:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: components.map((component, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between bg-white border border-gray-200 rounded-md p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: component.productName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            component.productSku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"SKU: \",\n                                                    component.productSku\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: component.quantity,\n                                                onChange: (e)=>handleQuantityChange(index, parseInt(e.target.value) || 1),\n                                                className: \"w-16 text-sm border border-gray-300 rounded px-2 py-1 text-center focus:ring-2 focus:ring-blue-400 focus:border-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleRemoveComponent(index),\n                                                className: \"text-red-600 hover:text-red-800 p-1\",\n                                                title: \"Remove from pack\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, component.productUuid, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this),\n            availableProductsForPack.length === 0 && !isLoadingProducts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 text-center py-4\",\n                children: \"No available products to add to pack\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(PackComponents, \"ttZe12LK8uM3120DL96/HltTGiE=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation\n    ];\n});\n_c = PackComponents;\nvar _c;\n$RefreshReg$(_c, \"PackComponents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/inventory/products/PackComponents.tsx\n"));

/***/ })

});