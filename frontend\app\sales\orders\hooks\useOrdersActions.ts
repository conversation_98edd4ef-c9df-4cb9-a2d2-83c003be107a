import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { getOrder, cancelOrder, Order } from '../ordersApi';
import { getCompaniesByUser } from '../../../settings/companiesApi';
import { getCustomerByUuid } from '../../customers/customersApi';
import { updateUrlParams } from '../ordersHelpers';

// Interface definitions for invoice printing
interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;
  rc: string;
  articleNumber: string;
  address: string;
  phoneNumber: string;
  website?: string;
}

interface CustomerInfo {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export const useOrdersActions = (refetchOrders?: () => void) => {
  const { user } = useAuth();
  
  // Modal states
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [orderToCancel, setOrderToCancel] = useState<Order | null>(null);
  
  // Edit order data for POS mode
  const [editOrderData, setEditOrderData] = useState<Order | null>(null);
  
  // Loading states
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isLoadingEditOrder, setIsLoadingEditOrder] = useState(false);
  const [isLoadingViewDetails, setIsLoadingViewDetails] = useState(false);
  const [isLoadingPrintInvoice, setIsLoadingPrintInvoice] = useState(false);
  const [isLoadingCancel, setIsLoadingCancel] = useState(false);
  
  // Invoice data
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);

  // View order details - with loading state
  const handleViewDetails = async (order: Order) => {
    
    setIsLoadingViewDetails(true);
    try {
      
      const detailedOrder = await getOrder(order.uuid);
      
      setSelectedOrder(detailedOrder);
      setIsDetailsModalOpen(true);
      toast.success('Order details loaded');
      
    } catch (error) {
      toast.error('Failed to load order details');
    } finally {
      setIsLoadingViewDetails(false);
    }
  };

  // Edit order - fetch data and switch to POS mode within orders page
  const handleEdit = async (order: Order) => {
    
    setIsLoadingEditOrder(true);
    try {
      // Fetch the complete order data first
      const detailedOrder = await getOrder(order.uuid);
      
      // Store the fetched data for POS view
      setEditOrderData(detailedOrder);
      
      // Update URL parameters to switch to POS view
      updateUrlParams({ 
        view: 'pos',
        edit: order.uuid
      });
      
      toast.success('Order loaded for editing');
      
    } catch (error) {
      toast.error('Failed to load order for editing');
    } finally {
      setIsLoadingEditOrder(false);
    }
  };

  // Clear edit order data
  const clearEditOrderData = () => {
    setEditOrderData(null);
  };

  // Print invoice
  const handlePrintInvoice = async (order: Order) => {
    if (!user?.uuid) {
      toast.error('User information missing');
      return;
    }

    setIsLoadingPrintInvoice(true);
    try {
      
      // Fetch the complete order data to ensure we have all tax information
      const detailedOrder = await getOrder(order.uuid);
      
      // Fetch company information
      
      const companies = await getCompaniesByUser(user.uuid);
      if (companies.length === 0) {
        toast.error('No company information found for this user');
        return;
      }
      const company = companies[0];
      
      // Transform company data to match CompanyInfo interface
      const companyInfo: CompanyInfo = {
        uuid: company.uuid,
        name: company.name,
        nif: company.nif,
        rc: company.rc,
        articleNumber: company.articleNumber,
        address: company.address,
        phoneNumber: company.phoneNumber,
        website: company.website
      };
      
      // Fetch customer information
      
      const customer = await getCustomerByUuid(detailedOrder.customerUuidString);
      
      // Transform customer data to match CustomerInfo interface
      const customerInfo: CustomerInfo = {
        uuid: customer.uuid,
        name: customer.name,
        fiscalId: customer.fiscalId || 'N/A',
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        rc: customer.rc,
        articleNumber: customer.articleNumber,
        customerType: customer.customerType
      };
      
      setCompanyInfo(companyInfo);
      setCustomerInfo(customerInfo);
      setSelectedOrder(detailedOrder);
      setIsInvoiceModalOpen(true);
      
    } catch (error: any) {
      if (error.response?.status === 401) {
        toast.error('Authentication error. Please log in again.');
      } else {
        toast.error('Error loading invoice data');
      }
    } finally {
      setIsLoadingPrintInvoice(false);
    }
  };

  // Cancel order - with loading state
  const handleCancel = async (order: Order) => {
    if (order.status === 'cancelled') {
      toast.error('Order is already cancelled');
      return;
    }
    
    
    setIsLoadingCancel(true);
    try {
      // Set the order to cancel and open modal
    setOrderToCancel(order);
    setIsCancelModalOpen(true);
      
    } catch (error) {
      toast.error('Failed to open cancel dialog');
    } finally {
      setIsLoadingCancel(false);
    }
  };

  const handleConfirmCancel = async () => {
    if (!orderToCancel || !user?.uuid) return;

    setIsCancelling(true);
    try {
      await cancelOrder(orderToCancel.uuid, user.uuid);
      toast.success('Order cancelled successfully');
      if (refetchOrders) {
        refetchOrders();
      }
      setIsCancelModalOpen(false);
      setOrderToCancel(null);
    } catch (error) {
      toast.error('Failed to cancel order');
    } finally {
      setIsCancelling(false);
    }
  };

  const handleCancelModalClose = () => {
    setIsCancelModalOpen(false);
    setOrderToCancel(null);
  };

  // Close modals
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedOrder(null);
  };

  const closeInvoiceModal = () => {
    setIsInvoiceModalOpen(false);
    setSelectedOrder(null);
    setCompanyInfo(null);
    setCustomerInfo(null);
  };

  return {
    // State
    selectedOrder,
    isDetailsModalOpen,
    isInvoiceModalOpen,
    isCancelModalOpen,
    orderToCancel,
    editOrderData,
    isLoadingInvoice,
    isCancelling,
    companyInfo,
    customerInfo,
    isLoadingEditOrder,
    isLoadingViewDetails,
    isLoadingPrintInvoice,
    isLoadingCancel,
    
    // Actions
    handleViewDetails,
    handleEdit,
    clearEditOrderData,
    handlePrintInvoice,
    handleCancel,
    handleConfirmCancel,
    handleCancelModalClose,
    closeDetailsModal,
    closeInvoiceModal
  };
}; 