import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeUserUuidNullable1753941931316 implements MigrationInterface {
    name = 'MakeUserUuidNullable1753941931316'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "userUuid" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "userUuid" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
    }

}
