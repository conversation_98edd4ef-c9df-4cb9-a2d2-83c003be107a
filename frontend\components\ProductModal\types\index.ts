// Global Product Modal type definitions

export interface Product {
  uuid: string;
  warehouseUuid: string;
  warehouseUuidString?: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  productCategoryUuid?: string;
  productCategoryUuidString?: string;
  price?: number;
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  cost?: number;
  isPack?: boolean;
  isComponent?: boolean;
  componentCount?: number;
  packInfo?: {
    packUuid?: string;
    packName?: string;
    packSku?: string;
    quantityInPack?: number;
  };
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateProductDto {
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  productCategoryUuid?: string;
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  cost?: number;
  warehouseUuid?: string;
}

export interface ProductFilter {
  name?: string;
  sku?: string;
  barcode?: string;
  search?: string;
  productCategoryUuid?: string;
  warehouseUuid?: string;
  isDeleted?: boolean;
}

export interface ProductModalProps {
  isOpen: boolean;
  products?: Product[]; // Keep for backward compatibility but won't use
  onSelect: (product: Product | null) => void;
  onClose: () => void;
  onCreateNew: (product: CreateProductDto) => void;
  disabled?: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  totalPages: number;
} 