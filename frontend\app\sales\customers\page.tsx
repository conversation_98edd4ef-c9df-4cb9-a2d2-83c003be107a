"use client";

import React, { useEffect, useMemo, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCustomers } from "./useCustomers";
import { Customer, CreateCustomerDto, UpdateCustomerDto } from "./customersApi";
import ItemsTable, { ItemsTableColumn } from "@/components/itemsTable/ItemsTable";
import { CustomerModal } from "@/components/CustomerModal";
import CustomerEditModal from "./CustomerEditModal";
import CustomerDetailsModal from "./CustomerDetailsModal";
import { toast } from "sonner";
import { buildPOSUrl } from "../sales/pos/config/posConfig";

// Import extracted hooks and components
import { useCustomerFilters, useCustomerActions } from "./hooks";
import { SearchAndFilters, CustomerTableActions } from "./components";


export default function CustomersPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  // Custom hooks
  const filters = useCustomerFilters(warehouseUuid || undefined);
  const actions = useCustomerActions();

  // Fetch customers with enhanced filtering and sorting
  const { 
    data: customersResponse, 
    isLoading, 
    error: fetchError 
  } = useCustomers(
    { page: filters.currentPage, limit: filters.pageSize },
    filters.filter
  );

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F2') {
        e.preventDefault();
        actions.handleAddCustomer();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [actions]);

  // Event handlers
  const handleCreateSaleForCustomer = useCallback((customer: Customer) => {
    window.location.href = buildPOSUrl(undefined, customer.uuid);
  }, []);

  // Format currency
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  }, []);

  // Table columns
  const columns: ItemsTableColumn<Customer>[] = useMemo(() => [
    {
      key: 'name',
      header: 'Name',
      cellClassName: 'text-left font-semibold',
      headerClassName: 'text-left',
    },
    {
      key: 'customerType',
      header: 'Type',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => (
        <span className="capitalize px-2 py-1 bg-gray-100 rounded text-xs font-medium">
          {value.replace('-', ' ')}
        </span>
      ),
    },
    {
      key: 'currentCredit',
      header: 'Credit Balance',
      cellClassName: 'text-right font-semibold',
      headerClassName: 'text-right',
      render: (value: number) => {
        const colorClass = value > 0 
          ? 'text-green-600' 
          : value < 0 
            ? 'text-red-600' 
            : 'text-gray-600';

        return (
          <span className={`${colorClass} font-semibold text-sm`}>
            {formatCurrency(value)}
          </span>
        );
      },
    },
    {
      key: 'actions',
      header: 'Actions',
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
      render: (_: any, customer: Customer) => (
        <CustomerTableActions
          customer={customer}
          onViewDetails={actions.handleViewCustomerDetails}
          onCreateSale={handleCreateSaleForCustomer}
          onEdit={actions.handleEditCustomer}
          onDelete={actions.handleDeleteCustomer}
          isUpdating={actions.isLoading}
          isDeleting={actions.isLoading}
        />
      ),
    },
  ], [
    formatCurrency,
    actions.handleViewCustomerDetails,
    handleCreateSaleForCustomer,
    actions.handleEditCustomer,
    actions.handleDeleteCustomer,
    actions.isLoading,
  ]);

  // Data - extract from nested meta structure
  const customers = customersResponse?.data || [];
  const totalPages = customersResponse?.meta?.totalPages || Math.ceil(customers.length / filters.pageSize) || 1;
  const totalCustomers = customersResponse?.meta?.total || customers.length || 0;

        // Error handling
      if (fetchError) {
        return (
          <div className="p-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-xl font-semibold text-red-800">Error Loading Customers</h3>
              <p className="text-sm text-red-600 mt-1">
                {fetchError instanceof Error ? fetchError.message : 'An unexpected error occurred'}
              </p>
            </div>
          </div>
        );
      }

  return (
    <div className="p-6">

      
      {/* Search and Filters */}
      <SearchAndFilters
        filters={filters}
        onAddCustomer={actions.handleAddCustomer}
      />

      {/* Loading Indicator */}
      {isLoading && (
        <div className="mb-4 flex items-center justify-end">
          <div className="text-sm text-blue-600 flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            Loading...
          </div>
        </div>
      )}

      {/* Table */}
      <ItemsTable
        columns={columns}
        data={customers}
        noDataText={
          <div className="text-center py-8">
            <span className="text-gray-400">
              {filters.hasActiveFilters ? 'No customers match your filters.' : 'No customers found.'}
            </span>
            {filters.hasActiveFilters && (
              <button
                onClick={filters.clearAllFilters}
                className="ml-2 text-blue-600 hover:text-blue-700 underline"
              >
                Clear filters
              </button>
            )}
          </div>
        }
        containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
        pagination={{
          currentPage: filters.currentPage,
          totalPages: totalPages,
          onPageChange: filters.setCurrentPage,
          totalItems: totalCustomers,
          itemsPerPage: filters.pageSize,
        }}
      />

      {/* Modals */}
      <CustomerModal
        isOpen={actions.isAddModalOpen}
        onClose={actions.closeAddModal}
        onSelect={() => {}} // Not used for creation
        onCreateNew={actions.handleSubmitCustomer}
        disabled={actions.isLoading}
      />

      <CustomerEditModal
        isOpen={actions.isEditModalOpen}
        onClose={actions.closeEditModal}
        onSubmit={actions.handleSubmitCustomer}
        customer={actions.selectedCustomer}
        error={null}
        isLoading={actions.isLoading}
      />

      <CustomerDetailsModal
        isOpen={actions.isDetailsModalOpen}
        onClose={actions.closeDetailsModal}
        customer={actions.selectedCustomer}
        onEdit={actions.selectedCustomer ? () => actions.handleEditCustomer(actions.selectedCustomer!) : undefined}
        onNewSale={actions.selectedCustomer ? () => handleCreateSaleForCustomer(actions.selectedCustomer!) : undefined}
      />
    </div>
  );
}
