// Product Modal Styles
// Reusing the same structure as CustomerModal for consistency

export const productModalStyles = {
  modal: {
    overlay: `
      fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4
    `,
    content: `
      bg-white rounded-lg shadow-lg max-w-lg w-full mx-4 h-[600px] overflow-hidden flex flex-col
    `,
    header: `
      p-4 border-b border-gray-200 flex-shrink-0 flex items-center justify-between
    `,
    title: `
      text-lg font-bold text-gray-900
    `,
    subtitle: `
      text-xs text-gray-600 mt-1
    `,
    closeButton: `
      p-1 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors
    `,
    body: `
      flex-1 flex flex-col min-h-0
    `,
    contentArea: `
      flex-1 overflow-y-auto p-4
    `,
    searchContainer: `
      mb-5
    `,
    searchInput: `
      w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm
    `,
    searchInputDisabled: `
      opacity-50 cursor-not-allowed
    `,
    searchIcon: `
      absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4
    `,
    loadingIcon: `
      absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin
    `,
    errorContainer: `
      mb-4 p-3 bg-red-50 border border-red-200 rounded-md
    `,
    errorText: `
      text-red-700 text-sm
    `,
    loadingContainer: `
      flex items-center justify-center py-8
    `,
    loadingText: `
      ml-2 text-gray-600
    `,
    productList: `
      space-y-2
    `,
    productItem: `
      p-3 border border-gray-200 rounded-lg transition-all duration-200 cursor-pointer hover:bg-gray-50 hover:border-blue-300
    `,
    productItemDisabled: `
      opacity-50 cursor-not-allowed
    `,
    productName: `
      font-medium text-gray-900 text-sm
    `,
    productDetails: `
      text-xs text-gray-600 mt-1
    `,
    noResultsContainer: `
      text-center py-8
    `,
    noResultsText: `
      text-gray-500 text-sm
    `,
    createNewButton: `
      mt-2 text-sm font-medium text-blue-600 hover:text-blue-800
    `,
    createNewButtonDisabled: `
      text-gray-400 cursor-not-allowed
    `,
    paginationContainer: `
      flex items-center justify-between mt-4 pt-4 border-t border-gray-200
    `,
    paginationButton: `
      flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed
    `,
    paginationText: `
      text-sm text-gray-600
    `,
    formContainer: `
      space-y-4
    `,
    formGroup: `
      block text-sm font-medium text-gray-700 mb-1
    `,
    formInput: `
      w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm
    `,
    formInputDisabled: `
      opacity-50 cursor-not-allowed
    `,
    formSelect: `
      w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm
    `,
    formSelectDisabled: `
      opacity-50 cursor-not-allowed
    `,
    validationErrors: `
      p-3 bg-red-50 border border-red-200 rounded-md
    `,
    validationErrorText: `
      text-red-700 text-sm
    `,
    formButtons: `
      flex space-x-3
    `,
    cancelButton: `
      flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed
    `,
    createButton: `
      flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed
    `,
    bottomSection: `
      flex-shrink-0 p-4 border-t border-gray-200
    `,
    createNewProductButton: `
      w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed
    `
  }
}; 