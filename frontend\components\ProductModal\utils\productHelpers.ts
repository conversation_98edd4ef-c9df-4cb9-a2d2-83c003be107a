import type { CreateProductDto, FormValidationResult } from '../types';

export function validateProduct(productData: CreateProductDto): FormValidationResult {
  const errors: string[] = [];

  // Validate name (required)
  if (!productData.name || productData.name.trim().length === 0) {
    errors.push('Product name is required');
  } else if (productData.name.trim().length < 2) {
    errors.push('Product name must be at least 2 characters long');
  }

  // Validate SKU (optional but if provided, must be valid)
  if (productData.sku && productData.sku.trim().length === 0) {
    errors.push('SKU cannot be empty if provided');
  }

  // Validate barcode (optional but if provided, must be valid)
  if (productData.barcode && productData.barcode.trim().length === 0) {
    errors.push('Barcode cannot be empty if provided');
  }

  // Validate prices (optional but if provided, must be positive)
  if (productData.retailPrice !== undefined && productData.retailPrice < 0) {
    errors.push('Retail price cannot be negative');
  }
  if (productData.wholesalePrice !== undefined && productData.wholesalePrice < 0) {
    errors.push('Wholesale price cannot be negative');
  }
  if (productData.midWholesalePrice !== undefined && productData.midWholesalePrice < 0) {
    errors.push('Mid-wholesale price cannot be negative');
  }
  if (productData.institutionalPrice !== undefined && productData.institutionalPrice < 0) {
    errors.push('Institutional price cannot be negative');
  }
  if (productData.cost !== undefined && productData.cost < 0) {
    errors.push('Cost cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function formatProductDisplayName(product: any): string {
  const parts = [product.name];
  
  if (product.sku) {
    parts.push(`SKU: ${product.sku}`);
  }
  
  if (product.barcode) {
    parts.push(`Barcode: ${product.barcode}`);
  }
  
  return parts.join(' • ');
}

export function formatProductPrice(product: any): string {
  if (product.retailPrice !== undefined) {
    return `$${product.retailPrice.toFixed(2)}`;
  }
  if (product.price !== undefined) {
    return `$${product.price.toFixed(2)}`;
  }
  return 'No price set';
} 