// PackComponents.tsx - Component for managing pack components within product form
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Product } from './productsApi';
import { createProductStructure, ProductStructure } from './productStructureApi';
import { ProductModal } from '@/components/ProductModal';

interface PackComponent {
  productUuid: string;
  productName: string;
  productSku?: string;
  quantity: number;
}

interface PackComponentsProps {
  warehouseUuid: string;
  packProductUuid?: string; // Only set when editing an existing pack
  components: PackComponent[];
  onComponentsChange: (components: PackComponent[]) => void;
  availableProducts: Product[];
  isLoadingProducts: boolean;
}

export default function PackComponents({ 
  warehouseUuid, 
  packProductUuid, 
  components, 
  onComponentsChange, 
  availableProducts, 
  isLoadingProducts 
}: PackComponentsProps) {
  const queryClient = useQueryClient();
  const [selectedProductUuid, setSelectedProductUuid] = React.useState('');
  const [quantity, setQuantity] = React.useState(1);
  const [error, setError] = React.useState<string | null>(null);
  const [isProductModalOpen, setIsProductModalOpen] = React.useState(false);

  // Create product structure mutation
  const createStructureMutation = useMutation({
    mutationFn: async (data: { componentProductUuid: string; quantity: number }) => {
      if (!packProductUuid) return null;
      return createProductStructure({
        warehouseUuid,
        assemblyProductUuid: packProductUuid,
        componentProductUuid: data.componentProductUuid,
        quantity: data.quantity,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      setError(null);
    },
    onError: (error: any) => {
      setError(error?.response?.data?.message || 'Failed to add component to pack');
    },
  });

  // Filter out products that are already in the pack
  const availableProductsForPack = availableProducts.filter(product => 
    !components.some(comp => comp.productUuid === product.uuid)
  );

  const handleAddComponent = () => {
    if (!selectedProductUuid || quantity <= 0) {
      setError('Please select a product and enter a valid quantity');
      return;
    }

    const selectedProduct = availableProducts.find(p => p.uuid === selectedProductUuid);
    if (!selectedProduct) {
      setError('Selected product not found');
      return;
    }

    const newComponent: PackComponent = {
      productUuid: selectedProductUuid,
      productName: selectedProduct.name,
      productSku: selectedProduct.sku,
      quantity: quantity,
    };

    const updatedComponents = [...components, newComponent];
    onComponentsChange(updatedComponents);

    // If we have a pack product UUID (editing mode), create the structure in backend
    if (packProductUuid) {
      createStructureMutation.mutate({
        componentProductUuid: selectedProductUuid,
        quantity: quantity,
      });
    }

    // Reset form
    setSelectedProductUuid('');
    setQuantity(1);
    setError(null);
  };

  const handleRemoveComponent = (index: number) => {
    const updatedComponents = components.filter((_, i) => i !== index);
    onComponentsChange(updatedComponents);
  };

  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) return;
    
    const updatedComponents = components.map((comp, i) => 
      i === index ? { ...comp, quantity: newQuantity } : comp
    );
    onComponentsChange(updatedComponents);
  };

  // Handle product selection from modal
  const handleProductSelect = (product: Product | null) => {
    if (product) {
      // Check if product is already in the pack
      const isAlreadySelected = components.some(comp => comp.productUuid === product.uuid);
      if (isAlreadySelected) {
        setError('This product is already in the pack');
        return;
      }
      
      setSelectedProductUuid(product.uuid);
      setIsProductModalOpen(false);
      setError(null);
    }
  };

  // Handle creating new product from modal
  const handleCreateNewProduct = (productData: any) => {
    // This will be handled by the parent component through the availableProducts
    // The modal will close and the new product should appear in the list
    setIsProductModalOpen(false);
    // Note: The new product will be available in the next render cycle
    // through the availableProducts prop from the parent component
  };

  // Get selected product display name
  const getSelectedProductDisplay = () => {
    if (!selectedProductUuid) return 'Select a product...';
    
    const selectedProduct = availableProducts.find(p => p.uuid === selectedProductUuid);
    if (!selectedProduct) return 'Product not found...';
    
    return `${selectedProduct.name}${selectedProduct.sku ? ` (${selectedProduct.sku})` : ''}`;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">Pack Components</h3>
        <span className="text-xs text-gray-500">
          {components.length} component{components.length !== 1 ? 's' : ''}
        </span>
      </div>

      {/* Add new component form */}
      <div className="bg-gray-50 rounded-lg p-4 space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Product
            </label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setIsProductModalOpen(true)}
                className="flex-1 text-sm border border-gray-300 rounded-md px-3 py-2 text-left bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoadingProducts}
              >
                <div className="flex items-center justify-between">
                  <span className={selectedProductUuid ? 'text-gray-900' : 'text-gray-500'}>
                    {getSelectedProductDisplay()}
                  </span>
                  <svg className="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              {selectedProductUuid && (
                <button
                  type="button"
                  onClick={() => setSelectedProductUuid('')}
                  className="text-gray-400 hover:text-gray-600 p-2"
                  title="Clear selection"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Quantity
            </label>
            <input
              type="number"
              min="1"
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
              className="w-full text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-400 focus:border-blue-400"
            />
          </div>
        </div>
        
        <button
          type="button"
          onClick={handleAddComponent}
          disabled={!selectedProductUuid || quantity <= 0 || isLoadingProducts}
          className="w-full bg-blue-600 text-white text-sm font-medium rounded-md py-2 px-3 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Add to Pack
        </button>
        
        {error && (
          <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
            {error}
          </div>
        )}
      </div>

      {/* Product Modal */}
      <ProductModal
        isOpen={isProductModalOpen}
        onSelect={handleProductSelect}
        onClose={() => setIsProductModalOpen(false)}
        onCreateNew={handleCreateNewProduct}
        disabled={false}
      />

      {/* Components list */}
      {components.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-gray-700">Pack Contents:</h4>
          <div className="space-y-2">
            {components.map((component, index) => (
              <div key={component.productUuid} className="flex items-center justify-between bg-white border border-gray-200 rounded-md p-3">
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {component.productName}
                  </div>
                  {component.productSku && (
                    <div className="text-xs text-gray-500">
                      SKU: {component.productSku}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 ml-3">
                  <input
                    type="number"
                    min="1"
                    value={component.quantity}
                    onChange={(e) => handleQuantityChange(index, parseInt(e.target.value) || 1)}
                    className="w-16 text-sm border border-gray-300 rounded px-2 py-1 text-center focus:ring-2 focus:ring-blue-400 focus:border-blue-400"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveComponent(index)}
                    className="text-red-600 hover:text-red-800 p-1"
                    title="Remove from pack"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {availableProductsForPack.length === 0 && !isLoadingProducts && (
        <div className="text-xs text-gray-500 text-center py-4">
          No available products to add to pack
        </div>
      )}
    </div>
  );
} 