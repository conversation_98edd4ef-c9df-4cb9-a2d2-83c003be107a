import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsInt, Min, IsOptional } from 'class-validator';

export class UpdateProductStructureDto {
  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the warehouse this structure belongs to",
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID('7') // Note: Requires custom validator for UUIDv7
  warehouseUuid?: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the assembly product (pack/parent product)",
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID('7') // Note: Requires custom validator for UUIDv7
  assemblyProductUuid?: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the component product (child product)",
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID('7') // Note: Requires custom validator for UUIDv7
  componentProductUuid?: string;

  @ApiProperty({
    example: 5,
    description: "Quantity of the component product required in the assembly",
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  quantity?: number;

}
