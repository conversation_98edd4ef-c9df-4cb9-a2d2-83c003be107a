// create.tsx - Page for creating a new product
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ProductForm, { ProductFormValues, PackComponent } from './ProductForm';
import { useAuth } from '@/contexts/AuthContext';
import { createProduct } from './productsApi';
import { createProductStructure } from './productStructureApi';

export default function CreateProductPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid || "";
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);


  const handleSubmit = async (values: ProductFormValues, packComponents?: PackComponent[]) => {
    setLoading(true);
    setError(null);
    try {
      console.log('[Create Product] Form values received:', values);
      console.log('[Create Product] Pack components:', packComponents);
      
      // Determine if we're using simple or custom pricing
      const isUsingSimplePricing = !values.retailPrice || values.retailPrice === '' || values.retailPrice === '0';
      
      let createData: any = {
        ...values,
        cost: values.cost && values.cost !== '' ? Number(values.cost) : undefined,
        warehouseUuid
      };
      
      if (isUsingSimplePricing) {
        // Simple pricing: send the same price value for all price fields
        console.log('[Create Product] Using simple pricing');
        const simplePrice = values.price && values.price !== '' ? Number(values.price) : undefined;
        createData = {
          ...createData,
          price: simplePrice,
          // Set all price fields to the same value for simple pricing
          retailPrice: simplePrice,
          wholesalePrice: simplePrice,
          midWholesalePrice: simplePrice,
          institutionalPrice: simplePrice,
        };
      } else {
        // Custom pricing: send all individual price fields
        console.log('[Create Product] Using custom pricing');
        createData = {
          ...createData,
          retailPrice: values.retailPrice && values.retailPrice !== '' ? Number(values.retailPrice) : undefined,
          wholesalePrice: values.wholesalePrice && values.wholesalePrice !== '' ? Number(values.wholesalePrice) : undefined,
          midWholesalePrice: values.midWholesalePrice && values.midWholesalePrice !== '' ? Number(values.midWholesalePrice) : undefined,
          institutionalPrice: values.institutionalPrice && values.institutionalPrice !== '' ? Number(values.institutionalPrice) : undefined,
        };
      }
      
      console.log('[Create Product] Processed create data:', createData);
      const createdProduct = await createProduct(createData);
      
      // If this is a pack and has components, create the product structures
      if (values.isPack && packComponents && packComponents.length > 0) {
        console.log('[Create Product] Creating pack structures for:', packComponents);
        for (const component of packComponents) {
          await createProductStructure({
            warehouseUuid,
            assemblyProductUuid: createdProduct.uuid,
            componentProductUuid: component.productUuid,
            quantity: component.quantity,
          });
        }
      }
      
      router.push('/inventory/products');
    } catch (err: any) {
      console.error('[Create Product] Create error:', err);
      setError(err?.response?.data?.message || 'Failed to create product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Create Product</h1>
      {error && <div className="mb-4 text-red-600">{error}</div>}
      <ProductForm initialValues={{}} onSubmit={handleSubmit} submitLabel="Create" loading={loading} warehouseUuid={warehouseUuid} />
    </div>
  );
}
