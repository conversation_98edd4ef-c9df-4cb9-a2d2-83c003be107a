import { MigrationInterface, QueryRunner } from "typeorm";

export class ProductStructureUpdate1753947929323 implements MigrationInterface {
    name = 'ProductStructureUpdate1753947929323'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
    }

}
