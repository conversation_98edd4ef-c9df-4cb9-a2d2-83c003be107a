// Product API functions for the global Product Modal

import { getAuthHeadersWithContentType } from '@/utils/authHeaders';
import type { Product, CreateProductDto, ProductFilter, PaginatedResponse } from '../types';

// Export types for external use
export type { ProductFilter, Product, CreateProductDto, PaginatedResponse };

// Filter products with pagination
export async function filterProducts(filter: ProductFilter): Promise<PaginatedResponse<Product>> {
  try {
    const response = await fetch('/api/products/filter', {
      method: 'GET',
      headers: getAuthHeadersWithContentType(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return {
      data: data.data || [],
      total: data.total || 0,
      page: data.page || 1,
      limit: data.limit || 20,
      hasNext: data.hasNext || false,
      hasPrev: data.hasPrev || false,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / (data.limit || 20))
    };
  } catch (error: any) {
    console.error('[ProductModal API] filterProducts error:', error);
    throw error;
  }
}

// Search products by name, SKU, or barcode
export async function searchProducts(warehouseUuid: string, searchTerm: string): Promise<PaginatedResponse<Product>> {
  try {
    const params = new URLSearchParams({
      warehouseUuid,
      search: searchTerm
    });
    
    const response = await fetch(`/api/products/filter?${params.toString()}`, {
      method: 'GET',
      headers: getAuthHeadersWithContentType(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return {
      data: data.data || [],
      total: data.total || 0,
      page: data.page || 1,
      limit: data.limit || 20,
      hasNext: data.hasNext || false,
      hasPrev: data.hasPrev || false,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / (data.limit || 20))
    };
  } catch (error: any) {
    console.error('[ProductModal API] searchProducts error:', error);
    throw error;
  }
}

// Create a new product
export async function createProduct(productData: CreateProductDto): Promise<Product> {
  try {
    const response = await fetch('/api/products', {
      method: 'POST',
      headers: getAuthHeadersWithContentType(),
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return data;
  } catch (error: any) {
    console.error('[ProductModal API] createProduct error:', error);
    throw error;
  }
}

// Get product by UUID
export async function getProduct(uuid: string): Promise<Product> {
  try {
    const response = await fetch(`/api/products/${uuid}`, {
      method: 'GET',
      headers: getAuthHeadersWithContentType(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return data;
  } catch (error: any) {
    console.error('[ProductModal API] getProduct error:', error);
    throw error;
  }
} 