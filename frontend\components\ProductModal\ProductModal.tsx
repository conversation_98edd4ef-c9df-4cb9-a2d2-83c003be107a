import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { FiSearch, FiChevronLeft, FiChevronRight, FiLoader, FiX } from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import { useProductData } from './hooks/useProductData';
import { validateProduct, formatProductDisplayName, formatProductPrice } from './utils/productHelpers';
import { productModalStyles } from './styles/productModalStyles';
import type { ProductModalProps, Product, CreateProductDto } from './types';

export function ProductModal({
  isOpen,
  products: initialProducts = [], // Keep for backward compatibility but won't use
  onSelect,
  onClose,
  onCreateNew,
  disabled = false,
}: ProductModalProps) {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  // Use the product data hook for efficient loading and caching
  const {
    products,
    isLoading,
    isSearching,
    error,
    currentPage,
    totalPages,
    totalProducts,
    loadProducts,
    clearError
  } = useProductData();
  
  const pageSize = 20; // Products per page
  
  // Request debouncing
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sku: '',
    barcode: '',
    retailPrice: undefined as number | undefined,
    wholesalePrice: undefined as number | undefined,
    midWholesalePrice: undefined as number | undefined,
    institutionalPrice: undefined as number | undefined,
    cost: undefined as number | undefined,
    warehouseUuid: '',
  });
  const [errors, setErrors] = useState<string[]>([]);

  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        handleClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Centralized close handler
  const handleClose = useCallback(() => {
    if (showCreateForm) {
      // If in create form, go back to product list
      setShowCreateForm(false);
      setErrors([]);
    } else {
      // Close the modal completely
      onClose();
    }
  }, [showCreateForm, onClose]);

  // Load products when modal opens or search term changes
  useEffect(() => {
    if (!isOpen || !warehouseUuid) return;
    
    loadProducts(1, debouncedSearchTerm);
  }, [isOpen, warehouseUuid, loadProducts]);

  // Handle debounced search - only trigger when search term actually changes
  useEffect(() => {
    if (!isOpen || !warehouseUuid) return;
    
    // Clear any existing timeout
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    requestTimeoutRef.current = setTimeout(() => {
      loadProducts(1, debouncedSearchTerm);
    }, 300);
    
    // Cleanup timeout on unmount or dependency change
    return () => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, [debouncedSearchTerm, isOpen, warehouseUuid, loadProducts]);

  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, []);

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && !isLoading && !isSearching) {
      loadProducts(newPage, debouncedSearchTerm);
    }
  };

  const handleCreateProduct = () => {
    const validation = validateProduct(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onCreateNew({
      ...formData,
      warehouseUuid: warehouseUuid!
    });
    onClose();
  };

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
      setShowCreateForm(false);
      setErrors([]);
      clearError();
    }
  }, [isOpen, clearError]);

  // Update debounced search term when search term changes
  useEffect(() => {
    setDebouncedSearchTerm(searchTerm);
  }, [searchTerm]);

  if (!isOpen) return null;

  return (
    <div className={productModalStyles.modal.overlay} onClick={handleOverlayClick}>
      <div className={productModalStyles.modal.content}>
        <div className={productModalStyles.modal.header}>
          <div>
            <h3 className={productModalStyles.modal.title}>
              {showCreateForm ? 'Create New Product' : 'Select Product'}
            </h3>
            {!showCreateForm && totalProducts > 0 && (
              <p className={productModalStyles.modal.subtitle}>
                {totalProducts} products found
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            className={productModalStyles.modal.closeButton}
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className={productModalStyles.modal.body}>
          {/* Scrollable Content Area */}
          <div className={productModalStyles.modal.contentArea}>
            {!showCreateForm ? (
              <>
                {/* Search Bar */}
                <div className={productModalStyles.modal.searchContainer}>
                  <div className="relative">
                    <FiSearch className={productModalStyles.modal.searchIcon} />
                    <input
                      type="text"
                      placeholder="Search products by name, SKU, or barcode..."
                      value={searchTerm}
                      onChange={(e) => {
                        if (disabled) return;
                        setSearchTerm(e.target.value);
                      }}
                      className={`${productModalStyles.modal.searchInput} ${disabled ? productModalStyles.modal.searchInputDisabled : ''}`}
                      autoFocus
                      disabled={disabled}
                    />
                    {isSearching && (
                      <FiLoader className={productModalStyles.modal.loadingIcon} />
                    )}
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <div className={productModalStyles.modal.errorContainer}>
                    <div className={productModalStyles.modal.errorText}>
                      {error}
                    </div>
                  </div>
                )}

                {/* Loading State */}
                {isLoading && (
                  <div className={productModalStyles.modal.loadingContainer}>
                    <FiLoader className="animate-spin h-6 w-6 text-blue-500" />
                    <span className={productModalStyles.modal.loadingText}>Loading products...</span>
                  </div>
                )}

                {/* Product List */}
                {!isLoading && products.length > 0 && (
                  <div className={productModalStyles.modal.productList}>
                    {products.map((product) => (
                      <div
                        key={product.uuid}
                        className={`${productModalStyles.modal.productItem} ${
                          disabled ? productModalStyles.modal.productItemDisabled : ''
                        }`}
                        onClick={() => {
                          if (disabled) return;
                          onSelect(product);
                          onClose();
                        }}
                      >
                        <div className={productModalStyles.modal.productName}>
                          {formatProductDisplayName(product)}
                        </div>
                        <div className={productModalStyles.modal.productDetails}>
                          {product.description && `${product.description} • `}
                          {formatProductPrice(product)}
                          {product.isPack && ' • Pack'}
                          {product.isComponent && ' • Component'}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* No Results */}
                {!isLoading && products.length === 0 && searchTerm && (
                  <div className={productModalStyles.modal.noResultsContainer}>
                    <p className={productModalStyles.modal.noResultsText}>No products found matching "{searchTerm}"</p>
                    <button
                      onClick={() => !disabled && setShowCreateForm(true)}
                      disabled={disabled}
                      className={`${productModalStyles.modal.createNewButton} ${disabled ? productModalStyles.modal.createNewButtonDisabled : ''}`}
                    >
                      Create new product
                    </button>
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className={productModalStyles.modal.paginationContainer}>
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1 || isLoading || disabled}
                      className={productModalStyles.modal.paginationButton}
                    >
                      <FiChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </button>
                    
                    <span className={productModalStyles.modal.paginationText}>
                      Page {currentPage} of {totalPages}
                    </span>
                    
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages || isLoading || disabled}
                      className={productModalStyles.modal.paginationButton}
                    >
                      Next
                      <FiChevronRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                )}
              </>
            ) : (
              /* Create Product Form */
              <div className={productModalStyles.modal.formContainer}>
                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => !disabled && setFormData({ ...formData, name: e.target.value })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="Product name"
                    autoFocus
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => !disabled && setFormData({ ...formData, description: e.target.value })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="Product description"
                    rows={3}
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    SKU
                  </label>
                  <input
                    type="text"
                    value={formData.sku}
                    onChange={(e) => !disabled && setFormData({ ...formData, sku: e.target.value })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="Stock Keeping Unit"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    Barcode
                  </label>
                  <input
                    type="text"
                    value={formData.barcode}
                    onChange={(e) => !disabled && setFormData({ ...formData, barcode: e.target.value })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="Product barcode"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    Retail Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.retailPrice || ''}
                    onChange={(e) => !disabled && setFormData({ 
                      ...formData, 
                      retailPrice: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="0.00"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className={productModalStyles.modal.formGroup}>
                    Cost
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.cost || ''}
                    onChange={(e) => !disabled && setFormData({ 
                      ...formData, 
                      cost: e.target.value ? parseFloat(e.target.value) : undefined 
                    })}
                    className={`${productModalStyles.modal.formInput} ${disabled ? productModalStyles.modal.formInputDisabled : ''}`}
                    placeholder="0.00"
                    disabled={disabled}
                  />
                </div>

                {errors.length > 0 && (
                  <div className={productModalStyles.modal.validationErrors}>
                    <div className={productModalStyles.modal.validationErrorText}>
                      {errors.map((error, index) => (
                        <div key={index}>{error}</div>
                      ))}
                    </div>
                  </div>
                )}

                <div className={productModalStyles.modal.formButtons}>
                  <button
                    onClick={() => !disabled && setShowCreateForm(false)}
                    disabled={disabled}
                    className={productModalStyles.modal.cancelButton}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateProduct}
                    disabled={disabled}
                    className={productModalStyles.modal.createButton}
                  >
                    Create Product
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Fixed Bottom Section - Create Product Button */}
          {!showCreateForm && (
            <div className={productModalStyles.modal.bottomSection}>
              <button
                onClick={() => !disabled && setShowCreateForm(true)}
                disabled={disabled}
                className={productModalStyles.modal.createNewProductButton}
              >
                Create New Product
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 