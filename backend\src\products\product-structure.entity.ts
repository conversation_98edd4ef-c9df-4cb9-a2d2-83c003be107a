import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { Product } from './product.entity';

@Entity('product_structure')
export class ProductStructure {
  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the product structure (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the warehouse this structure belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the assembly product (pack/parent product)",
  })
  @Column('uuid')
  @Index()
  assemblyProductUuid: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the component product (child product)",
  })
  @Column('uuid')
  @Index()
  componentProductUuid: string;

  @ApiProperty({
    example: 5,
    description: "Quantity of the component product required in the assembly",
  })
  @Column('int')
  quantity: number;

  @ApiProperty({ 
    example: false, 
    description: "Soft delete flag" 
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations for easier querying (optional)
  @ManyToOne(() => Product)
  @JoinColumn({ name: 'assemblyProductUuid', referencedColumnName: 'id' })
  assemblyProduct?: Product;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'componentProductUuid', referencedColumnName: 'id' })
  componentProduct?: Product;

  // Helper method to generate UUID
  static generateId(): string {
    return Uuid7.generate().toString();
  }
}
