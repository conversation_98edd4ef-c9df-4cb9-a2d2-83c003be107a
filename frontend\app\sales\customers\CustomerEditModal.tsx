// CustomerEditModal.tsx - Modal for editing customers
// Follows UI Guidelines: Modal UX Conventions (All Resource Modals)
// - Escape key closes modal
// - First field auto-focused when modal opens
// - Only name field is required as per user request

import React, { useState, useEffect, useRef } from 'react';
import { Customer, UpdateCustomerDto } from './customersApi';

interface CustomerEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: UpdateCustomerDto) => Promise<void>;
  customer: Customer | null;
  isLoading?: boolean;
  error?: string | null;
}

const CustomerEditModal: React.FC<CustomerEditModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  customer,
  isLoading = false,
  error = null,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [showFiscalInfo, setShowFiscalInfo] = useState(false);
  const [showMapCoordinates, setShowMapCoordinates] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    fiscalId: '',
    email: '',
    phone: '',
    address: '',
    rc: '',
    articleNumber: '',
    customerType: 'retail' as 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional',
    latitude: 0,
    longitude: 0,
  });

  // Reset form when modal opens/closes or customer changes
  useEffect(() => {
    if (isOpen && customer) {
      // Editing existing customer
      setFormData({
        name: customer.name || '',
        fiscalId: customer.fiscalId || '',
        email: customer.email || '',
        phone: customer.phone || '',
        address: customer.address || '',
        rc: customer.rc || '',
        articleNumber: customer.articleNumber || '',
        customerType: customer.customerType || 'retail',
        latitude: customer.latitude || 0,
        longitude: customer.longitude || 0,
      });
      // Show sections if they have data
      setShowFiscalInfo(!!(customer.fiscalId || customer.rc || customer.articleNumber));
      setShowMapCoordinates(!!(customer.latitude || customer.longitude));
    }
  }, [isOpen, customer]);

  // Handle modal focus and escape key
  useEffect(() => {
    if (!isOpen) return;

    // Focus first input when modal opens
    const firstInput = modalRef.current?.querySelector('input, textarea, select') as HTMLElement;
    if (firstInput) {
      firstInput.focus();
    }

    // Handle escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'latitude' || name === 'longitude' 
        ? value === '' ? 0 : parseFloat(value)
        : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Prepare data for submission - exclude empty strings
      const submitData = {
        name: formData.name,
        fiscalId: formData.fiscalId?.trim() || undefined,
        email: formData.email?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
        address: formData.address?.trim() || undefined,
        rc: formData.rc?.trim() || undefined,
        articleNumber: formData.articleNumber?.trim() || undefined,
        customerType: formData.customerType,
        latitude: formData.latitude && formData.latitude !== 0 ? formData.latitude : undefined,
        longitude: formData.longitude && formData.longitude !== 0 ? formData.longitude : undefined,
      };
      await onSubmit(submitData);
      onClose();
    } catch (error) {
      // Error handling is done by parent component
      console.error('Error updating customer:', error);
    }
  };

  if (!isOpen || !customer) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div 
        ref={modalRef}
        className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto"
        role="dialog"
        aria-modal="true"
        aria-labelledby="customer-edit-modal-title"
      >
        <h2 id="customer-edit-modal-title" className="text-xl font-semibold mb-4">
          Edit Customer
        </h2>
        
        <p className="text-sm text-gray-600 mb-6">
          Update the customer information below.
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <p className="text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name - Required */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter customer name"
              required
            />
          </div>

          {/* Customer Type */}
          <div>
            <label htmlFor="customerType" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Type
            </label>
            <select
              id="customerType"
              name="customerType"
              value={formData.customerType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="retail">Retail</option>
              <option value="wholesale">Wholesale</option>
              <option value="mid-wholesale">Mid-Wholesale</option>
              <option value="institutional">Institutional</option>
            </select>
          </div>

          {/* Basic Contact Information */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter email address"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <input
              type="text"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter address"
            />
          </div>

          {/* Fiscal Information Section */}
          <div className="border-t pt-4">
            <button
              type="button"
              onClick={() => setShowFiscalInfo(!showFiscalInfo)}
              className="flex items-center justify-between w-full px-3 py-2 text-left text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <span>Fiscal Information</span>
              <svg 
                className={`w-4 h-4 transition-transform ${showFiscalInfo ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {showFiscalInfo && (
              <div className="mt-3 space-y-4 pl-3 border-l-2 border-gray-200">
                <div>
                  <label htmlFor="fiscalId" className="block text-sm font-medium text-gray-700 mb-1">
                    Fiscal ID / Tax Number
                  </label>
                  <input
                    type="text"
                    id="fiscalId"
                    name="fiscalId"
                    value={formData.fiscalId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter fiscal ID / tax number"
                  />
                </div>

                {/* RC Field */}
                <div className="col-span-6 sm:col-span-3">
                  <label htmlFor="rc" className="block text-sm font-medium text-gray-700 mb-1">
                    RC (Commercial Register)
                  </label>
                  <input
                    type="text"
                    id="rc"
                    name="rc"
                    value={formData.rc}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="RC123456"
                  />
                </div>

                {/* Article Number Field */}
                <div className="col-span-6 sm:col-span-3">
                  <label htmlFor="articleNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Article Number
                  </label>
                  <input
                    type="text"
                    id="articleNumber"
                    name="articleNumber"
                    value={formData.articleNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="ART001"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Map Coordinates Section */}
          <div className="border-t pt-4">
            <button
              type="button"
              onClick={() => setShowMapCoordinates(!showMapCoordinates)}
              className="flex items-center justify-between w-full px-3 py-2 text-left text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <span>Map Coordinates</span>
              <svg 
                className={`w-4 h-4 transition-transform ${showMapCoordinates ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {showMapCoordinates && (
              <div className="mt-3 space-y-4 pl-3 border-l-2 border-gray-200">
                <div>
                  <label htmlFor="latitude" className="block text-sm font-medium text-gray-700 mb-1">
                    Latitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    id="latitude"
                    name="latitude"
                    value={formData.latitude || ''}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter latitude coordinate"
                  />
                </div>

                <div>
                  <label htmlFor="longitude" className="block text-sm font-medium text-gray-700 mb-1">
                    Longitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    id="longitude"
                    name="longitude"
                    value={formData.longitude || ''}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter longitude coordinate"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Buttons */}
          <div className="flex flex-col gap-3 pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Updating...
                </>
              ) : (
                'Update Customer'
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerEditModal; 