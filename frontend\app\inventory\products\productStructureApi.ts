// productStructureApi.ts - API utilities for Product Structure (Pack) endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';
import { Product } from './productsApi';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export interface ProductStructure {
  uuid: string;
  warehouseUuid: string;
  assemblyProductUuid: string;
  componentProductUuid: string;
  quantity: number;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  // Optional product details for expanded responses
  assemblyProduct?: Product;
  componentProduct?: Product;
}

export interface CreateProductStructureDto {
  warehouseUuid: string;
  assemblyProductUuid: string;
  componentProductUuid: string;
  quantity: number;
}

export interface UpdateProductStructureDto {
  assemblyProductUuid?: string;
  componentProductUuid?: string;
  quantity?: number;
}

export interface BillOfMaterialsResponse {
  assemblyProductUuid: string;
  components: ProductStructure[];
}

export interface WhereUsedResponse {
  componentProductUuid: string;
  assemblies: ProductStructure[];
}

export interface PaginatedResponseDto<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

// Create a new product structure
export async function createProductStructure(data: CreateProductStructureDto): Promise<ProductStructure> {
  const res = await axios.post('/api/product-structure', data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get all product structures with pagination
export async function getAllProductStructures(paginationQuery?: PaginationQuery): Promise<PaginatedResponseDto<ProductStructure>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/product-structure${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get product structures by warehouse
export async function getProductStructuresByWarehouse(
  warehouseUuid: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<ProductStructure>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/product-structure/warehouse/${warehouseUuid}${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get product structures by assembly product (Bill of Materials)
export async function getProductStructuresByAssembly(
  assemblyProductUuid: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<ProductStructure>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/product-structure/assembly/${assemblyProductUuid}${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get product structures by component product (Where Used)
export async function getProductStructuresByComponent(
  componentProductUuid: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<ProductStructure>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/product-structure/component/${componentProductUuid}${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get complete Bill of Materials for an assembly product (non-paginated)
export async function getBillOfMaterials(assemblyProductUuid: string): Promise<BillOfMaterialsResponse> {
  const res = await axios.get(`/api/product-structure/bom/${assemblyProductUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get complete Where Used list for a component product (non-paginated)
export async function getWhereUsed(componentProductUuid: string): Promise<WhereUsedResponse> {
  const res = await axios.get(`/api/product-structure/where-used/${componentProductUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get a single product structure by UUID
export async function getProductStructureById(uuid: string): Promise<ProductStructure> {
  const res = await axios.get(`/api/product-structure/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Update a product structure
export async function updateProductStructure(uuid: string, data: UpdateProductStructureDto): Promise<ProductStructure> {
  const res = await axios.put(`/api/product-structure/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Delete a product structure (soft delete)
export async function deleteProductStructure(uuid: string): Promise<ProductStructure> {
  const res = await axios.delete(`/api/product-structure/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get total count of product structures
export async function getProductStructuresCount(): Promise<{ count: number }> {
  const res = await axios.get('/api/product-structure/count', {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 