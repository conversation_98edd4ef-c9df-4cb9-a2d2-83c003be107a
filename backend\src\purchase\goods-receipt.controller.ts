import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from "@nestjs/swagger";
import { GoodsReceiptService } from "./goods-receipt.service";
import { CreateGoodsReceiptDto } from "./dto/create-goods-receipt.dto";
import { GoodsReceiptResponseDto, GoodsReceiptListResponseDto } from "./dto/goods-receipt-response.dto";
import { LogsService } from "../logs/logs.service";

@ApiTags("goods-receipts")
@Controller("goods-receipts")
export class GoodsReceiptController {
  constructor(
    private readonly goodsReceiptService: GoodsReceiptService,
    private readonly logsService: LogsService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new goods receipt" })
  @ApiResponse({ status: 201, description: "Goods receipt created successfully" })
  async create(@Body() createGoodsReceiptDto: CreateGoodsReceiptDto): Promise<GoodsReceiptResponseDto> {
    const result = await this.goodsReceiptService.create(
      createGoodsReceiptDto.purchaseUuid,
      createGoodsReceiptDto.supplierUuid,
      createGoodsReceiptDto.warehouseUuid,
      createGoodsReceiptDto.receiptItems,
      new Date(createGoodsReceiptDto.receiptDate),
      new Date(createGoodsReceiptDto.expectedReceiptDate),
      createGoodsReceiptDto.status,
      createGoodsReceiptDto.notes,
      createGoodsReceiptDto.createdBy,
      createGoodsReceiptDto.updatedBy,
    );

    // Log the creation
    await this.logsService.create({
      userUuid: createGoodsReceiptDto.createdBy,
      operation: "CREATED",
      entityType: "goods_receipt",
      entity: result.id,
      description: `Goods receipt created`,
      data: {
        purchaseUuid: createGoodsReceiptDto.purchaseUuid,
        supplierUuid: createGoodsReceiptDto.supplierUuid,
        warehouseUuid: createGoodsReceiptDto.warehouseUuid,
        itemCount: createGoodsReceiptDto.receiptItems.length,
      },
    });

    return result;
  }

  @Post("partial")
  @ApiOperation({ summary: "Create a partial goods receipt for partial deliveries" })
  @ApiResponse({ status: 201, description: "Partial goods receipt created successfully" })
  async createPartialReceipt(@Body() body: {
    purchaseUuid: string;
    supplierUuid: string;
    warehouseUuid: string;
    receiptItems: any[];
    receiptDate: string;
    partialDeliveryNotes?: string;
    createdBy: string;
  }): Promise<GoodsReceiptResponseDto> {
    const result = await this.goodsReceiptService.createPartialReceipt(
      body.purchaseUuid,
      body.supplierUuid,
      body.warehouseUuid,
      body.receiptItems,
      new Date(body.receiptDate),
      body.partialDeliveryNotes,
      body.createdBy,
    );

    // Log the partial receipt creation
    await this.logsService.create({
      userUuid: body.createdBy,
      operation: "PARTIAL_RECEIPT_CREATED",
      entityType: "goods_receipt",
      entity: result.id,
      description: `Partial goods receipt created`,
      data: {
        purchaseUuid: body.purchaseUuid,
        supplierUuid: body.supplierUuid,
        warehouseUuid: body.warehouseUuid,
        itemCount: body.receiptItems.length,
        partialDeliveryNotes: body.partialDeliveryNotes,
      },
    });

    return result;
  }

  @Get()
  @ApiOperation({ summary: "Get all goods receipts with filtering" })
  @ApiResponse({ status: 200, description: "Goods receipts retrieved successfully" })
  @ApiQuery({ name: "purchaseUuid", required: false })
  @ApiQuery({ name: "supplierUuid", required: false })
  @ApiQuery({ name: "warehouseUuid", required: false })
  @ApiQuery({ name: "status", required: false })
  @ApiQuery({ name: "page", required: false, type: Number })
  @ApiQuery({ name: "limit", required: false, type: Number })
  async findAll(
    @Query("purchaseUuid") purchaseUuid?: string,
    @Query("supplierUuid") supplierUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<GoodsReceiptListResponseDto> {
    return this.goodsReceiptService.findAll({
      purchaseUuid,
      supplierUuid,
      warehouseUuid,
      status,
      page,
      limit,
    });
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a specific goods receipt" })
  @ApiResponse({ status: 200, description: "Goods receipt retrieved successfully" })
  @ApiParam({ name: "uuid", description: "Goods receipt UUID" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<GoodsReceiptResponseDto> {
    return this.goodsReceiptService.findOne(uuid);
  }

  @Get("warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get goods receipts for a specific warehouse" })
  @ApiResponse({ status: 200, description: "Warehouse goods receipts retrieved successfully" })
  @ApiParam({ name: "warehouseUuid", description: "Warehouse UUID" })
  async findByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string): Promise<GoodsReceiptResponseDto[]> {
    return this.goodsReceiptService.findByWarehouse(warehouseUuid);
  }

  @Get("purchase/:purchaseUuid")
  @ApiOperation({ summary: "Get goods receipts for a specific purchase" })
  @ApiResponse({ status: 200, description: "Purchase goods receipts retrieved successfully" })
  @ApiParam({ name: "purchaseUuid", description: "Purchase UUID" })
  async findByPurchase(@Param("purchaseUuid", ParseUUIDPipe) purchaseUuid: string): Promise<GoodsReceiptResponseDto[]> {
    return this.goodsReceiptService.findByPurchase(purchaseUuid);
  }

  @Get("analytics/warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get goods receipt analytics for a warehouse" })
  @ApiResponse({ status: 200, description: "Warehouse analytics retrieved successfully" })
  @ApiParam({ name: "warehouseUuid", description: "Warehouse UUID" })
  async getWarehouseAnalytics(
    @Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string,
  ) {
    const dateRange = startDate && endDate ? {
      startDate: new Date(startDate),
      endDate: new Date(endDate),
    } : undefined;

    return this.goodsReceiptService.getWarehouseReceiptAnalytics(warehouseUuid, dateRange);
  }

  @Get("pending")
  @ApiOperation({ summary: "Get pending goods receipts" })
  @ApiResponse({ status: 200, description: "Pending receipts retrieved successfully" })
  async getPendingReceipts(@Query("warehouseUuid") warehouseUuid?: string): Promise<GoodsReceiptResponseDto[]> {
    return this.goodsReceiptService.getPendingReceipts(warehouseUuid);
  }

  @Get("overdue")
  @ApiOperation({ summary: "Get overdue goods receipts" })
  @ApiResponse({ status: 200, description: "Overdue receipts retrieved successfully" })
  async getOverdueReceipts(@Query("warehouseUuid") warehouseUuid?: string): Promise<GoodsReceiptResponseDto[]> {
    return this.goodsReceiptService.getOverdueReceipts(warehouseUuid);
  }

  @Patch(":uuid/complete")
  @ApiOperation({ summary: "Complete a goods receipt" })
  @ApiResponse({ status: 200, description: "Goods receipt completed successfully" })
  @ApiParam({ name: "uuid", description: "Goods receipt UUID" })
  async completeReceipt(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string; completionNotes?: string },
  ): Promise<GoodsReceiptResponseDto> {
    const result = await this.goodsReceiptService.completeReceipt(uuid, body.userUuid, body.completionNotes);

    // Log the completion
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "COMPLETED",
      entityType: "goods_receipt",
      entity: uuid,
      description: `Goods receipt completed`,
      data: {
        completionNotes: body.completionNotes,
      },
    });

    return result;
  }

  @Patch(":uuid/quality-control")
  @ApiOperation({ summary: "Add quality control notes to a goods receipt" })
  @ApiResponse({ status: 200, description: "Quality control notes added successfully" })
  @ApiParam({ name: "uuid", description: "Goods receipt UUID" })
  async addQualityControlNotes(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: {
      userUuid: string;
      qualityNotes: string;
      qualityStatus: 'PASSED' | 'FAILED' | 'PARTIAL';
    },
  ): Promise<GoodsReceiptResponseDto> {
    const result = await this.goodsReceiptService.addQualityControlNotes(
      uuid,
      body.userUuid,
      body.qualityNotes,
      body.qualityStatus,
    );

    // Log the quality control action
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "QUALITY_CONTROL",
      entityType: "goods_receipt",
      entity: uuid,
      description: `Quality control notes added`,
      data: {
        qualityStatus: body.qualityStatus,
        qualityNotes: body.qualityNotes,
      },
    });

    return result;
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update goods receipt status" })
  @ApiResponse({ status: 200, description: "Status updated successfully" })
  @ApiParam({ name: "uuid", description: "Goods receipt UUID" })
  async updateStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { status: string; userUuid: string },
  ): Promise<GoodsReceiptResponseDto> {
    const result = await this.goodsReceiptService.updateStatus(uuid, body.status as any, body.userUuid);

    // Log the status change
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "STATUS_UPDATED",
      entityType: "goods_receipt",
      entity: uuid,
      description: `Goods receipt status updated`,
      data: {
        newStatus: body.status,
      },
    });

    return result;
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a goods receipt" })
  @ApiResponse({ status: 200, description: "Goods receipt deleted successfully" })
  @ApiParam({ name: "uuid", description: "Goods receipt UUID" })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<{ message: string }> {
    const result = await this.goodsReceiptService.remove(uuid);

    // Log the deletion (without userUuid for now)
    await this.logsService.create({
      operation: "DELETED",
      entityType: "goods_receipt",
      entity: uuid,
      description: `Goods receipt deleted`,
      data: {
        deletionReason: "User requested deletion",
      },
    });

    return result;
  }
} 