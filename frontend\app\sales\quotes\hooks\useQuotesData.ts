import { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { getQuotes, FilterQuoteDto, PaginationQueryDto } from '../quotesApi';

export const useQuotesData = () => {
  const { user, token } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  // Quotes List state
  const [filter, setFilter] = useState<FilterQuoteDto>({});
  const [pagination, setPagination] = useState<PaginationQueryDto>({ page: 1, limit: 10 });
  const [showFilters, setShowFilters] = useState(true);

  // Memoize the filter and pagination objects to prevent infinite loops
  const memoizedFilter = useMemo(() => filter, [JSON.stringify(filter)]);
  const memoizedPagination = useMemo(() => pagination, [JSON.stringify(pagination)]);

  // Memoize the query key dependencies to prevent infinite loops
  const queryKey = useMemo(() => ['quotes', warehouseUuid, memoizedFilter, memoizedPagination], [warehouseUuid, memoizedFilter, memoizedPagination]);

  // Query for quotes data
  const { data: quotesResponse, isLoading, error, refetch } = useQuery({
    queryKey,
    queryFn: () => {
      // Validate warehouseUuid before making the request
      if (!warehouseUuid || warehouseUuid.trim() === '') {
        throw new Error('Warehouse UUID is required');
      }
      
      // Always include warehouseUuid in the filter
      const filterWithWarehouse = {
        ...memoizedFilter,
        warehouseUuid: warehouseUuid.trim()
      };
      
      return getQuotes(memoizedPagination, filterWithWarehouse);
    },
    enabled: !!warehouseUuid && warehouseUuid.trim() !== '' && !!token,
  });

  const quotes = quotesResponse?.quotes || [];
  const total = quotesResponse?.total || 0;
  const totalPages = quotesResponse?.totalPages || 0;

  // Filter handlers
  const updateFilter = (newFilter: Partial<FilterQuoteDto>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilter({});
    setPagination({ page: 1, limit: 10 });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    // Validate page number
    if (page && !isNaN(page) && page > 0) {
      setPagination(prev => ({ ...prev, page }));
    } else {
      console.warn('Invalid page number:', page);
    }
  };

  const toggleFilters = () => {
    setShowFilters(prev => !prev);
  };

  return {
    // Data
    quotes,
    total,
    totalPages,
    
    // State
    filter,
    pagination,
    showFilters,
    isLoading,
    error,
    
    // Actions
    updateFilter,
    clearFilters,
    handlePageChange,
    toggleFilters,
    refetch,
    
    // Computed
    warehouseUuid
  };
};
