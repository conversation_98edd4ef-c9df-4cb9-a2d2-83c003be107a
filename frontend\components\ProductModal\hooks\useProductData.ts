import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { searchProducts, type ProductFilter } from '../api/productApi';
import type { Product } from '../types';

interface UseProductDataReturn {
  products: Product[];
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalProducts: number;
  loadProducts: (page?: number, search?: string) => Promise<void>;
  clearError: () => void;
}

export function useProductData(): UseProductDataReturn {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  
  // Cache for storing product data
  const productCache = useRef<Map<string, { data: Product[]; timestamp: number; total: number; totalPages: number }>>(new Map());
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isRequestInProgressRef = useRef(false);
  const currentRequestRef = useRef<string | null>(null);
  
  // Cache expiration time (5 minutes)
  const CACHE_EXPIRY = 5 * 60 * 1000;
  
  // Generate cache key for the current request
  const getCacheKey = useCallback((page: number, search: string) => {
    return `${warehouseUuid}-${page}-${search}`;
  }, [warehouseUuid]);
  
  // Check if cached data is still valid
  const isCacheValid = useCallback((cacheKey: string) => {
    const cached = productCache.current.get(cacheKey);
    if (!cached) return false;
    
    const now = Date.now();
    return (now - cached.timestamp) < CACHE_EXPIRY;
  }, []);
  
  // Load products with caching and request deduplication
  const loadProducts = useCallback(async (page: number = 1, search: string = '') => {
    if (!warehouseUuid) {
      console.error('[useProductData] No warehouseUuid available');
      setError('Warehouse information not available');
      return;
    }
    
    const cacheKey = getCacheKey(page, search);
    
    // Check if we have valid cached data
    if (isCacheValid(cacheKey)) {
      const cached = productCache.current.get(cacheKey)!;
      console.log('[useProductData] Using cached data for:', cacheKey);
      setProducts(cached.data);
      setCurrentPage(page);
      setTotalPages(cached.totalPages);
      setTotalProducts(cached.total);
      setError(null);
      return;
    }
    
    // Prevent concurrent requests with the same parameters
    if (isRequestInProgressRef.current && currentRequestRef.current === cacheKey) {
      console.log('[useProductData] Request already in progress for:', cacheKey);
      return;
    }
    
    // Cancel any pending request
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }
    
    isRequestInProgressRef.current = true;
    currentRequestRef.current = cacheKey;
    setIsLoading(page === 1);
    setIsSearching(page > 1 || !!search);
    setError(null);
    
    console.log('[useProductData] Loading products:', { page, search, warehouseUuid, cacheKey });
    
    try {
      const response = await searchProducts(warehouseUuid, search);
      console.log('[useProductData] API response:', response);
      
      // Cache the response
      productCache.current.set(cacheKey, {
        data: response.data || [],
        timestamp: Date.now(),
        total: response.total || 0,
        totalPages: response.totalPages || Math.ceil((response.total || 0) / 20)
      });
      
      setProducts(response.data || []);
      setCurrentPage(response.page || page);
      setTotalPages(response.totalPages || Math.ceil((response.total || 0) / 20));
      setTotalProducts(response.total || 0);
      
      console.log('[useProductData] State updated:', {
        productsCount: response.data?.length || 0,
        currentPage: response.page || page,
        totalPages: response.totalPages || Math.ceil((response.total || 0) / 20),
        totalProducts: response.total || 0
      });
      
    } catch (err: any) {
      console.error('[useProductData] Error loading products:', err);
      setError(err.message || 'Failed to load products');
    } finally {
      isRequestInProgressRef.current = false;
      currentRequestRef.current = null;
      setIsLoading(false);
      setIsSearching(false);
    }
  }, [warehouseUuid, getCacheKey, isCacheValid]);
  
  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
      // Reset request state
      isRequestInProgressRef.current = false;
      currentRequestRef.current = null;
    };
  }, []);
  
  return {
    products,
    isLoading,
    isSearching,
    error,
    currentPage,
    totalPages,
    totalProducts,
    loadProducts,
    clearError,
  };
} 