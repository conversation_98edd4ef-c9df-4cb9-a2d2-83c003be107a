import React from 'react';
import { <PERSON><PERSON>ye, FiShoppingCart, FiPrinter, FiX } from 'react-icons/fi';
import ItemsTable from '@/components/itemsTable/ItemsTable';
import TableActionButtons from '@/components/itemsTable/TableActionButtons';
import { Quote } from '../quotesApi';
import { formatDate, getStatusBadgeColor } from '../quotesHelpers';
import { formatCurrency } from '../pos/utils/posHelpers';

interface QuotesListViewProps {
  quotes: Quote[];
  totalPages: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingInvoice: boolean;
  isLoadingEditQuote?: boolean;
  isLoadingViewDetails?: boolean;
  isLoadingPrintInvoice?: boolean;
  isLoadingCancel?: boolean;
  onPageChange: (page: number) => void;
  onViewDetails: (quote: Quote) => void;
  onEdit: (quote: Quote) => void;
  onPrintInvoice: (quote: Quote) => void;
  onCancel: (quote: Quote) => void;
}

export const QuotesListView: React.FC<QuotesListViewProps> = ({
  quotes,
  totalPages,
  currentPage,
  isLoading,
  isLoadingInvoice,
  isLoadingEditQuote = false,
  isLoadingViewDetails = false,
  isLoadingPrintInvoice = false,
  isLoadingCancel = false,
  onPageChange,
  onViewDetails,
  onEdit,
  onPrintInvoice,
  onCancel
}) => {
  // Check if any action is currently loading
  const isAnyActionLoading = isLoadingEditQuote || isLoadingViewDetails || isLoadingPrintInvoice || isLoadingCancel;

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      <ItemsTable
        columns={[
          { 
            key: 'quoteNumber', 
            header: 'Quote #',
            render: (value: string, row: Quote) => (
              <div className="font-semibold text-gray-900">{value}</div>
            )
          },
          { 
            key: 'customerName', 
            header: 'Customer',
            render: (value: string, row: Quote) => (
              <div className="text-sm text-gray-900">{row.customerName || 'N/A'}</div>
            )
          },
          { 
            key: 'status', 
            header: 'Status',
            render: (value: string, row: Quote) => (
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(value)}`}>
                {value.replace('_', ' ')}
              </span>
            )
          },
          { 
            key: 'totalAmount', 
            header: 'Total',
            render: (value: number, row: Quote) => (
              <div className="text-sm text-gray-900">{formatCurrency(value)}</div>
            )
          },
          { 
            key: 'createdAt', 
            header: 'Date',
            render: (value: string, row: Quote) => (
              <div className="text-sm text-gray-900">{formatDate(value)}</div>
            )
          },
          {
            key: 'actions',
            header: 'Actions',
            render: (_: any, row: Quote) => (
              <div className="flex items-center justify-center gap-2">
                <button
                  onClick={() => onViewDetails(row)}
                  className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="View Details"
                  aria-label="View Details"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingViewDetails ? (
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiEye className="w-4 h-4 text-blue-600" />
                  )}
                </button>
                <button
                  onClick={() => onEdit(row)}
                  className="p-2 rounded-full hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Edit Quote"
                  aria-label="Edit Quote"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingEditQuote ? (
                    <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiShoppingCart className="w-4 h-4 text-purple-600" />
                  )}
                </button>
                <button
                  onClick={() => onPrintInvoice(row)}
                  className="p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Print Invoice"
                  aria-label="Print Invoice"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingPrintInvoice ? (
                    <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiPrinter className="w-4 h-4 text-green-600" />
                  )}
                </button>
                <button
                  onClick={() => onCancel(row)}
                  className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Cancel Quote"
                  aria-label="Cancel Quote"
                  disabled={isAnyActionLoading || row.status === 'cancelled'}
                >
                  {isLoadingCancel ? (
                    <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiX className="w-4 h-4 text-red-600" />
                  )}
                </button>
              </div>
            ),
            headerClassName: 'text-center',
            cellClassName: 'text-center',
          },
        ]}
        data={quotes}
        isLoading={isLoading}
        noDataText={<span className="text-gray-400">No quotes found.</span>}
        containerClassName=""
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
