import React, { useState } from 'react';
import { ProductModal, type Product, type CreateProductDto } from './index';

export function ProductModalExample() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const handleProductSelect = (product: Product | null) => {
    setSelectedProduct(product);
    setIsModalOpen(false);
    console.log('Selected product:', product);
  };

  const handleCreateProduct = (productData: CreateProductDto) => {
    console.log('Creating new product:', productData);
    // Here you would typically call your API to create the product
    // For example:
    // await createProduct(productData);
    // Then refresh the product list or handle the response
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Product Modal Example</h2>
      
      <button
        onClick={() => setIsModalOpen(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Select Product
      </button>

      {selectedProduct && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Selected Product:</h3>
          <p><strong>Name:</strong> {selectedProduct.name}</p>
          <p><strong>SKU:</strong> {selectedProduct.sku || 'N/A'}</p>
          <p><strong>Barcode:</strong> {selectedProduct.barcode || 'N/A'}</p>
          <p><strong>Price:</strong> ${selectedProduct.retailPrice || selectedProduct.price || 'N/A'}</p>
          {selectedProduct.description && (
            <p><strong>Description:</strong> {selectedProduct.description}</p>
          )}
        </div>
      )}

      <ProductModal
        isOpen={isModalOpen}
        onSelect={handleProductSelect}
        onClose={() => setIsModalOpen(false)}
        onCreateNew={handleCreateProduct}
      />
    </div>
  );
} 