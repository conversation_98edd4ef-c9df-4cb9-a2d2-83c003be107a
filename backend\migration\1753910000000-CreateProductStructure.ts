import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateProductStructure1753910000000 implements MigrationInterface {
    name = 'CreateProductStructure1753910000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create product_structure table
        await queryRunner.query(`
            CREATE TABLE "product_structure" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "warehouseUuid" uuid NOT NULL,
                "assemblyProductUuid" uuid NOT NULL,
                "componentProductUuid" uuid NOT NULL,
                "quantity" integer NOT NULL,
                "isDeleted" boolean NOT NULL DEFAULT false,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_product_structure" PRIMARY KEY ("id")
            )
        `);

        // Create indexes for better query performance
        await queryRunner.query(`CREATE INDEX "IDX_product_structure_warehouse" ON "product_structure" ("warehouseUuid")`);
        await queryRunner.query(`CREATE INDEX "IDX_product_structure_assembly" ON "product_structure" ("assemblyProductUuid")`);
        await queryRunner.query(`CREATE INDEX "IDX_product_structure_component" ON "product_structure" ("componentProductUuid")`);

        // Create unique constraint to prevent duplicate structures (only for non-deleted records)
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_product_structure_unique" 
            ON "product_structure" ("assemblyProductUuid", "componentProductUuid") 
            WHERE "isDeleted" = false
        `);

        // Create foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "product_structure" 
            ADD CONSTRAINT "FK_product_structure_assembly_product" 
            FOREIGN KEY ("assemblyProductUuid") REFERENCES "products"("id") ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "product_structure" 
            ADD CONSTRAINT "FK_product_structure_component_product" 
            FOREIGN KEY ("componentProductUuid") REFERENCES "products"("id") ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign keys first
        await queryRunner.query(`ALTER TABLE "product_structure" DROP CONSTRAINT "FK_product_structure_component_product"`);
        await queryRunner.query(`ALTER TABLE "product_structure" DROP CONSTRAINT "FK_product_structure_assembly_product"`);

        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_product_structure_unique"`);
        await queryRunner.query(`DROP INDEX "IDX_product_structure_component"`);
        await queryRunner.query(`DROP INDEX "IDX_product_structure_assembly"`);
        await queryRunner.query(`DROP INDEX "IDX_product_structure_warehouse"`);

        // Drop table
        await queryRunner.query(`DROP TABLE "product_structure"`);
    }
}
