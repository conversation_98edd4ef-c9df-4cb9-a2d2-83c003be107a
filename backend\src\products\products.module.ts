import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ProductsController } from "./products.controller";
import { ProductsService } from "./products.service.typeorm";
import { Product } from "./product.entity";
import { ProductStructureController } from "./product-structure.controller";
import { ProductStructureService } from "./product-structure.service.typeorm";
import { ProductStructure } from "./product-structure.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, ProductStructure]),
  ],
  controllers: [ProductsController, ProductStructureController],
  providers: [ProductsService, ProductStructureService],
  exports: [ProductsService, ProductStructureService],
})
export class ProductsModule {}
