// ProductModal - A reusable modal component for selecting products
// 
// Features:
// - Search products by name, SKU, or barcode
// - Paginated product list with caching
// - Create new product functionality
// - Responsive design with keyboard navigation
// - Loading states and error handling
// - Debounced search to prevent excessive API calls
//
// Usage:
// import { ProductModal } from '@/components/ProductModal';
//
// <ProductModal
//   isOpen={isModalOpen}
//   onSelect={(product) => {
//     console.log('Selected product:', product);
//     setIsModalOpen(false);
//   }}
//   onClose={() => setIsModalOpen(false)}
//   onCreateNew={(productData) => {
//     console.log('Create new product:', productData);
//     // Handle product creation
//   }}
// />

export { ProductModal } from './ProductModal';

// Export types for external use
export type {
  Product,
  CreateProductDto,
  ProductFilter,
  ProductModalProps,
  ValidationError,
  FormValidationResult,
  PaginatedResponse
} from './types';

// Export API functions for external use
export {
  filterProducts,
  searchProducts,
  createProduct,
  getProduct
} from './api/productApi';

// Export utility functions for external use
export {
  validateProduct,
  formatProductDisplayName,
  formatProductPrice
} from './utils/productHelpers'; 