// DTO utility for ProductStructure output, following UUID_USAGE_GUIDELINES.md
import { ProductStructure } from "../product-structure.entity";
import { ProductDto } from "./product.dto";

export interface ProductStructureDto {
  uuid: string;
  warehouseUuid: string;
  assemblyProductUuid: string;
  componentProductUuid: string;
  quantity: number;
  isDeleted: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  // Optional product details for expanded responses
  assemblyProduct?: ProductDto;
  componentProduct?: ProductDto;
}

export function toProductStructureDto(productStructure: any): ProductStructureDto {
  // Accepts a TypeORM entity or plain object
  return {
    uuid: productStructure.id, // TypeORM uses 'id' as primary key
    warehouseUuid: productStructure.warehouseUuid,
    assemblyProductUuid: productStructure.assemblyProductUuid,
    componentProductUuid: productStructure.componentProductUuid,
    quantity: productStructure.quantity,
    isDeleted: productStructure.isDeleted,
    createdAt: productStructure.createdAt,
    updatedAt: productStructure.updatedAt,
    // Include product details if they were loaded via relations
    assemblyProduct: productStructure.assemblyProduct ? {
      uuid: productStructure.assemblyProduct.id,
      warehouseUuidString: productStructure.assemblyProduct.warehouseUuid,
      productCategoryUuidString: productStructure.assemblyProduct.productCategoryUuid,
      name: productStructure.assemblyProduct.name,
      description: productStructure.assemblyProduct.description ?? "",
      sku: productStructure.assemblyProduct.sku ?? "",
      barcode: productStructure.assemblyProduct.barcode ?? "",
      retailPrice: productStructure.assemblyProduct.retailPrice,
      wholesalePrice: productStructure.assemblyProduct.wholesalePrice,
      midWholesalePrice: productStructure.assemblyProduct.midWholesalePrice,
      institutionalPrice: productStructure.assemblyProduct.institutionalPrice,
      cost: productStructure.assemblyProduct.cost ?? null,
      isDeleted: productStructure.assemblyProduct.isDeleted,
      createdAt: productStructure.assemblyProduct.createdAt,
      updatedAt: productStructure.assemblyProduct.updatedAt,
    } : undefined,
    componentProduct: productStructure.componentProduct ? {
      uuid: productStructure.componentProduct.id,
      warehouseUuidString: productStructure.componentProduct.warehouseUuid,
      productCategoryUuidString: productStructure.componentProduct.productCategoryUuid,
      name: productStructure.componentProduct.name,
      description: productStructure.componentProduct.description ?? "",
      sku: productStructure.componentProduct.sku ?? "",
      barcode: productStructure.componentProduct.barcode ?? "",
      retailPrice: productStructure.componentProduct.retailPrice,
      wholesalePrice: productStructure.componentProduct.wholesalePrice,
      midWholesalePrice: productStructure.componentProduct.midWholesalePrice,
      institutionalPrice: productStructure.componentProduct.institutionalPrice,
      cost: productStructure.componentProduct.cost ?? null,
      isDeleted: productStructure.componentProduct.isDeleted,
      createdAt: productStructure.componentProduct.createdAt,
      updatedAt: productStructure.componentProduct.updatedAt,
    } : undefined,
  };
}

export function toProductStructureDtoArray(productStructures: any[]): ProductStructureDto[] {
  return productStructures.map(toProductStructureDto);
}
