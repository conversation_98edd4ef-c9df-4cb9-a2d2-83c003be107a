"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/products/page",{

/***/ "(app-pages-browser)/./app/inventory/products/PackComponents.tsx":
/*!***************************************************!*\
  !*** ./app/inventory/products/PackComponents.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PackComponents; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _productStructureApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./productStructureApi */ \"(app-pages-browser)/./app/inventory/products/productStructureApi.ts\");\n/* harmony import */ var _components_ProductModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProductModal */ \"(app-pages-browser)/./components/ProductModal/index.ts\");\n// PackComponents.tsx - Component for managing pack components within product form\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction PackComponents(param) {\n    let { warehouseUuid, packProductUuid, components, onComponentsChange, availableProducts, isLoadingProducts } = param;\n    _s();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const [selectedProductUuid, setSelectedProductUuid] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const [quantity, setQuantity] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(1);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const [isProductModalOpen, setIsProductModalOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    // Create product structure mutation\n    const createStructureMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: async (data)=>{\n            if (!packProductUuid) return null;\n            return (0,_productStructureApi__WEBPACK_IMPORTED_MODULE_2__.createProductStructure)({\n                warehouseUuid,\n                assemblyProductUuid: packProductUuid,\n                componentProductUuid: data.componentProductUuid,\n                quantity: data.quantity\n            });\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setError(null);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            setError((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to add component to pack\");\n        }\n    });\n    // Filter out products that are already in the pack\n    const availableProductsForPack = availableProducts.filter((product)=>!components.some((comp)=>comp.productUuid === product.uuid));\n    const handleAddComponent = ()=>{\n        if (!selectedProductUuid || quantity <= 0) {\n            setError(\"Please select a product and enter a valid quantity\");\n            return;\n        }\n        const selectedProduct = availableProducts.find((p)=>p.uuid === selectedProductUuid);\n        if (!selectedProduct) {\n            setError(\"Selected product not found\");\n            return;\n        }\n        const newComponent = {\n            productUuid: selectedProductUuid,\n            productName: selectedProduct.name,\n            productSku: selectedProduct.sku,\n            quantity: quantity\n        };\n        const updatedComponents = [\n            ...components,\n            newComponent\n        ];\n        onComponentsChange(updatedComponents);\n        // If we have a pack product UUID (editing mode), create the structure in backend\n        if (packProductUuid) {\n            createStructureMutation.mutate({\n                componentProductUuid: selectedProductUuid,\n                quantity: quantity\n            });\n        }\n        // Reset form\n        setSelectedProductUuid(\"\");\n        setQuantity(1);\n        setError(null);\n    };\n    const handleRemoveComponent = (index)=>{\n        const updatedComponents = components.filter((_, i)=>i !== index);\n        onComponentsChange(updatedComponents);\n    };\n    const handleQuantityChange = (index, newQuantity)=>{\n        if (newQuantity <= 0) return;\n        const updatedComponents = components.map((comp, i)=>i === index ? {\n                ...comp,\n                quantity: newQuantity\n            } : comp);\n        onComponentsChange(updatedComponents);\n    };\n    // Handle product selection from modal\n    const handleProductSelect = (product)=>{\n        if (product) {\n            // Check if product is already in the pack\n            const isAlreadySelected = components.some((comp)=>comp.productUuid === product.uuid);\n            if (isAlreadySelected) {\n                setError(\"This product is already in the pack\");\n                return;\n            }\n            setSelectedProductUuid(product.uuid);\n            setIsProductModalOpen(false);\n            setError(null);\n        }\n    };\n    // Handle creating new product from modal\n    const handleCreateNewProduct = (productData)=>{\n        // This will be handled by the parent component through the availableProducts\n        // The modal will close and the new product should appear in the list\n        setIsProductModalOpen(false);\n    // Note: The new product will be available in the next render cycle\n    // through the availableProducts prop from the parent component\n    };\n    // Get selected product display name\n    const getSelectedProductDisplay = ()=>{\n        if (!selectedProductUuid) return \"Select a product...\";\n        const selectedProduct = availableProducts.find((p)=>p.uuid === selectedProductUuid);\n        if (!selectedProduct) return \"Product not found...\";\n        return \"\".concat(selectedProduct.name).concat(selectedProduct.sku ? \" (\".concat(selectedProduct.sku, \")\") : \"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"Pack Components\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\n                            components.length,\n                            \" component\",\n                            components.length !== 1 ? \"s\" : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsProductModalOpen(true),\n                                                className: \"flex-1 text-sm border border-gray-300 rounded-md px-3 py-2 text-left bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-400 focus:border-blue-400\",\n                                                disabled: isLoadingProducts,\n                                                children: getSelectedProductDisplay()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedProductUuid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setSelectedProductUuid(\"\"),\n                                                className: \"text-gray-400 hover:text-gray-600 p-2\",\n                                                title: \"Clear selection\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                        children: \"Quantity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"1\",\n                                        value: quantity,\n                                        onChange: (e)=>setQuantity(parseInt(e.target.value) || 1),\n                                        className: \"w-full text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-400 focus:border-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleAddComponent,\n                        disabled: !selectedProductUuid || quantity <= 0 || isLoadingProducts,\n                        className: \"w-full bg-blue-600 text-white text-sm font-medium rounded-md py-2 px-3 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Add to Pack\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-red-600 bg-red-50 p-2 rounded\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModal__WEBPACK_IMPORTED_MODULE_3__.ProductModal, {\n                isOpen: isProductModalOpen,\n                onSelect: handleProductSelect,\n                onClose: ()=>setIsProductModalOpen(false),\n                onCreateNew: handleCreateNewProduct,\n                disabled: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            components.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xs font-medium text-gray-700\",\n                        children: \"Pack Contents:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: components.map((component, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between bg-white border border-gray-200 rounded-md p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: component.productName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this),\n                                            component.productSku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"SKU: \",\n                                                    component.productSku\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: component.quantity,\n                                                onChange: (e)=>handleQuantityChange(index, parseInt(e.target.value) || 1),\n                                                className: \"w-16 text-sm border border-gray-300 rounded px-2 py-1 text-center focus:ring-2 focus:ring-blue-400 focus:border-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleRemoveComponent(index),\n                                                className: \"text-red-600 hover:text-red-800 p-1\",\n                                                title: \"Remove from pack\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, component.productUuid, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this),\n            availableProductsForPack.length === 0 && !isLoadingProducts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 text-center py-4\",\n                children: \"No available products to add to pack\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\inventory\\\\products\\\\PackComponents.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(PackComponents, \"ttZe12LK8uM3120DL96/HltTGiE=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation\n    ];\n});\n_c = PackComponents;\nvar _c;\n$RefreshReg$(_c, \"PackComponents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/inventory/products/PackComponents.tsx\n"));

/***/ })

});