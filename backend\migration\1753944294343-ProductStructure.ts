import { MigrationInterface, QueryRunner } from "typeorm";

export class ProductStructure1753944294343 implements MigrationInterface {
    name = 'ProductStructure1753944294343'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "product_structure" ("id" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "assemblyProductUuid" uuid NOT NULL, "componentProductUuid" uuid NOT NULL, "quantity" integer NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9f9ed904fa5fb8f2360e24e232e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0644f6b2729142468b493ea2dc" ON "product_structure" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_5b0b05aadff070b98d40ffcab2" ON "product_structure" ("assemblyProductUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b98c1c141d4c9e92d923582395" ON "product_structure" ("componentProductUuid") `);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "userUuid" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_structure" ADD CONSTRAINT "FK_5b0b05aadff070b98d40ffcab26" FOREIGN KEY ("assemblyProductUuid") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_structure" ADD CONSTRAINT "FK_b98c1c141d4c9e92d923582395c" FOREIGN KEY ("componentProductUuid") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_structure" DROP CONSTRAINT "FK_b98c1c141d4c9e92d923582395c"`);
        await queryRunner.query(`ALTER TABLE "product_structure" DROP CONSTRAINT "FK_5b0b05aadff070b98d40ffcab26"`);
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "userUuid" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "quotes" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b98c1c141d4c9e92d923582395"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5b0b05aadff070b98d40ffcab2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0644f6b2729142468b493ea2dc"`);
        await queryRunner.query(`DROP TABLE "product_structure"`);
    }

}
