import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
  ParseUUIDPipe,
} from "@nestjs/common";
import { ProductStructureService } from "./product-structure.service.typeorm";
import {
  ApiTags,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
} from "@nestjs/swagger";
import { CreateProductStructureDto } from "./dto/create-product-structure.dto";
import { UpdateProductStructureDto } from "./dto/update-product-structure.dto";
import { toProductStructureDto, toProductStructureDtoArray } from "./dto/product-structure.dto";
import { PaginationQueryDto, PaginatedResponseDto } from "../dto/pagination.dto";

@ApiTags("product-structure")
@Controller("product-structure")
export class ProductStructureController {
  constructor(private readonly productStructureService: ProductStructureService) {}

  @Post()
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({
    summary: "Create a new product structure",
    description: "Creates a new product structure (Bill of Materials entry) with the provided data",
  })
  @ApiBody({ type: CreateProductStructureDto })
  @ApiResponse({ 
    status: 201, 
    description: "Product structure created successfully",
    schema: {
      type: "object",
      properties: {
        uuid: { type: "string", example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e" },
        warehouseUuid: { type: "string", example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e" },
        assemblyProductUuid: { type: "string", example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e" },
        componentProductUuid: { type: "string", example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e" },
        quantity: { type: "number", example: 5 },
        isDeleted: { type: "boolean", example: false },
        createdAt: { type: "string", format: "date-time" },
        updatedAt: { type: "string", format: "date-time" },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid input data or business rule violation" })
  @ApiResponse({ status: 404, description: "Assembly or component product not found" })
  async create(@Body() createProductStructureDto: CreateProductStructureDto) {
    const productStructure = await this.productStructureService.create(createProductStructureDto);
    return toProductStructureDto(productStructure);
  }

  @Get()
  @ApiOperation({
    summary: "Get all product structures with pagination",
    description: "Returns a paginated list of product structures, excluding deleted ones.",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiResponse({ 
    status: 200, 
    description: "Paginated list of product structures.",
    schema: {
      type: "object",
      properties: {
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              warehouseUuid: { type: "string" },
              assemblyProductUuid: { type: "string" },
              componentProductUuid: { type: "string" },
              quantity: { type: "number" },
              assemblyProduct: { type: "object" },
              componentProduct: { type: "object" },
            },
          },
        },
        meta: {
          type: "object",
          properties: {
            total: { type: "number" },
            page: { type: "number" },
            limit: { type: "number" },
            totalPages: { type: "number" },
            hasNext: { type: "boolean" },
            hasPrev: { type: "boolean" },
          },
        },
      },
    },
  })
  async findAll(@Query() paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;
    const result = await this.productStructureService.findAll(page, limit);
    return new PaginatedResponseDto(
      toProductStructureDtoArray(result.data),
      result.total,
      result.page,
      result.limit,
    );
  }

  @Get("warehouse/:warehouseUuid")
  @ApiOperation({
    summary: "Get product structures by warehouse",
    description: "Returns a paginated list of product structures for a specific warehouse.",
  })
  @ApiParam({
    name: "warehouseUuid",
    type: String,
    description: "UUID of the warehouse",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiResponse({ status: 200, description: "Paginated list of product structures for the warehouse." })
  @ApiResponse({ status: 400, description: "Invalid warehouse UUID format" })
  async findByWarehouse(
    @Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string,
    @Query() paginationQuery: PaginationQueryDto,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;
    const result = await this.productStructureService.findByWarehouse(warehouseUuid, page, limit);
    return new PaginatedResponseDto(
      toProductStructureDtoArray(result.data),
      result.total,
      result.page,
      result.limit,
    );
  }

  @Get("assembly/:assemblyProductUuid")
  @ApiOperation({
    summary: "Get product structures by assembly product (Bill of Materials)",
    description: "Returns a paginated list of components for a specific assembly product.",
  })
  @ApiParam({
    name: "assemblyProductUuid",
    type: String,
    description: "UUID of the assembly product",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiResponse({ status: 200, description: "Paginated list of components for the assembly product." })
  @ApiResponse({ status: 400, description: "Invalid assembly product UUID format" })
  async findByAssemblyProduct(
    @Param("assemblyProductUuid", ParseUUIDPipe) assemblyProductUuid: string,
    @Query() paginationQuery: PaginationQueryDto,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;
    const result = await this.productStructureService.findByAssemblyProduct(assemblyProductUuid, page, limit);
    return new PaginatedResponseDto(
      toProductStructureDtoArray(result.data),
      result.total,
      result.page,
      result.limit,
    );
  }

  @Get("component/:componentProductUuid")
  @ApiOperation({
    summary: "Get product structures by component product (Where Used)",
    description: "Returns a paginated list of assemblies that use a specific component product.",
  })
  @ApiParam({
    name: "componentProductUuid",
    type: String,
    description: "UUID of the component product",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiResponse({ status: 200, description: "Paginated list of assemblies that use the component product." })
  @ApiResponse({ status: 400, description: "Invalid component product UUID format" })
  async findByComponentProduct(
    @Param("componentProductUuid", ParseUUIDPipe) componentProductUuid: string,
    @Query() paginationQuery: PaginationQueryDto,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;
    const result = await this.productStructureService.findByComponentProduct(componentProductUuid, page, limit);
    return new PaginatedResponseDto(
      toProductStructureDtoArray(result.data),
      result.total,
      result.page,
      result.limit,
    );
  }

  @Get("bom/:assemblyProductUuid")
  @ApiOperation({
    summary: "Get complete Bill of Materials for an assembly product",
    description: "Returns all components required for a specific assembly product (non-paginated).",
  })
  @ApiParam({
    name: "assemblyProductUuid",
    type: String,
    description: "UUID of the assembly product",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Complete Bill of Materials for the assembly product.",
    schema: {
      type: "object",
      properties: {
        assemblyProductUuid: { type: "string" },
        components: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              componentProductUuid: { type: "string" },
              quantity: { type: "number" },
              componentProduct: { type: "object" },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid assembly product UUID format" })
  async getBillOfMaterials(@Param("assemblyProductUuid", ParseUUIDPipe) assemblyProductUuid: string) {
    const components = await this.productStructureService.getBillOfMaterials(assemblyProductUuid);
    return {
      assemblyProductUuid,
      components: toProductStructureDtoArray(components),
    };
  }

  @Get("where-used/:componentProductUuid")
  @ApiOperation({
    summary: "Get complete Where Used list for a component product",
    description: "Returns all assemblies that use a specific component product (non-paginated).",
  })
  @ApiParam({
    name: "componentProductUuid",
    type: String,
    description: "UUID of the component product",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Complete Where Used list for the component product.",
    schema: {
      type: "object",
      properties: {
        componentProductUuid: { type: "string" },
        assemblies: {
          type: "array",
          items: {
            type: "object",
            properties: {
              uuid: { type: "string" },
              assemblyProductUuid: { type: "string" },
              quantity: { type: "number" },
              assemblyProduct: { type: "object" },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid component product UUID format" })
  async getWhereUsed(@Param("componentProductUuid", ParseUUIDPipe) componentProductUuid: string) {
    const assemblies = await this.productStructureService.getWhereUsed(componentProductUuid);
    return {
      componentProductUuid,
      assemblies: toProductStructureDtoArray(assemblies),
    };
  }

  @Get("count")
  @ApiOperation({
    summary: "Get total count of product structures",
    description: "Returns the total count of non-deleted product structures",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Product structure count",
    schema: {
      type: "object",
      properties: {
        count: { type: "number", example: 150 },
      },
    },
  })
  async count() {
    const count = await this.productStructureService.count();
    return { count };
  }

  @Get(":uuid")
  @ApiOperation({
    summary: "Get a single product structure by UUID",
    description: "Returns a single product structure by its UUID with related product details",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product structure to retrieve",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Product structure found",
    schema: {
      type: "object",
      properties: {
        uuid: { type: "string" },
        warehouseUuid: { type: "string" },
        assemblyProductUuid: { type: "string" },
        componentProductUuid: { type: "string" },
        quantity: { type: "number" },
        assemblyProduct: { type: "object" },
        componentProduct: { type: "object" },
        createdAt: { type: "string", format: "date-time" },
        updatedAt: { type: "string", format: "date-time" },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Product structure not found" })
  @ApiResponse({ status: 400, description: "Invalid UUID format" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string) {
    const productStructure = await this.productStructureService.findOne(uuid);
    return toProductStructureDto(productStructure);
  }

  @Put(":uuid")
  @ApiOperation({
    summary: "Update a product structure",
    description: "Update product structure details by UUID",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product structure to update",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiBody({ type: UpdateProductStructureDto })
  @ApiResponse({
    status: 200,
    description: "Product structure updated successfully",
    schema: {
      type: "object",
      properties: {
        uuid: { type: "string" },
        warehouseUuid: { type: "string" },
        assemblyProductUuid: { type: "string" },
        componentProductUuid: { type: "string" },
        quantity: { type: "number" },
        isDeleted: { type: "boolean" },
        createdAt: { type: "string", format: "date-time" },
        updatedAt: { type: "string", format: "date-time" },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Bad request - invalid data or UUID format" })
  @ApiResponse({ status: 404, description: "Product structure not found" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateProductStructureDto: UpdateProductStructureDto,
  ) {
    const productStructure = await this.productStructureService.update(uuid, updateProductStructureDto);
    return toProductStructureDto(productStructure);
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete a product structure",
    description: "Marks a product structure as deleted without removing it from the database",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product structure to delete",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Product structure soft deleted successfully",
    schema: {
      type: "object",
      properties: {
        uuid: { type: "string" },
        isDeleted: { type: "boolean", example: true },
        updatedAt: { type: "string", format: "date-time" },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Product structure not found" })
  @ApiResponse({ status: 400, description: "Invalid UUID format" })
  async softDelete(@Param("uuid", ParseUUIDPipe) uuid: string) {
    const productStructure = await this.productStructureService.softDelete(uuid);
    return toProductStructureDto(productStructure);
  }
}
