{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.es2021.d.ts", "./node_modules/typescript/lib/lib.es2022.d.ts", "./node_modules/typescript/lib/lib.es2023.d.ts", "./node_modules/typescript/lib/lib.es2024.d.ts", "./node_modules/typescript/lib/lib.esnext.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.es2021.promise.d.ts", "./node_modules/typescript/lib/lib.es2021.string.d.ts", "./node_modules/typescript/lib/lib.es2021.weakref.d.ts", "./node_modules/typescript/lib/lib.es2021.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.array.d.ts", "./node_modules/typescript/lib/lib.es2022.error.d.ts", "./node_modules/typescript/lib/lib.es2022.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.object.d.ts", "./node_modules/typescript/lib/lib.es2022.string.d.ts", "./node_modules/typescript/lib/lib.es2022.regexp.d.ts", "./node_modules/typescript/lib/lib.es2023.array.d.ts", "./node_modules/typescript/lib/lib.es2023.collection.d.ts", "./node_modules/typescript/lib/lib.es2023.intl.d.ts", "./node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2024.collection.d.ts", "./node_modules/typescript/lib/lib.es2024.object.d.ts", "./node_modules/typescript/lib/lib.es2024.promise.d.ts", "./node_modules/typescript/lib/lib.es2024.regexp.d.ts", "./node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2024.string.d.ts", "./node_modules/typescript/lib/lib.esnext.array.d.ts", "./node_modules/typescript/lib/lib.esnext.collection.d.ts", "./node_modules/typescript/lib/lib.esnext.intl.d.ts", "./node_modules/typescript/lib/lib.esnext.disposable.d.ts", "./node_modules/typescript/lib/lib.esnext.promise.d.ts", "./node_modules/typescript/lib/lib.esnext.decorators.d.ts", "./node_modules/typescript/lib/lib.esnext.iterator.d.ts", "./node_modules/typescript/lib/lib.esnext.float16.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/next/dist/styled-jsx/types/css.d.ts", "./node_modules/@types/react/global.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@types/prop-types/index.d.ts", "./node_modules/@types/react/index.d.ts", "./node_modules/next/dist/styled-jsx/types/index.d.ts", "./node_modules/next/dist/styled-jsx/types/macro.d.ts", "./node_modules/next/dist/styled-jsx/types/style.d.ts", "./node_modules/next/dist/styled-jsx/types/global.d.ts", "./node_modules/next/dist/shared/lib/amp.d.ts", "./node_modules/next/amp.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/next/dist/server/get-page-files.d.ts", "./node_modules/@types/react/canary.d.ts", "./node_modules/@types/react/experimental.d.ts", "./node_modules/@types/react-dom/index.d.ts", "./node_modules/@types/react-dom/canary.d.ts", "./node_modules/@types/react-dom/experimental.d.ts", "./node_modules/next/dist/compiled/webpack/webpack.d.ts", "./node_modules/next/dist/server/config.d.ts", "./node_modules/next/dist/lib/load-custom-routes.d.ts", "./node_modules/next/dist/shared/lib/image-config.d.ts", "./node_modules/next/dist/build/webpack/plugins/subresource-integrity-plugin.d.ts", "./node_modules/next/dist/server/body-streams.d.ts", "./node_modules/next/dist/server/future/route-kind.d.ts", "./node_modules/next/dist/server/future/route-definitions/route-definition.d.ts", "./node_modules/next/dist/server/future/route-matches/route-match.d.ts", "./node_modules/next/dist/client/components/app-router-headers.d.ts", "./node_modules/next/dist/server/request-meta.d.ts", "./node_modules/next/dist/server/lib/revalidate.d.ts", "./node_modules/next/dist/server/config-shared.d.ts", "./node_modules/next/dist/server/base-http/index.d.ts", "./node_modules/next/dist/server/api-utils/index.d.ts", "./node_modules/next/dist/server/node-environment.d.ts", "./node_modules/next/dist/server/require-hook.d.ts", "./node_modules/next/dist/server/node-polyfill-crypto.d.ts", "./node_modules/next/dist/lib/page-types.d.ts", "./node_modules/next/dist/build/analysis/get-page-static-info.d.ts", "./node_modules/next/dist/build/webpack/loaders/get-module-build-info.d.ts", "./node_modules/next/dist/build/webpack/plugins/middleware-plugin.d.ts", "./node_modules/next/dist/server/render-result.d.ts", "./node_modules/next/dist/server/future/helpers/i18n-provider.d.ts", "./node_modules/next/dist/server/web/next-url.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/cookies/index.d.ts", "./node_modules/next/dist/server/web/spec-extension/cookies.d.ts", "./node_modules/next/dist/server/web/spec-extension/request.d.ts", "./node_modules/next/dist/server/web/spec-extension/fetch-event.d.ts", "./node_modules/next/dist/server/web/spec-extension/response.d.ts", "./node_modules/next/dist/server/web/types.d.ts", "./node_modules/next/dist/lib/setup-exception-listeners.d.ts", "./node_modules/next/dist/lib/constants.d.ts", "./node_modules/next/dist/build/index.d.ts", "./node_modules/next/dist/build/webpack/plugins/pages-manifest-plugin.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-regex.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-matcher.d.ts", "./node_modules/next/dist/shared/lib/router/utils/parse-url.d.ts", "./node_modules/next/dist/server/base-http/node.d.ts", "./node_modules/next/dist/server/font-utils.d.ts", "./node_modules/next/dist/build/webpack/plugins/flight-manifest-plugin.d.ts", "./node_modules/next/dist/server/future/route-modules/route-module.d.ts", "./node_modules/next/dist/shared/lib/deep-readonly.d.ts", "./node_modules/next/dist/server/load-components.d.ts", "./node_modules/next/dist/shared/lib/router/utils/middleware-route-matcher.d.ts", "./node_modules/next/dist/build/webpack/plugins/next-font-manifest-plugin.d.ts", "./node_modules/next/dist/server/future/route-definitions/locale-route-definition.d.ts", "./node_modules/next/dist/server/future/route-definitions/pages-route-definition.d.ts", "./node_modules/next/dist/shared/lib/mitt.d.ts", "./node_modules/next/dist/client/with-router.d.ts", "./node_modules/next/dist/client/router.d.ts", "./node_modules/next/dist/client/route-loader.d.ts", "./node_modules/next/dist/client/page-loader.d.ts", "./node_modules/next/dist/shared/lib/bloom-filter.d.ts", "./node_modules/next/dist/shared/lib/router/router.d.ts", "./node_modules/next/dist/shared/lib/router-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-definitions/app-page-route-definition.d.ts", "./node_modules/next/dist/shared/lib/modern-browserslist-target.d.ts", "./node_modules/next/dist/shared/lib/constants.d.ts", "./node_modules/next/dist/build/webpack/loaders/metadata/types.d.ts", "./node_modules/next/dist/build/page-extensions-type.d.ts", "./node_modules/next/dist/build/webpack/loaders/next-app-loader.d.ts", "./node_modules/next/dist/server/lib/app-dir-module.d.ts", "./node_modules/next/dist/server/response-cache/types.d.ts", "./node_modules/next/dist/server/response-cache/index.d.ts", "./node_modules/next/dist/server/lib/incremental-cache/index.d.ts", "./node_modules/next/dist/client/components/hooks-server-context.d.ts", "./node_modules/next/dist/server/app-render/dynamic-rendering.d.ts", "./node_modules/next/dist/client/components/static-generation-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/static-generation-async-storage.external.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.d.ts", "./node_modules/next/dist/server/async-storage/draft-mode-provider.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/headers.d.ts", "./node_modules/next/dist/client/components/request-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/request-async-storage.external.d.ts", "./node_modules/next/dist/server/app-render/create-error-handler.d.ts", "./node_modules/next/dist/server/app-render/app-render.d.ts", "./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/amp-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.d.ts", "./node_modules/@types/react/jsx-runtime.d.ts", "./node_modules/next/dist/client/components/error-boundary.d.ts", "./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.d.ts", "./node_modules/next/dist/client/components/app-router.d.ts", "./node_modules/next/dist/client/components/layout-router.d.ts", "./node_modules/next/dist/client/components/render-from-template-context.d.ts", "./node_modules/next/dist/client/components/action-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/action-async-storage.external.d.ts", "./node_modules/next/dist/client/components/client-page.d.ts", "./node_modules/next/dist/client/components/search-params.d.ts", "./node_modules/next/dist/client/components/not-found-boundary.d.ts", "./node_modules/next/dist/server/app-render/rsc/preloads.d.ts", "./node_modules/next/dist/server/app-render/rsc/postpone.d.ts", "./node_modules/next/dist/server/app-render/rsc/taint.d.ts", "./node_modules/next/dist/server/app-render/entry-base.d.ts", "./node_modules/next/dist/build/templates/app-page.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/module.d.ts", "./node_modules/next/dist/server/lib/builtin-request-context.d.ts", "./node_modules/next/dist/server/app-render/types.d.ts", "./node_modules/next/dist/client/components/router-reducer/fetch-server-response.d.ts", "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.d.ts", "./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/module.compiled.d.ts", "./node_modules/next/dist/build/templates/pages.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/module.d.ts", "./node_modules/next/dist/server/render.d.ts", "./node_modules/next/dist/server/future/route-definitions/pages-api-route-definition.d.ts", "./node_modules/next/dist/server/future/route-matches/pages-api-route-match.d.ts", "./node_modules/next/dist/server/future/route-matchers/route-matcher.d.ts", "./node_modules/next/dist/server/future/route-matcher-providers/route-matcher-provider.d.ts", "./node_modules/next/dist/server/future/route-matcher-managers/route-matcher-manager.d.ts", "./node_modules/next/dist/server/future/normalizers/normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/locale-route-normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/request/pathname-normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/request/suffix.d.ts", "./node_modules/next/dist/server/future/normalizers/request/rsc.d.ts", "./node_modules/next/dist/server/future/normalizers/request/prefix.d.ts", "./node_modules/next/dist/server/future/normalizers/request/postponed.d.ts", "./node_modules/next/dist/server/future/normalizers/request/action.d.ts", "./node_modules/next/dist/server/future/normalizers/request/prefetch-rsc.d.ts", "./node_modules/next/dist/server/future/normalizers/request/next-data.d.ts", "./node_modules/next/dist/server/base-server.d.ts", "./node_modules/next/dist/server/image-optimizer.d.ts", "./node_modules/next/dist/server/next-server.d.ts", "./node_modules/next/dist/lib/coalesced-function.d.ts", "./node_modules/next/dist/server/lib/router-utils/types.d.ts", "./node_modules/next/dist/trace/types.d.ts", "./node_modules/next/dist/trace/trace.d.ts", "./node_modules/next/dist/trace/shared.d.ts", "./node_modules/next/dist/trace/index.d.ts", "./node_modules/next/dist/build/load-jsconfig.d.ts", "./node_modules/next/dist/build/webpack-config.d.ts", "./node_modules/next/dist/build/webpack/plugins/define-env-plugin.d.ts", "./node_modules/next/dist/build/swc/index.d.ts", "./node_modules/next/dist/server/dev/parse-version-info.d.ts", "./node_modules/next/dist/server/dev/hot-reloader-types.d.ts", "./node_modules/next/dist/telemetry/storage.d.ts", "./node_modules/next/dist/server/lib/types.d.ts", "./node_modules/next/dist/server/lib/render-server.d.ts", "./node_modules/next/dist/server/lib/router-server.d.ts", "./node_modules/next/dist/shared/lib/router/utils/path-match.d.ts", "./node_modules/next/dist/server/lib/router-utils/filesystem.d.ts", "./node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.d.ts", "./node_modules/next/dist/server/lib/dev-bundler-service.d.ts", "./node_modules/next/dist/server/dev/static-paths-worker.d.ts", "./node_modules/next/dist/server/dev/next-dev-server.d.ts", "./node_modules/next/dist/server/next.d.ts", "./node_modules/next/dist/lib/metadata/types/alternative-urls-types.d.ts", "./node_modules/next/dist/lib/metadata/types/extra-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-types.d.ts", "./node_modules/next/dist/lib/metadata/types/manifest-types.d.ts", "./node_modules/next/dist/lib/metadata/types/opengraph-types.d.ts", "./node_modules/next/dist/lib/metadata/types/twitter-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-interface.d.ts", "./node_modules/next/types/index.d.ts", "./node_modules/next/dist/shared/lib/html-context.shared-runtime.d.ts", "./node_modules/@next/env/dist/index.d.ts", "./node_modules/next/dist/shared/lib/utils.d.ts", "./node_modules/next/dist/pages/_app.d.ts", "./node_modules/next/app.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-cache.d.ts", "./node_modules/next/dist/server/web/spec-extension/revalidate.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-no-store.d.ts", "./node_modules/next/cache.d.ts", "./node_modules/next/dist/shared/lib/runtime-config.external.d.ts", "./node_modules/next/config.d.ts", "./node_modules/next/dist/pages/_document.d.ts", "./node_modules/next/document.d.ts", "./node_modules/next/dist/shared/lib/dynamic.d.ts", "./node_modules/next/dynamic.d.ts", "./node_modules/next/dist/pages/_error.d.ts", "./node_modules/next/error.d.ts", "./node_modules/next/dist/shared/lib/head.d.ts", "./node_modules/next/head.d.ts", "./node_modules/next/dist/client/components/draft-mode.d.ts", "./node_modules/next/dist/client/components/headers.d.ts", "./node_modules/next/headers.d.ts", "./node_modules/next/dist/shared/lib/get-img-props.d.ts", "./node_modules/next/dist/client/image-component.d.ts", "./node_modules/next/dist/shared/lib/image-external.d.ts", "./node_modules/next/image.d.ts", "./node_modules/next/dist/client/link.d.ts", "./node_modules/next/link.d.ts", "./node_modules/next/dist/client/components/redirect-status-code.d.ts", "./node_modules/next/dist/client/components/redirect.d.ts", "./node_modules/next/dist/client/components/not-found.d.ts", "./node_modules/next/dist/client/components/navigation.react-server.d.ts", "./node_modules/next/dist/client/components/navigation.d.ts", "./node_modules/next/navigation.d.ts", "./node_modules/next/router.d.ts", "./node_modules/next/dist/client/script.d.ts", "./node_modules/next/script.d.ts", "./node_modules/next/dist/server/web/spec-extension/user-agent.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/primitives/url.d.ts", "./node_modules/next/dist/server/web/spec-extension/image-response.d.ts", "./node_modules/next/dist/compiled/@vercel/og/satori/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/emoji/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/types.d.ts", "./node_modules/next/server.d.ts", "./node_modules/next/types/global.d.ts", "./node_modules/next/types/compiled.d.ts", "./node_modules/next/index.d.ts", "./node_modules/next/image-types/global.d.ts", "./next-env.d.ts", "./app/api/[...path]/route.ts", "./app/auth/authapi.ts", "./node_modules/axios/index.d.ts", "./utils/authheaders.ts", "./app/dashboard/dashboardapi.ts", "./node_modules/@tanstack/query-core/build/modern/removable.d.ts", "./node_modules/@tanstack/query-core/build/modern/subscribable.d.ts", "./node_modules/@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "./node_modules/@tanstack/query-core/build/modern/queriesobserver.d.ts", "./node_modules/@tanstack/query-core/build/modern/infinitequeryobserver.d.ts", "./node_modules/@tanstack/query-core/build/modern/notifymanager.d.ts", "./node_modules/@tanstack/query-core/build/modern/focusmanager.d.ts", "./node_modules/@tanstack/query-core/build/modern/onlinemanager.d.ts", "./node_modules/@tanstack/query-core/build/modern/streamedquery.d.ts", "./node_modules/@tanstack/query-core/build/modern/index.d.ts", "./node_modules/@tanstack/react-query/build/modern/types.d.ts", "./node_modules/@tanstack/react-query/build/modern/usequeries.d.ts", "./node_modules/@tanstack/react-query/build/modern/queryoptions.d.ts", "./node_modules/@tanstack/react-query/build/modern/usequery.d.ts", "./node_modules/@tanstack/react-query/build/modern/usesuspensequery.d.ts", "./node_modules/@tanstack/react-query/build/modern/usesuspenseinfinitequery.d.ts", "./node_modules/@tanstack/react-query/build/modern/usesuspensequeries.d.ts", "./node_modules/@tanstack/react-query/build/modern/useprefetchquery.d.ts", "./node_modules/@tanstack/react-query/build/modern/useprefetchinfinitequery.d.ts", "./node_modules/@tanstack/react-query/build/modern/infinitequeryoptions.d.ts", "./node_modules/@tanstack/react-query/build/modern/queryclientprovider.d.ts", "./node_modules/@tanstack/react-query/build/modern/queryerrorresetboundary.d.ts", "./node_modules/@tanstack/react-query/build/modern/hydrationboundary.d.ts", "./node_modules/@tanstack/react-query/build/modern/useisfetching.d.ts", "./node_modules/@tanstack/react-query/build/modern/usemutationstate.d.ts", "./node_modules/@tanstack/react-query/build/modern/usemutation.d.ts", "./node_modules/@tanstack/react-query/build/modern/useinfinitequery.d.ts", "./node_modules/@tanstack/react-query/build/modern/isrestoringprovider.d.ts", "./node_modules/@tanstack/react-query/build/modern/index.d.ts", "./contexts/authcontext.tsx", "./app/dashboard/hooks/usedashboarddata.ts", "./app/inventory/products/productcategoriesapi.ts", "./app/inventory/products/productsapi.ts", "./app/inventory/stock/adjustments/api.ts", "./app/inventory/stock-levels/api.ts", "./app/inventory/stock-levels/hooks/usestocklevelsdata.ts", "./app/logistics/regions/api.ts", "./app/logistics/routes/api.ts", "./app/logistics/vans/vansapi.ts", "./app/logs/logsapi.ts", "./app/logs/components/entitydisplayconfig.ts", "./app/logs/components/examples/newentityexample.ts", "./app/purchase/orders/salesapi.ts", "./app/purchase/orders/saleshelpers.ts", "./node_modules/react-icons/lib/iconsmanifest.d.ts", "./node_modules/react-icons/lib/iconbase.d.ts", "./node_modules/react-icons/lib/iconcontext.d.ts", "./node_modules/react-icons/lib/index.d.ts", "./node_modules/react-icons/fi/index.d.ts", "./app/purchase/orders/components/salesheader.tsx", "./components/customermodal/types/index.ts", "./components/customermodal/api/customerapi.ts", "./components/customermodal/hooks/usecustomerdata.ts", "./components/customermodal/utils/customerhelpers.ts", "./components/customermodal/styles/customermodalstyles.ts", "./components/customermodal/customermodal.tsx", "./components/customermodal/index.ts", "./app/purchase/orders/components/salesfilters.tsx", "./node_modules/lucide-react/dist/lucide-react.d.ts", "./components/itemstable/itemstable.tsx", "./components/itemstable/tableactionbuttons.tsx", "./app/purchase/orders/pos/types/index.ts", "./app/purchase/orders/pos/utils/poshelpers.ts", "./app/purchase/orders/components/saleslistview.tsx", "./app/purchase/orders/components/saledetailsmodal.tsx", "./app/purchase/orders/components/salescancelmodal.tsx", "./node_modules/sonner/dist/index.d.mts", "./app/purchase/orders/pos/styles/posstyles.ts", "./app/purchase/orders/pos/components/searchbar.tsx", "./app/purchase/orders/pos/components/productlist.tsx", "./app/purchase/orders/pos/components/paymentselector.tsx", "./app/purchase/orders/pos/components/taxcontrols.tsx", "./app/purchase/orders/pos/components/notescontrols.tsx", "./app/purchase/orders/pos/components/salescart.tsx", "./app/purchase/orders/pos/components/quantitymodal.tsx", "./node_modules/@heroicons/react/24/outline/academiccapicon.d.ts", "./node_modules/@heroicons/react/24/outline/adjustmentshorizontalicon.d.ts", "./node_modules/@heroicons/react/24/outline/adjustmentsverticalicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxxmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdowncircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownonsquarestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownonsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdowntrayicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftendonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlonglefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowpathroundedsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowpathicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightendonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmalldownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmalllefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmallrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmallupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtoprightonsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtrendingdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtrendingupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturndownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturndownrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnleftdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnleftupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnrightdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnrightupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnuplefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowupcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuplefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuponsquarestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuponsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuptrayicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturndownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowspointinginicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowspointingouticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsrightlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsupdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/atsymbolicon.d.ts", "./node_modules/@heroicons/react/24/outline/backspaceicon.d.ts", "./node_modules/@heroicons/react/24/outline/backwardicon.d.ts", "./node_modules/@heroicons/react/24/outline/banknotesicon.d.ts", "./node_modules/@heroicons/react/24/outline/bars2icon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3bottomlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3bottomrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3centerlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3icon.d.ts", "./node_modules/@heroicons/react/24/outline/bars4icon.d.ts", "./node_modules/@heroicons/react/24/outline/barsarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/barsarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/battery0icon.d.ts", "./node_modules/@heroicons/react/24/outline/battery100icon.d.ts", "./node_modules/@heroicons/react/24/outline/battery50icon.d.ts", "./node_modules/@heroicons/react/24/outline/beakericon.d.ts", "./node_modules/@heroicons/react/24/outline/bellalerticon.d.ts", "./node_modules/@heroicons/react/24/outline/bellslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bellsnoozeicon.d.ts", "./node_modules/@heroicons/react/24/outline/bellicon.d.ts", "./node_modules/@heroicons/react/24/outline/boldicon.d.ts", "./node_modules/@heroicons/react/24/outline/boltslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bolticon.d.ts", "./node_modules/@heroicons/react/24/outline/bookopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarkslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarksquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/briefcaseicon.d.ts", "./node_modules/@heroicons/react/24/outline/buganticon.d.ts", "./node_modules/@heroicons/react/24/outline/buildinglibraryicon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingoffice2icon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingofficeicon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingstorefronticon.d.ts", "./node_modules/@heroicons/react/24/outline/cakeicon.d.ts", "./node_modules/@heroicons/react/24/outline/calculatoricon.d.ts", "./node_modules/@heroicons/react/24/outline/calendardaterangeicon.d.ts", "./node_modules/@heroicons/react/24/outline/calendardaysicon.d.ts", "./node_modules/@heroicons/react/24/outline/calendaricon.d.ts", "./node_modules/@heroicons/react/24/outline/cameraicon.d.ts", "./node_modules/@heroicons/react/24/outline/chartbarsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/chartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/chartpieicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblebottomcentertexticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblebottomcentericon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleleftellipsisicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleleftrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblelefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleovalleftellipsisicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleovallefticon.d.ts", "./node_modules/@heroicons/react/24/outline/checkbadgeicon.d.ts", "./node_modules/@heroicons/react/24/outline/checkcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/checkicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoubledownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoublelefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoublerighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoubleupicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronupdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronupicon.d.ts", "./node_modules/@heroicons/react/24/outline/circlestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumentcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumentlisticon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumenticon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboardicon.d.ts", "./node_modules/@heroicons/react/24/outline/clockicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudicon.d.ts", "./node_modules/@heroicons/react/24/outline/codebracketsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/codebracketicon.d.ts", "./node_modules/@heroicons/react/24/outline/cog6toothicon.d.ts", "./node_modules/@heroicons/react/24/outline/cog8toothicon.d.ts", "./node_modules/@heroicons/react/24/outline/cogicon.d.ts", "./node_modules/@heroicons/react/24/outline/commandlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/computerdesktopicon.d.ts", "./node_modules/@heroicons/react/24/outline/cpuchipicon.d.ts", "./node_modules/@heroicons/react/24/outline/creditcardicon.d.ts", "./node_modules/@heroicons/react/24/outline/cubetransparenticon.d.ts", "./node_modules/@heroicons/react/24/outline/cubeicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencydollaricon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyeuroicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencypoundicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyrupeeicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyyenicon.d.ts", "./node_modules/@heroicons/react/24/outline/cursorarrowraysicon.d.ts", "./node_modules/@heroicons/react/24/outline/cursorarrowrippleicon.d.ts", "./node_modules/@heroicons/react/24/outline/devicephonemobileicon.d.ts", "./node_modules/@heroicons/react/24/outline/devicetableticon.d.ts", "./node_modules/@heroicons/react/24/outline/divideicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentchartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencydollaricon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyeuroicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencypoundicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyrupeeicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyyenicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentduplicateicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentmagnifyingglassicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/documenttexticon.d.ts", "./node_modules/@heroicons/react/24/outline/documenticon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsishorizontalcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsishorizontalicon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsisverticalicon.d.ts", "./node_modules/@heroicons/react/24/outline/envelopeopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/envelopeicon.d.ts", "./node_modules/@heroicons/react/24/outline/equalsicon.d.ts", "./node_modules/@heroicons/react/24/outline/exclamationcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/exclamationtriangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/eyedroppericon.d.ts", "./node_modules/@heroicons/react/24/outline/eyeslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/eyeicon.d.ts", "./node_modules/@heroicons/react/24/outline/facefrownicon.d.ts", "./node_modules/@heroicons/react/24/outline/facesmileicon.d.ts", "./node_modules/@heroicons/react/24/outline/filmicon.d.ts", "./node_modules/@heroicons/react/24/outline/fingerprinticon.d.ts", "./node_modules/@heroicons/react/24/outline/fireicon.d.ts", "./node_modules/@heroicons/react/24/outline/flagicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/foldericon.d.ts", "./node_modules/@heroicons/react/24/outline/forwardicon.d.ts", "./node_modules/@heroicons/react/24/outline/funnelicon.d.ts", "./node_modules/@heroicons/react/24/outline/gificon.d.ts", "./node_modules/@heroicons/react/24/outline/gifttopicon.d.ts", "./node_modules/@heroicons/react/24/outline/gifticon.d.ts", "./node_modules/@heroicons/react/24/outline/globealticon.d.ts", "./node_modules/@heroicons/react/24/outline/globeamericasicon.d.ts", "./node_modules/@heroicons/react/24/outline/globeasiaaustraliaicon.d.ts", "./node_modules/@heroicons/react/24/outline/globeeuropeafricaicon.d.ts", "./node_modules/@heroicons/react/24/outline/h1icon.d.ts", "./node_modules/@heroicons/react/24/outline/h2icon.d.ts", "./node_modules/@heroicons/react/24/outline/h3icon.d.ts", "./node_modules/@heroicons/react/24/outline/handraisedicon.d.ts", "./node_modules/@heroicons/react/24/outline/handthumbdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/handthumbupicon.d.ts", "./node_modules/@heroicons/react/24/outline/hashtagicon.d.ts", "./node_modules/@heroicons/react/24/outline/hearticon.d.ts", "./node_modules/@heroicons/react/24/outline/homemodernicon.d.ts", "./node_modules/@heroicons/react/24/outline/homeicon.d.ts", "./node_modules/@heroicons/react/24/outline/identificationicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxstackicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxicon.d.ts", "./node_modules/@heroicons/react/24/outline/informationcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/italicicon.d.ts", "./node_modules/@heroicons/react/24/outline/keyicon.d.ts", "./node_modules/@heroicons/react/24/outline/languageicon.d.ts", "./node_modules/@heroicons/react/24/outline/lifebuoyicon.d.ts", "./node_modules/@heroicons/react/24/outline/lightbulbicon.d.ts", "./node_modules/@heroicons/react/24/outline/linkslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/linkicon.d.ts", "./node_modules/@heroicons/react/24/outline/listbulleticon.d.ts", "./node_modules/@heroicons/react/24/outline/lockclosedicon.d.ts", "./node_modules/@heroicons/react/24/outline/lockopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglasscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassicon.d.ts", "./node_modules/@heroicons/react/24/outline/mappinicon.d.ts", "./node_modules/@heroicons/react/24/outline/mapicon.d.ts", "./node_modules/@heroicons/react/24/outline/megaphoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/microphoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/minuscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/minussmallicon.d.ts", "./node_modules/@heroicons/react/24/outline/minusicon.d.ts", "./node_modules/@heroicons/react/24/outline/moonicon.d.ts", "./node_modules/@heroicons/react/24/outline/musicalnoteicon.d.ts", "./node_modules/@heroicons/react/24/outline/newspapericon.d.ts", "./node_modules/@heroicons/react/24/outline/nosymbolicon.d.ts", "./node_modules/@heroicons/react/24/outline/numberedlisticon.d.ts", "./node_modules/@heroicons/react/24/outline/paintbrushicon.d.ts", "./node_modules/@heroicons/react/24/outline/paperairplaneicon.d.ts", "./node_modules/@heroicons/react/24/outline/paperclipicon.d.ts", "./node_modules/@heroicons/react/24/outline/pausecircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/pauseicon.d.ts", "./node_modules/@heroicons/react/24/outline/pencilsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/pencilicon.d.ts", "./node_modules/@heroicons/react/24/outline/percentbadgeicon.d.ts", "./node_modules/@heroicons/react/24/outline/phonearrowdownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/phonearrowuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/phonexmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/phoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/photoicon.d.ts", "./node_modules/@heroicons/react/24/outline/playcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/playpauseicon.d.ts", "./node_modules/@heroicons/react/24/outline/playicon.d.ts", "./node_modules/@heroicons/react/24/outline/pluscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/plussmallicon.d.ts", "./node_modules/@heroicons/react/24/outline/plusicon.d.ts", "./node_modules/@heroicons/react/24/outline/powericon.d.ts", "./node_modules/@heroicons/react/24/outline/presentationchartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/presentationchartlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/printericon.d.ts", "./node_modules/@heroicons/react/24/outline/puzzlepieceicon.d.ts", "./node_modules/@heroicons/react/24/outline/qrcodeicon.d.ts", "./node_modules/@heroicons/react/24/outline/questionmarkcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/queuelisticon.d.ts", "./node_modules/@heroicons/react/24/outline/radioicon.d.ts", "./node_modules/@heroicons/react/24/outline/receiptpercenticon.d.ts", "./node_modules/@heroicons/react/24/outline/receiptrefundicon.d.ts", "./node_modules/@heroicons/react/24/outline/rectanglegroupicon.d.ts", "./node_modules/@heroicons/react/24/outline/rectanglestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/rocketlaunchicon.d.ts", "./node_modules/@heroicons/react/24/outline/rssicon.d.ts", "./node_modules/@heroicons/react/24/outline/scaleicon.d.ts", "./node_modules/@heroicons/react/24/outline/scissorsicon.d.ts", "./node_modules/@heroicons/react/24/outline/serverstackicon.d.ts", "./node_modules/@heroicons/react/24/outline/servericon.d.ts", "./node_modules/@heroicons/react/24/outline/shareicon.d.ts", "./node_modules/@heroicons/react/24/outline/shieldcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/shieldexclamationicon.d.ts", "./node_modules/@heroicons/react/24/outline/shoppingbagicon.d.ts", "./node_modules/@heroicons/react/24/outline/shoppingcarticon.d.ts", "./node_modules/@heroicons/react/24/outline/signalslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/signalicon.d.ts", "./node_modules/@heroicons/react/24/outline/slashicon.d.ts", "./node_modules/@heroicons/react/24/outline/sparklesicon.d.ts", "./node_modules/@heroicons/react/24/outline/speakerwaveicon.d.ts", "./node_modules/@heroicons/react/24/outline/speakerxmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/square2stackicon.d.ts", "./node_modules/@heroicons/react/24/outline/square3stack3dicon.d.ts", "./node_modules/@heroicons/react/24/outline/squares2x2icon.d.ts", "./node_modules/@heroicons/react/24/outline/squaresplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/staricon.d.ts", "./node_modules/@heroicons/react/24/outline/stopcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/stopicon.d.ts", "./node_modules/@heroicons/react/24/outline/strikethroughicon.d.ts", "./node_modules/@heroicons/react/24/outline/sunicon.d.ts", "./node_modules/@heroicons/react/24/outline/swatchicon.d.ts", "./node_modules/@heroicons/react/24/outline/tablecellsicon.d.ts", "./node_modules/@heroicons/react/24/outline/tagicon.d.ts", "./node_modules/@heroicons/react/24/outline/ticketicon.d.ts", "./node_modules/@heroicons/react/24/outline/trashicon.d.ts", "./node_modules/@heroicons/react/24/outline/trophyicon.d.ts", "./node_modules/@heroicons/react/24/outline/truckicon.d.ts", "./node_modules/@heroicons/react/24/outline/tvicon.d.ts", "./node_modules/@heroicons/react/24/outline/underlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/usercircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/usergroupicon.d.ts", "./node_modules/@heroicons/react/24/outline/userminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/userplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/usericon.d.ts", "./node_modules/@heroicons/react/24/outline/usersicon.d.ts", "./node_modules/@heroicons/react/24/outline/variableicon.d.ts", "./node_modules/@heroicons/react/24/outline/videocameraslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/videocameraicon.d.ts", "./node_modules/@heroicons/react/24/outline/viewcolumnsicon.d.ts", "./node_modules/@heroicons/react/24/outline/viewfindercircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/walleticon.d.ts", "./node_modules/@heroicons/react/24/outline/wifiicon.d.ts", "./node_modules/@heroicons/react/24/outline/windowicon.d.ts", "./node_modules/@heroicons/react/24/outline/wrenchscrewdrivericon.d.ts", "./node_modules/@heroicons/react/24/outline/wrenchicon.d.ts", "./node_modules/@heroicons/react/24/outline/xcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/xmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/index.d.ts", "./app/purchase/orders/pos/components/pagination.tsx", "./app/purchase/orders/pos/components/index.ts", "./app/purchase/orders/pos/config/posconfig.ts", "./app/purchase/orders/pos/components/categoryfilter.tsx", "./app/purchase/orders/pos/hooks/usekeyboardnavigation.ts", "./app/purchase/orders/pos/hooks/useposstate.ts", "./app/settings/accountsettingsapi.ts", "./app/purchase/orders/pos/posapi.ts", "./app/purchase/orders/pos/hooks/useposoperations.ts", "./app/settings/warehousesapi.ts", "./app/purchase/orders/pos/hooks/useposproducts.ts", "./app/purchase/orders/pos/hooks/index.ts", "./app/purchase/orders/components/posview.tsx", "./app/purchase/orders/components/poscomponent.tsx", "./app/purchase/orders/components/listcomponent.tsx", "./app/purchase/purchase/purchaseapi.ts", "./app/purchase/components/invoiceprint.tsx", "./app/purchase/orders/components/index.ts", "./app/purchase/orders/hooks/usesalesdata.ts", "./app/settings/companiesapi.ts", "./app/purchase/orders/hooks/usesalesactions.ts", "./app/purchase/orders/hooks/usesalesposstate.ts", "./app/purchase/orders/hooks/index.ts", "./app/purchase/purchase/purchasehelpers.ts", "./app/purchase/purchase/components/purchaseheader.tsx", "./app/purchase/purchase/components/purchasefilters.tsx", "./app/purchase/purchase/components/purchaselistview.tsx", "./app/purchase/purchase/components/purchasedetailsmodal.tsx", "./app/purchase/purchase/components/purchasecancelmodal.tsx", "./app/purchase/purchase/pos/styles/posstyles.ts", "./app/purchase/purchase/pos/types/index.ts", "./app/purchase/purchase/pos/components/searchbar.tsx", "./app/purchase/purchase/pos/utils/poshelpers.ts", "./app/purchase/purchase/pos/components/productlist.tsx", "./app/purchase/purchase/pos/components/paymentselector.tsx", "./app/purchase/purchase/pos/components/taxcontrols.tsx", "./app/purchase/purchase/pos/components/notescontrols.tsx", "./app/purchase/purchase/pos/components/purchasecart.tsx", "./app/purchase/purchase/pos/components/pagination.tsx", "./app/purchase/purchase/pos/components/categoryfilter.tsx", "./app/purchase/purchase/pos/components/index.ts", "./components/suppliermodal/types.ts", "./components/suppliermodal/api/supplierapi.ts", "./components/suppliermodal/hooks/usesupplierdata.ts", "./components/suppliermodal/utils/supplierhelpers.ts", "./components/suppliermodal/styles/suppliermodalstyles.ts", "./components/suppliermodal/suppliermodal.tsx", "./components/suppliermodal/index.ts", "./app/purchase/purchase/pos/config/posconfig.ts", "./app/purchase/purchase/pos/hooks/useposstate.ts", "./app/purchase/purchase/pos/hooks/useposoperations.ts", "./app/purchase/purchase/pos/hooks/useposproducts.ts", "./app/purchase/purchase/pos/hooks/usekeyboardnavigation.ts", "./app/purchase/purchase/pos/hooks/index.ts", "./app/purchasing/suppliers/suppliersapi.ts", "./app/purchase/purchase/components/poview.tsx", "./app/purchase/purchase/components/pocomponent.tsx", "./app/purchase/purchase/components/listcomponent.tsx", "./app/purchase/purchase/components/index.ts", "./app/purchase/purchase/hooks/usepurchasedata.ts", "./app/purchase/purchase/hooks/usepurchaseactions.ts", "./app/purchase/purchase/hooks/usepurchaseposstate.ts", "./app/purchase/purchase/hooks/index.ts", "./app/purchase/purchase/pos/posapi.ts", "./app/purchasing/suppliers/usesuppliers.ts", "./app/purchasing/suppliers/components/searchandfilters.tsx", "./app/purchasing/suppliers/components/suppliertableactions.tsx", "./app/purchasing/suppliers/components/index.ts", "./app/purchasing/suppliers/hooks/usesupplierfilters.ts", "./app/purchasing/suppliers/hooks/usesupplieractions.ts", "./app/purchasing/suppliers/hooks/index.ts", "./app/sales/sales/salesapi.ts", "./app/sales/components/invoiceprint.tsx", "./app/sales/components/index.ts", "./app/sales/customers/customerpaymentsapi.ts", "./app/sales/customers/customersapi.ts", "./app/sales/customers/usecustomerpayments.ts", "./app/sales/customers/usecustomers.ts", "./app/sales/customers/components/searchandfilters.tsx", "./app/sales/customers/components/customertableactions.tsx", "./app/sales/customers/components/index.ts", "./app/sales/customers/hooks/usecustomerfilters.ts", "./app/sales/sales/pos/config/posconfig.ts", "./app/sales/customers/hooks/usecustomeractions.ts", "./app/sales/customers/hooks/index.ts", "./app/sales/orders/ordersapi.ts", "./app/sales/orders/ordershelpers.ts", "./app/sales/orders/components/ordersheader.tsx", "./app/sales/orders/components/ordersfilters.tsx", "./app/sales/orders/pos/types/index.ts", "./app/sales/orders/pos/utils/poshelpers.ts", "./app/sales/orders/components/orderslistview.tsx", "./app/sales/orders/components/orderdetailsmodal.tsx", "./app/sales/orders/components/orderscancelmodal.tsx", "./app/sales/orders/pos/styles/posstyles.ts", "./app/sales/orders/pos/components/searchbar.tsx", "./app/sales/orders/pos/components/productlist.tsx", "./app/sales/orders/pos/components/paymentselector.tsx", "./app/sales/orders/pos/components/taxcontrols.tsx", "./app/sales/orders/pos/components/notescontrols.tsx", "./app/sales/orders/pos/components/orderscart.tsx", "./app/sales/orders/pos/components/quantitymodal.tsx", "./app/sales/orders/pos/components/pagination.tsx", "./app/sales/orders/pos/components/index.ts", "./app/sales/orders/pos/config/posconfig.ts", "./app/sales/orders/pos/components/categoryfilter.tsx", "./app/sales/orders/pos/hooks/useposstate.ts", "./app/sales/orders/pos/posapi.ts", "./app/sales/orders/pos/hooks/useposoperations.ts", "./app/sales/orders/pos/hooks/useposproducts.ts", "./app/sales/orders/pos/hooks/usekeyboardnavigation.ts", "./app/sales/orders/pos/hooks/index.ts", "./app/sales/orders/components/posview.tsx", "./app/sales/orders/components/poscomponent.tsx", "./app/sales/orders/components/listcomponent.tsx", "./app/sales/orders/components/index.ts", "./app/sales/orders/hooks/useordersdata.ts", "./app/sales/orders/hooks/useordersactions.ts", "./app/sales/orders/hooks/useordersposstate.ts", "./app/sales/orders/hooks/index.ts", "./app/sales/quotes/quotesapi.ts", "./app/sales/quotes/quoteshelpers.ts", "./app/sales/quotes/components/quotesheader.tsx", "./app/sales/quotes/components/quotesfilters.tsx", "./app/sales/quotes/pos/types/index.ts", "./app/sales/quotes/pos/utils/poshelpers.ts", "./app/sales/quotes/components/quoteslistview.tsx", "./app/sales/quotes/components/quotedetailsmodal.tsx", "./app/sales/quotes/components/quotescancelmodal.tsx", "./app/sales/quotes/pos/styles/posstyles.ts", "./app/sales/quotes/pos/components/searchbar.tsx", "./app/sales/quotes/pos/components/productlist.tsx", "./app/sales/quotes/pos/components/paymentselector.tsx", "./app/sales/quotes/pos/components/taxcontrols.tsx", "./app/sales/quotes/pos/components/notescontrols.tsx", "./app/sales/quotes/pos/components/salescart.tsx", "./app/sales/quotes/pos/components/quantitymodal.tsx", "./app/sales/quotes/pos/components/pagination.tsx", "./app/sales/quotes/pos/components/index.ts", "./app/sales/quotes/pos/config/posconfig.ts", "./app/sales/quotes/pos/components/categoryfilter.tsx", "./app/sales/quotes/pos/hooks/usekeyboardnavigation.ts", "./app/sales/quotes/pos/hooks/useposstate.ts", "./app/sales/quotes/pos/posapi.ts", "./app/sales/quotes/pos/hooks/useposoperations.ts", "./app/sales/quotes/pos/hooks/useposproducts.ts", "./app/sales/quotes/pos/hooks/usequotesposstate.ts", "./app/sales/quotes/pos/hooks/usequotesposoperations.ts", "./app/sales/quotes/pos/hooks/index.ts", "./app/sales/quotes/components/posview.tsx", "./app/sales/quotes/components/poscomponent.tsx", "./app/sales/quotes/components/listcomponent.tsx", "./app/sales/quotes/components/index.ts", "./app/sales/quotes/hooks/usequotesdata.ts", "./app/sales/quotes/hooks/usequotesactions.ts", "./app/sales/quotes/hooks/usequotesposstate.ts", "./app/sales/quotes/hooks/index.ts", "./app/sales/sales/saleshelpers.ts", "./app/sales/sales/components/salesheader.tsx", "./app/sales/sales/components/salesfilters.tsx", "./app/sales/sales/pos/types/index.ts", "./app/sales/sales/pos/utils/poshelpers.ts", "./app/sales/sales/components/saleslistview.tsx", "./app/sales/sales/components/saledetailsmodal.tsx", "./app/sales/sales/components/salescancelmodal.tsx", "./app/sales/sales/pos/styles/posstyles.ts", "./app/sales/sales/pos/components/searchbar.tsx", "./app/sales/sales/pos/components/productlist.tsx", "./app/sales/sales/pos/components/paymentselector.tsx", "./app/sales/sales/pos/components/taxcontrols.tsx", "./app/sales/sales/pos/components/notescontrols.tsx", "./app/sales/sales/pos/components/salescart.tsx", "./app/sales/sales/pos/components/quantitymodal.tsx", "./app/sales/sales/pos/components/pagination.tsx", "./app/sales/sales/pos/components/index.ts", "./app/sales/sales/pos/components/categoryfilter.tsx", "./app/sales/sales/pos/hooks/usekeyboardnavigation.ts", "./app/sales/sales/pos/hooks/useposstate.ts", "./app/sales/sales/pos/posapi.ts", "./app/sales/sales/pos/hooks/useposoperations.ts", "./app/sales/sales/pos/hooks/useposproducts.ts", "./app/sales/sales/pos/hooks/index.ts", "./app/sales/sales/components/posview.tsx", "./app/sales/sales/components/poscomponent.tsx", "./app/sales/sales/components/listcomponent.tsx", "./app/sales/sales/components/index.ts", "./app/sales/sales/hooks/usesalesdata.ts", "./app/sales/sales/hooks/usesalesactions.ts", "./app/sales/sales/hooks/usesalesposstate.ts", "./app/sales/sales/hooks/index.ts", "./app/settings/roles/rolesapi.ts", "./app/settings/users/usersapi.ts", "./components/usermodal/usermodal.tsx", "./components/usermodal/index.ts", "./node_modules/@mui/types/esm/index.d.ts", "./node_modules/@mui/material/esm/styles/identifier.d.ts", "./node_modules/@emotion/sheet/dist/declarations/src/index.d.ts", "./node_modules/@emotion/sheet/dist/emotion-sheet.cjs.d.mts", "./node_modules/@emotion/utils/dist/declarations/src/types.d.ts", "./node_modules/@emotion/utils/dist/declarations/src/index.d.ts", "./node_modules/@emotion/utils/dist/emotion-utils.cjs.d.mts", "./node_modules/@emotion/cache/dist/declarations/src/types.d.ts", "./node_modules/@emotion/cache/dist/declarations/src/index.d.ts", "./node_modules/@emotion/cache/dist/emotion-cache.cjs.default.d.ts", "./node_modules/@emotion/cache/dist/emotion-cache.cjs.d.mts", "./node_modules/@emotion/serialize/dist/declarations/src/index.d.ts", "./node_modules/@emotion/serialize/dist/emotion-serialize.cjs.d.mts", "./node_modules/@emotion/react/dist/declarations/src/context.d.ts", "./node_modules/@emotion/react/dist/declarations/src/types.d.ts", "./node_modules/@emotion/react/dist/declarations/src/theming.d.ts", "./node_modules/@emotion/react/dist/declarations/src/jsx-namespace.d.ts", "./node_modules/@emotion/react/dist/declarations/src/jsx.d.ts", "./node_modules/@emotion/react/dist/declarations/src/global.d.ts", "./node_modules/@emotion/react/dist/declarations/src/keyframes.d.ts", "./node_modules/@emotion/react/dist/declarations/src/class-names.d.ts", "./node_modules/@emotion/react/dist/declarations/src/css.d.ts", "./node_modules/@emotion/react/dist/declarations/src/index.d.ts", "./node_modules/@emotion/react/dist/emotion-react.cjs.d.mts", "./node_modules/@emotion/styled/dist/declarations/src/jsx-namespace.d.ts", "./node_modules/@emotion/styled/dist/declarations/src/types.d.ts", "./node_modules/@emotion/styled/dist/declarations/src/index.d.ts", "./node_modules/@emotion/styled/dist/emotion-styled.cjs.default.d.ts", "./node_modules/@emotion/styled/dist/emotion-styled.cjs.d.mts", "./node_modules/@mui/styled-engine/esm/styledengineprovider/styledengineprovider.d.ts", "./node_modules/@mui/styled-engine/esm/styledengineprovider/index.d.ts", "./node_modules/@mui/styled-engine/esm/globalstyles/globalstyles.d.ts", "./node_modules/@mui/styled-engine/esm/globalstyles/index.d.ts", "./node_modules/@mui/styled-engine/esm/index.d.ts", "./node_modules/@mui/system/esm/style/style.d.ts", "./node_modules/@mui/system/esm/style/index.d.ts", "./node_modules/@mui/system/esm/borders/borders.d.ts", "./node_modules/@mui/system/esm/borders/index.d.ts", "./node_modules/@mui/system/esm/createbreakpoints/createbreakpoints.d.ts", "./node_modules/@mui/system/esm/createtheme/shape.d.ts", "./node_modules/@mui/system/esm/createtheme/createspacing.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/standardcssproperties.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/aliasescssproperties.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/overwritecssproperties.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/stylefunctionsx.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/extendsxprop.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/defaultsxconfig.d.ts", "./node_modules/@mui/system/esm/stylefunctionsx/index.d.ts", "./node_modules/@mui/system/esm/createtheme/applystyles.d.ts", "./node_modules/@mui/system/esm/csscontainerqueries/csscontainerqueries.d.ts", "./node_modules/@mui/system/esm/csscontainerqueries/index.d.ts", "./node_modules/@mui/system/esm/createtheme/createtheme.d.ts", "./node_modules/@mui/system/esm/createtheme/index.d.ts", "./node_modules/@mui/system/esm/breakpoints/breakpoints.d.ts", "./node_modules/@mui/system/esm/breakpoints/index.d.ts", "./node_modules/@mui/system/esm/compose/compose.d.ts", "./node_modules/@mui/system/esm/compose/index.d.ts", "./node_modules/@mui/system/esm/display/display.d.ts", "./node_modules/@mui/system/esm/display/index.d.ts", "./node_modules/@mui/system/esm/flexbox/flexbox.d.ts", "./node_modules/@mui/system/esm/flexbox/index.d.ts", "./node_modules/@mui/system/esm/cssgrid/cssgrid.d.ts", "./node_modules/@mui/system/esm/cssgrid/index.d.ts", "./node_modules/@mui/system/esm/palette/palette.d.ts", "./node_modules/@mui/system/esm/palette/index.d.ts", "./node_modules/@mui/system/esm/positions/positions.d.ts", "./node_modules/@mui/system/esm/positions/index.d.ts", "./node_modules/@mui/system/esm/shadows/shadows.d.ts", "./node_modules/@mui/system/esm/shadows/index.d.ts", "./node_modules/@mui/system/esm/sizing/sizing.d.ts", "./node_modules/@mui/system/esm/sizing/index.d.ts", "./node_modules/@mui/system/esm/typography/typography.d.ts", "./node_modules/@mui/system/esm/typography/index.d.ts", "./node_modules/@mui/system/esm/getthemevalue/getthemevalue.d.ts", "./node_modules/@mui/system/esm/getthemevalue/index.d.ts", "./node_modules/@mui/private-theming/esm/defaulttheme/index.d.ts", "./node_modules/@mui/private-theming/esm/themeprovider/themeprovider.d.ts", "./node_modules/@mui/private-theming/esm/themeprovider/index.d.ts", "./node_modules/@mui/private-theming/esm/usetheme/usetheme.d.ts", "./node_modules/@mui/private-theming/esm/usetheme/index.d.ts", "./node_modules/@mui/private-theming/esm/index.d.ts", "./node_modules/@mui/system/esm/globalstyles/globalstyles.d.ts", "./node_modules/@mui/system/esm/globalstyles/index.d.ts", "./node_modules/@mui/system/esm/spacing/spacing.d.ts", "./node_modules/@mui/system/esm/spacing/index.d.ts", "./node_modules/@mui/system/esm/box/box.d.ts", "./node_modules/@mui/system/esm/box/boxclasses.d.ts", "./node_modules/@mui/system/esm/box/index.d.ts", "./node_modules/@mui/system/esm/createbox/createbox.d.ts", "./node_modules/@mui/system/esm/createbox/index.d.ts", "./node_modules/@mui/system/esm/createstyled/createstyled.d.ts", "./node_modules/@mui/system/esm/createstyled/index.d.ts", "./node_modules/@mui/system/esm/styled/styled.d.ts", "./node_modules/@mui/system/esm/styled/index.d.ts", "./node_modules/@mui/system/esm/usethemeprops/usethemeprops.d.ts", "./node_modules/@mui/system/esm/usethemeprops/getthemeprops.d.ts", "./node_modules/@mui/system/esm/usethemeprops/index.d.ts", "./node_modules/@mui/system/esm/usetheme/usetheme.d.ts", "./node_modules/@mui/system/esm/usetheme/index.d.ts", "./node_modules/@mui/system/esm/usethemewithoutdefault/usethemewithoutdefault.d.ts", "./node_modules/@mui/system/esm/usethemewithoutdefault/index.d.ts", "./node_modules/@mui/system/esm/usemediaquery/usemediaquery.d.ts", "./node_modules/@mui/system/esm/usemediaquery/index.d.ts", "./node_modules/@mui/system/esm/colormanipulator/colormanipulator.d.ts", "./node_modules/@mui/system/esm/colormanipulator/index.d.ts", "./node_modules/@mui/system/esm/themeprovider/themeprovider.d.ts", "./node_modules/@mui/system/esm/themeprovider/index.d.ts", "./node_modules/@mui/system/esm/memotheme.d.ts", "./node_modules/@mui/system/esm/initcolorschemescript/initcolorschemescript.d.ts", "./node_modules/@mui/system/esm/initcolorschemescript/index.d.ts", "./node_modules/@mui/system/esm/cssvars/localstoragemanager.d.ts", "./node_modules/@mui/system/esm/cssvars/usecurrentcolorscheme.d.ts", "./node_modules/@mui/system/esm/cssvars/createcssvarsprovider.d.ts", "./node_modules/@mui/system/esm/cssvars/preparecssvars.d.ts", "./node_modules/@mui/system/esm/cssvars/preparetypographyvars.d.ts", "./node_modules/@mui/system/esm/cssvars/createcssvarstheme.d.ts", "./node_modules/@mui/system/esm/cssvars/getcolorschemeselector.d.ts", "./node_modules/@mui/system/esm/cssvars/index.d.ts", "./node_modules/@mui/system/esm/cssvars/creategetcssvar.d.ts", "./node_modules/@mui/system/esm/cssvars/cssvarsparser.d.ts", "./node_modules/@mui/system/esm/responsiveproptype/responsiveproptype.d.ts", "./node_modules/@mui/system/esm/responsiveproptype/index.d.ts", "./node_modules/@mui/system/esm/container/containerclasses.d.ts", "./node_modules/@mui/system/esm/container/containerprops.d.ts", "./node_modules/@mui/system/esm/container/createcontainer.d.ts", "./node_modules/@mui/system/esm/container/container.d.ts", "./node_modules/@mui/system/esm/container/index.d.ts", "./node_modules/@mui/system/esm/grid/gridprops.d.ts", "./node_modules/@mui/system/esm/grid/grid.d.ts", "./node_modules/@mui/system/esm/grid/creategrid.d.ts", "./node_modules/@mui/system/esm/grid/gridclasses.d.ts", "./node_modules/@mui/system/esm/grid/traversebreakpoints.d.ts", "./node_modules/@mui/system/esm/grid/gridgenerator.d.ts", "./node_modules/@mui/system/esm/grid/index.d.ts", "./node_modules/@mui/system/esm/stack/stackprops.d.ts", "./node_modules/@mui/system/esm/stack/stack.d.ts", "./node_modules/@mui/system/esm/stack/createstack.d.ts", "./node_modules/@mui/system/esm/stack/stackclasses.d.ts", "./node_modules/@mui/system/esm/stack/index.d.ts", "./node_modules/@mui/system/esm/version/index.d.ts", "./node_modules/@mui/system/esm/index.d.ts", "./node_modules/@mui/material/esm/styles/createmixins.d.ts", "./node_modules/@mui/material/esm/styles/createpalette.d.ts", "./node_modules/@mui/material/esm/styles/createtypography.d.ts", "./node_modules/@mui/material/esm/styles/shadows.d.ts", "./node_modules/@mui/material/esm/styles/createtransitions.d.ts", "./node_modules/@mui/material/esm/styles/zindex.d.ts", "./node_modules/@mui/material/esm/overridablecomponent/index.d.ts", "./node_modules/@mui/material/esm/svgicon/svgiconclasses.d.ts", "./node_modules/@mui/material/esm/svgicon/svgicon.d.ts", "./node_modules/@mui/material/esm/svgicon/index.d.ts", "./node_modules/@mui/material/esm/internal/index.d.ts", "./node_modules/@mui/material/esm/buttonbase/touchrippleclasses.d.ts", "./node_modules/@mui/material/esm/buttonbase/touchripple.d.ts", "./node_modules/@mui/material/esm/buttonbase/buttonbaseclasses.d.ts", "./node_modules/@mui/material/esm/buttonbase/buttonbase.d.ts", "./node_modules/@mui/material/esm/buttonbase/index.d.ts", "./node_modules/@mui/material/esm/iconbutton/iconbuttonclasses.d.ts", "./node_modules/@mui/material/esm/iconbutton/iconbutton.d.ts", "./node_modules/@mui/material/esm/iconbutton/index.d.ts", "./node_modules/@mui/material/esm/paper/paperclasses.d.ts", "./node_modules/@mui/material/esm/paper/paper.d.ts", "./node_modules/@mui/material/esm/paper/index.d.ts", "./node_modules/@mui/material/esm/alert/alertclasses.d.ts", "./node_modules/@mui/utils/esm/types/index.d.ts", "./node_modules/@mui/material/esm/utils/types.d.ts", "./node_modules/@mui/material/esm/alert/alert.d.ts", "./node_modules/@mui/material/esm/alert/index.d.ts", "./node_modules/@mui/material/esm/typography/typographyclasses.d.ts", "./node_modules/@mui/material/esm/typography/typography.d.ts", "./node_modules/@mui/material/esm/typography/index.d.ts", "./node_modules/@mui/material/esm/alerttitle/alerttitleclasses.d.ts", "./node_modules/@mui/material/esm/alerttitle/alerttitle.d.ts", "./node_modules/@mui/material/esm/alerttitle/index.d.ts", "./node_modules/@mui/material/esm/appbar/appbarclasses.d.ts", "./node_modules/@mui/material/esm/appbar/appbar.d.ts", "./node_modules/@mui/material/esm/appbar/index.d.ts", "./node_modules/@mui/material/esm/chip/chipclasses.d.ts", "./node_modules/@mui/material/esm/chip/chip.d.ts", "./node_modules/@mui/material/esm/chip/index.d.ts", "./node_modules/@popperjs/core/lib/enums.d.ts", "./node_modules/@popperjs/core/lib/modifiers/popperoffsets.d.ts", "./node_modules/@popperjs/core/lib/modifiers/flip.d.ts", "./node_modules/@popperjs/core/lib/modifiers/hide.d.ts", "./node_modules/@popperjs/core/lib/modifiers/offset.d.ts", "./node_modules/@popperjs/core/lib/modifiers/eventlisteners.d.ts", "./node_modules/@popperjs/core/lib/modifiers/computestyles.d.ts", "./node_modules/@popperjs/core/lib/modifiers/arrow.d.ts", "./node_modules/@popperjs/core/lib/modifiers/preventoverflow.d.ts", "./node_modules/@popperjs/core/lib/modifiers/applystyles.d.ts", "./node_modules/@popperjs/core/lib/types.d.ts", "./node_modules/@popperjs/core/lib/modifiers/index.d.ts", "./node_modules/@popperjs/core/lib/utils/detectoverflow.d.ts", "./node_modules/@popperjs/core/lib/createpopper.d.ts", "./node_modules/@popperjs/core/lib/popper-lite.d.ts", "./node_modules/@popperjs/core/lib/popper.d.ts", "./node_modules/@popperjs/core/lib/index.d.ts", "./node_modules/@popperjs/core/index.d.ts", "./node_modules/@mui/material/esm/portal/portal.types.d.ts", "./node_modules/@mui/material/esm/portal/portal.d.ts", "./node_modules/@mui/material/esm/portal/index.d.ts", "./node_modules/@mui/material/esm/utils/polymorphiccomponent.d.ts", "./node_modules/@mui/material/esm/popper/basepopper.types.d.ts", "./node_modules/@mui/material/esm/popper/popper.d.ts", "./node_modules/@mui/material/esm/popper/popperclasses.d.ts", "./node_modules/@mui/material/esm/popper/index.d.ts", "./node_modules/@mui/material/esm/useautocomplete/useautocomplete.d.ts", "./node_modules/@mui/material/esm/useautocomplete/index.d.ts", "./node_modules/@mui/material/esm/autocomplete/autocompleteclasses.d.ts", "./node_modules/@mui/material/esm/autocomplete/autocomplete.d.ts", "./node_modules/@mui/material/esm/autocomplete/index.d.ts", "./node_modules/@mui/material/esm/avatar/avatarclasses.d.ts", "./node_modules/@mui/material/esm/avatar/avatar.d.ts", "./node_modules/@mui/material/esm/avatar/index.d.ts", "./node_modules/@mui/material/esm/avatargroup/avatargroupclasses.d.ts", "./node_modules/@mui/material/esm/avatargroup/avatargroup.d.ts", "./node_modules/@mui/material/esm/avatargroup/index.d.ts", "./node_modules/@types/react-transition-group/transition.d.ts", "./node_modules/@mui/material/esm/transitions/transition.d.ts", "./node_modules/@mui/material/esm/fade/fade.d.ts", "./node_modules/@mui/material/esm/fade/index.d.ts", "./node_modules/@mui/material/esm/backdrop/backdropclasses.d.ts", "./node_modules/@mui/material/esm/backdrop/backdrop.d.ts", "./node_modules/@mui/material/esm/backdrop/index.d.ts", "./node_modules/@mui/material/esm/badge/badgeclasses.d.ts", "./node_modules/@mui/material/esm/badge/badge.d.ts", "./node_modules/@mui/material/esm/badge/index.d.ts", "./node_modules/@mui/material/esm/bottomnavigationaction/bottomnavigationactionclasses.d.ts", "./node_modules/@mui/material/esm/bottomnavigationaction/bottomnavigationaction.d.ts", "./node_modules/@mui/material/esm/bottomnavigationaction/index.d.ts", "./node_modules/@mui/material/esm/bottomnavigation/bottomnavigationclasses.d.ts", "./node_modules/@mui/material/esm/bottomnavigation/bottomnavigation.d.ts", "./node_modules/@mui/material/esm/bottomnavigation/index.d.ts", "./node_modules/@mui/material/esm/breadcrumbs/breadcrumbsclasses.d.ts", "./node_modules/@mui/material/esm/breadcrumbs/breadcrumbs.d.ts", "./node_modules/@mui/material/esm/breadcrumbs/index.d.ts", "./node_modules/@mui/material/esm/buttongroup/buttongroupclasses.d.ts", "./node_modules/@mui/material/esm/buttongroup/buttongroup.d.ts", "./node_modules/@mui/material/esm/buttongroup/buttongroupcontext.d.ts", "./node_modules/@mui/material/esm/buttongroup/buttongroupbuttoncontext.d.ts", "./node_modules/@mui/material/esm/buttongroup/index.d.ts", "./node_modules/@mui/material/esm/button/buttonclasses.d.ts", "./node_modules/@mui/material/esm/button/button.d.ts", "./node_modules/@mui/material/esm/button/index.d.ts", "./node_modules/@mui/material/esm/cardactionarea/cardactionareaclasses.d.ts", "./node_modules/@mui/material/esm/cardactionarea/cardactionarea.d.ts", "./node_modules/@mui/material/esm/cardactionarea/index.d.ts", "./node_modules/@mui/material/esm/cardactions/cardactionsclasses.d.ts", "./node_modules/@mui/material/esm/cardactions/cardactions.d.ts", "./node_modules/@mui/material/esm/cardactions/index.d.ts", "./node_modules/@mui/material/esm/cardcontent/cardcontentclasses.d.ts", "./node_modules/@mui/material/esm/cardcontent/cardcontent.d.ts", "./node_modules/@mui/material/esm/cardcontent/index.d.ts", "./node_modules/@mui/material/esm/cardheader/cardheaderclasses.d.ts", "./node_modules/@mui/material/esm/cardheader/cardheader.d.ts", "./node_modules/@mui/material/esm/cardheader/index.d.ts", "./node_modules/@mui/material/esm/cardmedia/cardmediaclasses.d.ts", "./node_modules/@mui/material/esm/cardmedia/cardmedia.d.ts", "./node_modules/@mui/material/esm/cardmedia/index.d.ts", "./node_modules/@mui/material/esm/card/cardclasses.d.ts", "./node_modules/@mui/material/esm/card/card.d.ts", "./node_modules/@mui/material/esm/card/index.d.ts", "./node_modules/@mui/material/esm/internal/switchbaseclasses.d.ts", "./node_modules/@mui/material/esm/internal/switchbase.d.ts", "./node_modules/@mui/material/esm/checkbox/checkboxclasses.d.ts", "./node_modules/@mui/material/esm/checkbox/checkbox.d.ts", "./node_modules/@mui/material/esm/checkbox/index.d.ts", "./node_modules/@mui/material/esm/circularprogress/circularprogressclasses.d.ts", "./node_modules/@mui/material/esm/circularprogress/circularprogress.d.ts", "./node_modules/@mui/material/esm/circularprogress/index.d.ts", "./node_modules/@mui/material/esm/collapse/collapseclasses.d.ts", "./node_modules/@mui/material/esm/collapse/collapse.d.ts", "./node_modules/@mui/material/esm/collapse/index.d.ts", "./node_modules/@mui/material/esm/container/containerclasses.d.ts", "./node_modules/@mui/material/esm/container/container.d.ts", "./node_modules/@mui/material/esm/container/index.d.ts", "./node_modules/@mui/material/esm/cssbaseline/cssbaseline.d.ts", "./node_modules/@mui/material/esm/cssbaseline/index.d.ts", "./node_modules/@mui/material/esm/dialogactions/dialogactionsclasses.d.ts", "./node_modules/@mui/material/esm/dialogactions/dialogactions.d.ts", "./node_modules/@mui/material/esm/dialogactions/index.d.ts", "./node_modules/@mui/material/esm/dialogcontent/dialogcontentclasses.d.ts", "./node_modules/@mui/material/esm/dialogcontent/dialogcontent.d.ts", "./node_modules/@mui/material/esm/dialogcontent/index.d.ts", "./node_modules/@mui/material/esm/dialogcontenttext/dialogcontenttextclasses.d.ts", "./node_modules/@mui/material/esm/dialogcontenttext/dialogcontenttext.d.ts", "./node_modules/@mui/material/esm/dialogcontenttext/index.d.ts", "./node_modules/@mui/material/esm/modal/modalmanager.d.ts", "./node_modules/@mui/material/esm/modal/modalclasses.d.ts", "./node_modules/@mui/material/esm/modal/modal.d.ts", "./node_modules/@mui/material/esm/modal/index.d.ts", "./node_modules/@mui/material/esm/dialog/dialogclasses.d.ts", "./node_modules/@mui/material/esm/dialog/dialog.d.ts", "./node_modules/@mui/material/esm/dialog/index.d.ts", "./node_modules/@mui/material/esm/dialogtitle/dialogtitleclasses.d.ts", "./node_modules/@mui/material/esm/dialogtitle/dialogtitle.d.ts", "./node_modules/@mui/material/esm/dialogtitle/index.d.ts", "./node_modules/@mui/material/esm/divider/dividerclasses.d.ts", "./node_modules/@mui/material/esm/divider/divider.d.ts", "./node_modules/@mui/material/esm/divider/index.d.ts", "./node_modules/@mui/material/esm/slide/slide.d.ts", "./node_modules/@mui/material/esm/slide/index.d.ts", "./node_modules/@mui/material/esm/drawer/drawerclasses.d.ts", "./node_modules/@mui/material/esm/drawer/drawer.d.ts", "./node_modules/@mui/material/esm/drawer/index.d.ts", "./node_modules/@mui/material/esm/accordionactions/accordionactionsclasses.d.ts", "./node_modules/@mui/material/esm/accordionactions/accordionactions.d.ts", "./node_modules/@mui/material/esm/accordionactions/index.d.ts", "./node_modules/@mui/material/esm/accordiondetails/accordiondetailsclasses.d.ts", "./node_modules/@mui/material/esm/accordiondetails/accordiondetails.d.ts", "./node_modules/@mui/material/esm/accordiondetails/index.d.ts", "./node_modules/@mui/material/esm/accordion/accordionclasses.d.ts", "./node_modules/@mui/material/esm/accordion/accordion.d.ts", "./node_modules/@mui/material/esm/accordion/index.d.ts", "./node_modules/@mui/material/esm/accordionsummary/accordionsummaryclasses.d.ts", "./node_modules/@mui/material/esm/accordionsummary/accordionsummary.d.ts", "./node_modules/@mui/material/esm/accordionsummary/index.d.ts", "./node_modules/@mui/material/esm/fab/fabclasses.d.ts", "./node_modules/@mui/material/esm/fab/fab.d.ts", "./node_modules/@mui/material/esm/fab/index.d.ts", "./node_modules/@mui/material/esm/inputbase/inputbaseclasses.d.ts", "./node_modules/@mui/material/esm/inputbase/inputbase.d.ts", "./node_modules/@mui/material/esm/inputbase/index.d.ts", "./node_modules/@mui/material/esm/filledinput/filledinputclasses.d.ts", "./node_modules/@mui/material/esm/filledinput/filledinput.d.ts", "./node_modules/@mui/material/esm/filledinput/index.d.ts", "./node_modules/@mui/material/esm/formcontrollabel/formcontrollabelclasses.d.ts", "./node_modules/@mui/material/esm/formcontrollabel/formcontrollabel.d.ts", "./node_modules/@mui/material/esm/formcontrollabel/index.d.ts", "./node_modules/@mui/material/esm/formcontrol/formcontrolclasses.d.ts", "./node_modules/@mui/material/esm/formcontrol/formcontrol.d.ts", "./node_modules/@mui/material/esm/formcontrol/formcontrolcontext.d.ts", "./node_modules/@mui/material/esm/formcontrol/useformcontrol.d.ts", "./node_modules/@mui/material/esm/formcontrol/index.d.ts", "./node_modules/@mui/material/esm/formgroup/formgroupclasses.d.ts", "./node_modules/@mui/material/esm/formgroup/formgroup.d.ts", "./node_modules/@mui/material/esm/formgroup/index.d.ts", "./node_modules/@mui/material/esm/formhelpertext/formhelpertextclasses.d.ts", "./node_modules/@mui/material/esm/formhelpertext/formhelpertext.d.ts", "./node_modules/@mui/material/esm/formhelpertext/index.d.ts", "./node_modules/@mui/material/esm/formlabel/formlabelclasses.d.ts", "./node_modules/@mui/material/esm/formlabel/formlabel.d.ts", "./node_modules/@mui/material/esm/formlabel/index.d.ts", "./node_modules/@mui/material/esm/gridlegacy/gridlegacyclasses.d.ts", "./node_modules/@mui/material/esm/gridlegacy/gridlegacy.d.ts", "./node_modules/@mui/material/esm/gridlegacy/index.d.ts", "./node_modules/@mui/material/esm/grid/grid.d.ts", "./node_modules/@mui/material/esm/grid/gridclasses.d.ts", "./node_modules/@mui/material/esm/grid/index.d.ts", "./node_modules/@mui/material/esm/icon/iconclasses.d.ts", "./node_modules/@mui/material/esm/icon/icon.d.ts", "./node_modules/@mui/material/esm/icon/index.d.ts", "./node_modules/@mui/material/esm/imagelist/imagelistclasses.d.ts", "./node_modules/@mui/material/esm/imagelist/imagelist.d.ts", "./node_modules/@mui/material/esm/imagelist/index.d.ts", "./node_modules/@mui/material/esm/imagelistitembar/imagelistitembarclasses.d.ts", "./node_modules/@mui/material/esm/imagelistitembar/imagelistitembar.d.ts", "./node_modules/@mui/material/esm/imagelistitembar/index.d.ts", "./node_modules/@mui/material/esm/imagelistitem/imagelistitemclasses.d.ts", "./node_modules/@mui/material/esm/imagelistitem/imagelistitem.d.ts", "./node_modules/@mui/material/esm/imagelistitem/index.d.ts", "./node_modules/@mui/material/esm/inputadornment/inputadornmentclasses.d.ts", "./node_modules/@mui/material/esm/inputadornment/inputadornment.d.ts", "./node_modules/@mui/material/esm/inputadornment/index.d.ts", "./node_modules/@mui/material/esm/inputlabel/inputlabelclasses.d.ts", "./node_modules/@mui/material/esm/inputlabel/inputlabel.d.ts", "./node_modules/@mui/material/esm/inputlabel/index.d.ts", "./node_modules/@mui/material/esm/input/inputclasses.d.ts", "./node_modules/@mui/material/esm/input/input.d.ts", "./node_modules/@mui/material/esm/input/index.d.ts", "./node_modules/@mui/material/esm/linearprogress/linearprogressclasses.d.ts", "./node_modules/@mui/material/esm/linearprogress/linearprogress.d.ts", "./node_modules/@mui/material/esm/linearprogress/index.d.ts", "./node_modules/@mui/material/esm/link/linkclasses.d.ts", "./node_modules/@mui/material/esm/link/link.d.ts", "./node_modules/@mui/material/esm/link/index.d.ts", "./node_modules/@mui/material/esm/listitemavatar/listitemavatarclasses.d.ts", "./node_modules/@mui/material/esm/listitemavatar/listitemavatar.d.ts", "./node_modules/@mui/material/esm/listitemavatar/index.d.ts", "./node_modules/@mui/material/esm/listitemicon/listitemiconclasses.d.ts", "./node_modules/@mui/material/esm/listitemicon/listitemicon.d.ts", "./node_modules/@mui/material/esm/listitemicon/index.d.ts", "./node_modules/@mui/material/esm/listitem/listitemclasses.d.ts", "./node_modules/@mui/material/esm/listitem/listitem.d.ts", "./node_modules/@mui/material/esm/listitem/index.d.ts", "./node_modules/@mui/material/esm/listitembutton/listitembuttonclasses.d.ts", "./node_modules/@mui/material/esm/listitembutton/listitembutton.d.ts", "./node_modules/@mui/material/esm/listitembutton/index.d.ts", "./node_modules/@mui/material/esm/listitemsecondaryaction/listitemsecondaryactionclasses.d.ts", "./node_modules/@mui/material/esm/listitemsecondaryaction/listitemsecondaryaction.d.ts", "./node_modules/@mui/material/esm/listitemsecondaryaction/index.d.ts", "./node_modules/@mui/material/esm/listitemtext/listitemtextclasses.d.ts", "./node_modules/@mui/material/esm/listitemtext/listitemtext.d.ts", "./node_modules/@mui/material/esm/listitemtext/index.d.ts", "./node_modules/@mui/material/esm/list/listclasses.d.ts", "./node_modules/@mui/material/esm/list/list.d.ts", "./node_modules/@mui/material/esm/list/index.d.ts", "./node_modules/@mui/material/esm/listsubheader/listsubheaderclasses.d.ts", "./node_modules/@mui/material/esm/listsubheader/listsubheader.d.ts", "./node_modules/@mui/material/esm/listsubheader/index.d.ts", "./node_modules/@mui/material/esm/menuitem/menuitemclasses.d.ts", "./node_modules/@mui/material/esm/menuitem/menuitem.d.ts", "./node_modules/@mui/material/esm/menuitem/index.d.ts", "./node_modules/@mui/material/esm/menulist/menulist.d.ts", "./node_modules/@mui/material/esm/menulist/index.d.ts", "./node_modules/@mui/material/esm/popover/popoverclasses.d.ts", "./node_modules/@mui/material/esm/popover/popover.d.ts", "./node_modules/@mui/material/esm/popover/index.d.ts", "./node_modules/@mui/material/esm/menu/menuclasses.d.ts", "./node_modules/@mui/material/esm/menu/menu.d.ts", "./node_modules/@mui/material/esm/menu/index.d.ts", "./node_modules/@mui/material/esm/mobilestepper/mobilestepperclasses.d.ts", "./node_modules/@mui/material/esm/mobilestepper/mobilestepper.d.ts", "./node_modules/@mui/material/esm/mobilestepper/index.d.ts", "./node_modules/@mui/material/esm/nativeselect/nativeselectinput.d.ts", "./node_modules/@mui/material/esm/nativeselect/nativeselectclasses.d.ts", "./node_modules/@mui/material/esm/nativeselect/nativeselect.d.ts", "./node_modules/@mui/material/esm/nativeselect/index.d.ts", "./node_modules/@mui/material/esm/usemediaquery/index.d.ts", "./node_modules/@mui/material/esm/outlinedinput/outlinedinputclasses.d.ts", "./node_modules/@mui/material/esm/outlinedinput/outlinedinput.d.ts", "./node_modules/@mui/material/esm/outlinedinput/index.d.ts", "./node_modules/@mui/material/esm/usepagination/usepagination.d.ts", "./node_modules/@mui/material/esm/pagination/paginationclasses.d.ts", "./node_modules/@mui/material/esm/pagination/pagination.d.ts", "./node_modules/@mui/material/esm/pagination/index.d.ts", "./node_modules/@mui/material/esm/paginationitem/paginationitemclasses.d.ts", "./node_modules/@mui/material/esm/paginationitem/paginationitem.d.ts", "./node_modules/@mui/material/esm/paginationitem/index.d.ts", "./node_modules/@mui/material/esm/radiogroup/radiogroup.d.ts", "./node_modules/@mui/material/esm/radiogroup/radiogroupcontext.d.ts", "./node_modules/@mui/material/esm/radiogroup/useradiogroup.d.ts", "./node_modules/@mui/material/esm/radiogroup/radiogroupclasses.d.ts", "./node_modules/@mui/material/esm/radiogroup/index.d.ts", "./node_modules/@mui/material/esm/radio/radioclasses.d.ts", "./node_modules/@mui/material/esm/radio/radio.d.ts", "./node_modules/@mui/material/esm/radio/index.d.ts", "./node_modules/@mui/material/esm/rating/ratingclasses.d.ts", "./node_modules/@mui/material/esm/rating/rating.d.ts", "./node_modules/@mui/material/esm/rating/index.d.ts", "./node_modules/@mui/material/esm/scopedcssbaseline/scopedcssbaselineclasses.d.ts", "./node_modules/@mui/material/esm/scopedcssbaseline/scopedcssbaseline.d.ts", "./node_modules/@mui/material/esm/scopedcssbaseline/index.d.ts", "./node_modules/@mui/material/esm/select/selectinput.d.ts", "./node_modules/@mui/material/esm/select/selectclasses.d.ts", "./node_modules/@mui/material/esm/select/select.d.ts", "./node_modules/@mui/material/esm/select/index.d.ts", "./node_modules/@mui/material/esm/skeleton/skeletonclasses.d.ts", "./node_modules/@mui/material/esm/skeleton/skeleton.d.ts", "./node_modules/@mui/material/esm/skeleton/index.d.ts", "./node_modules/@mui/material/esm/slider/useslider.types.d.ts", "./node_modules/@mui/material/esm/slider/slidervaluelabel.types.d.ts", "./node_modules/@mui/material/esm/slider/slidervaluelabel.d.ts", "./node_modules/@mui/material/esm/slider/sliderclasses.d.ts", "./node_modules/@mui/material/esm/slider/slider.d.ts", "./node_modules/@mui/material/esm/slider/index.d.ts", "./node_modules/@mui/material/esm/snackbarcontent/snackbarcontentclasses.d.ts", "./node_modules/@mui/material/esm/snackbarcontent/snackbarcontent.d.ts", "./node_modules/@mui/material/esm/snackbarcontent/index.d.ts", "./node_modules/@mui/material/esm/clickawaylistener/clickawaylistener.d.ts", "./node_modules/@mui/material/esm/clickawaylistener/index.d.ts", "./node_modules/@mui/material/esm/snackbar/snackbarclasses.d.ts", "./node_modules/@mui/material/esm/snackbar/snackbar.d.ts", "./node_modules/@mui/material/esm/snackbar/index.d.ts", "./node_modules/@mui/material/esm/transitions/index.d.ts", "./node_modules/@mui/material/esm/speeddial/speeddialclasses.d.ts", "./node_modules/@mui/material/esm/speeddial/speeddial.d.ts", "./node_modules/@mui/material/esm/speeddial/index.d.ts", "./node_modules/@mui/material/esm/tooltip/tooltipclasses.d.ts", "./node_modules/@mui/material/esm/tooltip/tooltip.d.ts", "./node_modules/@mui/material/esm/tooltip/index.d.ts", "./node_modules/@mui/material/esm/speeddialaction/speeddialactionclasses.d.ts", "./node_modules/@mui/material/esm/speeddialaction/speeddialaction.d.ts", "./node_modules/@mui/material/esm/speeddialaction/index.d.ts", "./node_modules/@mui/material/esm/speeddialicon/speeddialiconclasses.d.ts", "./node_modules/@mui/material/esm/speeddialicon/speeddialicon.d.ts", "./node_modules/@mui/material/esm/speeddialicon/index.d.ts", "./node_modules/@mui/material/esm/stack/stack.d.ts", "./node_modules/@mui/material/esm/stack/stackclasses.d.ts", "./node_modules/@mui/material/esm/stack/index.d.ts", "./node_modules/@mui/material/esm/stepbutton/stepbuttonclasses.d.ts", "./node_modules/@mui/material/esm/stepbutton/stepbutton.d.ts", "./node_modules/@mui/material/esm/stepbutton/index.d.ts", "./node_modules/@mui/material/esm/stepconnector/stepconnectorclasses.d.ts", "./node_modules/@mui/material/esm/stepconnector/stepconnector.d.ts", "./node_modules/@mui/material/esm/stepconnector/index.d.ts", "./node_modules/@mui/material/esm/stepcontent/stepcontentclasses.d.ts", "./node_modules/@mui/material/esm/stepcontent/stepcontent.d.ts", "./node_modules/@mui/material/esm/stepcontent/index.d.ts", "./node_modules/@mui/material/esm/stepicon/stepiconclasses.d.ts", "./node_modules/@mui/material/esm/stepicon/stepicon.d.ts", "./node_modules/@mui/material/esm/stepicon/index.d.ts", "./node_modules/@mui/material/esm/steplabel/steplabelclasses.d.ts", "./node_modules/@mui/material/esm/steplabel/steplabel.d.ts", "./node_modules/@mui/material/esm/steplabel/index.d.ts", "./node_modules/@mui/material/esm/stepper/stepperclasses.d.ts", "./node_modules/@mui/material/esm/stepper/stepper.d.ts", "./node_modules/@mui/material/esm/stepper/steppercontext.d.ts", "./node_modules/@mui/material/esm/stepper/index.d.ts", "./node_modules/@mui/material/esm/step/stepclasses.d.ts", "./node_modules/@mui/material/esm/step/step.d.ts", "./node_modules/@mui/material/esm/step/stepcontext.d.ts", "./node_modules/@mui/material/esm/step/index.d.ts", "./node_modules/@mui/material/esm/swipeabledrawer/swipeabledrawer.d.ts", "./node_modules/@mui/material/esm/swipeabledrawer/index.d.ts", "./node_modules/@mui/material/esm/switch/switchclasses.d.ts", "./node_modules/@mui/material/esm/switch/switch.d.ts", "./node_modules/@mui/material/esm/switch/index.d.ts", "./node_modules/@mui/material/esm/tablebody/tablebodyclasses.d.ts", "./node_modules/@mui/material/esm/tablebody/tablebody.d.ts", "./node_modules/@mui/material/esm/tablebody/index.d.ts", "./node_modules/@mui/material/esm/tablecell/tablecellclasses.d.ts", "./node_modules/@mui/material/esm/tablecell/tablecell.d.ts", "./node_modules/@mui/material/esm/tablecell/index.d.ts", "./node_modules/@mui/material/esm/tablecontainer/tablecontainerclasses.d.ts", "./node_modules/@mui/material/esm/tablecontainer/tablecontainer.d.ts", "./node_modules/@mui/material/esm/tablecontainer/index.d.ts", "./node_modules/@mui/material/esm/tablehead/tableheadclasses.d.ts", "./node_modules/@mui/material/esm/tablehead/tablehead.d.ts", "./node_modules/@mui/material/esm/tablehead/index.d.ts", "./node_modules/@mui/material/esm/tablepaginationactions/tablepaginationactions.d.ts", "./node_modules/@mui/material/esm/tablepaginationactions/tablepaginationactionsclasses.d.ts", "./node_modules/@mui/material/esm/tablepaginationactions/index.d.ts", "./node_modules/@mui/material/esm/tablepagination/tablepaginationclasses.d.ts", "./node_modules/@mui/material/esm/toolbar/toolbarclasses.d.ts", "./node_modules/@mui/material/esm/toolbar/toolbar.d.ts", "./node_modules/@mui/material/esm/toolbar/index.d.ts", "./node_modules/@mui/material/esm/tablepagination/tablepagination.d.ts", "./node_modules/@mui/material/esm/tablepagination/index.d.ts", "./node_modules/@mui/material/esm/table/tableclasses.d.ts", "./node_modules/@mui/material/esm/table/table.d.ts", "./node_modules/@mui/material/esm/table/index.d.ts", "./node_modules/@mui/material/esm/tablerow/tablerowclasses.d.ts", "./node_modules/@mui/material/esm/tablerow/tablerow.d.ts", "./node_modules/@mui/material/esm/tablerow/index.d.ts", "./node_modules/@mui/material/esm/tablesortlabel/tablesortlabelclasses.d.ts", "./node_modules/@mui/material/esm/tablesortlabel/tablesortlabel.d.ts", "./node_modules/@mui/material/esm/tablesortlabel/index.d.ts", "./node_modules/@mui/material/esm/tablefooter/tablefooterclasses.d.ts", "./node_modules/@mui/material/esm/tablefooter/tablefooter.d.ts", "./node_modules/@mui/material/esm/tablefooter/index.d.ts", "./node_modules/@mui/material/esm/tab/tabclasses.d.ts", "./node_modules/@mui/material/esm/tab/tab.d.ts", "./node_modules/@mui/material/esm/tab/index.d.ts", "./node_modules/@mui/material/esm/tabscrollbutton/tabscrollbuttonclasses.d.ts", "./node_modules/@mui/material/esm/tabscrollbutton/tabscrollbutton.d.ts", "./node_modules/@mui/material/esm/tabscrollbutton/index.d.ts", "./node_modules/@mui/material/esm/tabs/tabsclasses.d.ts", "./node_modules/@mui/material/esm/tabs/tabs.d.ts", "./node_modules/@mui/material/esm/tabs/index.d.ts", "./node_modules/@mui/material/esm/textfield/textfieldclasses.d.ts", "./node_modules/@mui/material/esm/textfield/textfield.d.ts", "./node_modules/@mui/material/esm/textfield/index.d.ts", "./node_modules/@mui/material/esm/togglebutton/togglebuttonclasses.d.ts", "./node_modules/@mui/material/esm/togglebutton/togglebutton.d.ts", "./node_modules/@mui/material/esm/togglebutton/index.d.ts", "./node_modules/@mui/material/esm/togglebuttongroup/togglebuttongroupclasses.d.ts", "./node_modules/@mui/material/esm/togglebuttongroup/togglebuttongroup.d.ts", "./node_modules/@mui/material/esm/togglebuttongroup/index.d.ts", "./node_modules/@mui/material/esm/styles/props.d.ts", "./node_modules/@mui/material/esm/styles/overrides.d.ts", "./node_modules/@mui/material/esm/styles/variants.d.ts", "./node_modules/@mui/material/esm/styles/components.d.ts", "./node_modules/@mui/material/esm/styles/createthemenovars.d.ts", "./node_modules/@mui/material/esm/styles/createthemewithvars.d.ts", "./node_modules/@mui/material/esm/styles/createtheme.d.ts", "./node_modules/@mui/material/esm/styles/adaptv4theme.d.ts", "./node_modules/@mui/material/esm/styles/createcolorscheme.d.ts", "./node_modules/@mui/material/esm/styles/createstyles.d.ts", "./node_modules/@mui/material/esm/styles/responsivefontsizes.d.ts", "./node_modules/@mui/system/esm/createbreakpoints/index.d.ts", "./node_modules/@mui/material/esm/styles/usetheme.d.ts", "./node_modules/@mui/material/esm/styles/usethemeprops.d.ts", "./node_modules/@mui/material/esm/styles/slotshouldforwardprop.d.ts", "./node_modules/@mui/material/esm/styles/rootshouldforwardprop.d.ts", "./node_modules/@mui/material/esm/styles/styled.d.ts", "./node_modules/@mui/material/esm/styles/themeprovider.d.ts", "./node_modules/@mui/material/esm/styles/cssutils.d.ts", "./node_modules/@mui/material/esm/styles/makestyles.d.ts", "./node_modules/@mui/material/esm/styles/withstyles.d.ts", "./node_modules/@mui/material/esm/styles/withtheme.d.ts", "./node_modules/@mui/material/esm/styles/themeproviderwithvars.d.ts", "./node_modules/@mui/material/esm/styles/getoverlayalpha.d.ts", "./node_modules/@mui/material/esm/styles/shouldskipgeneratingvar.d.ts", "./node_modules/@mui/material/esm/styles/excludevariablesfromroot.d.ts", "./node_modules/@mui/material/esm/styles/index.d.ts", "./components/dynamictable/dynamictablestyles.tsx", "./components/dynamictable/dynamictablelogic.ts", "./styles/themes.ts", "./utils/networkutils.ts", "./node_modules/next/dist/compiled/@next/font/dist/types.d.ts", "./node_modules/next/dist/compiled/@next/font/dist/google/index.d.ts", "./node_modules/next/font/google/index.d.ts", "./contexts/themecontext.tsx", "./app/providers.tsx", "./app/layout.tsx", "./components/sidetaskbar/sidetaskbar.tsx", "./app/page.tsx", "./components/backendstatuschecker.tsx", "./components/networkstatuschecker.tsx", "./app/auth/page.tsx", "./app/auth/callback/page.tsx", "./components/toptaskbar/toptaskbar.tsx", "./app/dashboard/layout.tsx", "./app/dashboard/components/dashboardcard.tsx", "./app/dashboard/components/quickactionbutton.tsx", "./app/dashboard/components/recentactivity.tsx", "./app/dashboard/page.tsx", "./app/inventory/layout.tsx", "./node_modules/react-hook-form/dist/constants.d.ts", "./node_modules/react-hook-form/dist/utils/createsubject.d.ts", "./node_modules/react-hook-form/dist/types/events.d.ts", "./node_modules/react-hook-form/dist/types/path/common.d.ts", "./node_modules/react-hook-form/dist/types/path/eager.d.ts", "./node_modules/react-hook-form/dist/types/path/index.d.ts", "./node_modules/react-hook-form/dist/types/fieldarray.d.ts", "./node_modules/react-hook-form/dist/types/resolvers.d.ts", "./node_modules/react-hook-form/dist/types/form.d.ts", "./node_modules/react-hook-form/dist/types/utils.d.ts", "./node_modules/react-hook-form/dist/types/fields.d.ts", "./node_modules/react-hook-form/dist/types/errors.d.ts", "./node_modules/react-hook-form/dist/types/validator.d.ts", "./node_modules/react-hook-form/dist/types/controller.d.ts", "./node_modules/react-hook-form/dist/types/index.d.ts", "./node_modules/react-hook-form/dist/controller.d.ts", "./node_modules/react-hook-form/dist/form.d.ts", "./node_modules/react-hook-form/dist/logic/appenderrors.d.ts", "./node_modules/react-hook-form/dist/logic/createformcontrol.d.ts", "./node_modules/react-hook-form/dist/logic/index.d.ts", "./node_modules/react-hook-form/dist/usecontroller.d.ts", "./node_modules/react-hook-form/dist/usefieldarray.d.ts", "./node_modules/react-hook-form/dist/useform.d.ts", "./node_modules/react-hook-form/dist/useformcontext.d.ts", "./node_modules/react-hook-form/dist/useformstate.d.ts", "./node_modules/react-hook-form/dist/usewatch.d.ts", "./node_modules/react-hook-form/dist/utils/get.d.ts", "./node_modules/react-hook-form/dist/utils/set.d.ts", "./node_modules/react-hook-form/dist/utils/index.d.ts", "./node_modules/react-hook-form/dist/index.d.ts", "./node_modules/zod/dist/types/v3/helpers/typealiases.d.ts", "./node_modules/zod/dist/types/v3/helpers/util.d.ts", "./node_modules/zod/dist/types/v3/zoderror.d.ts", "./node_modules/zod/dist/types/v3/locales/en.d.ts", "./node_modules/zod/dist/types/v3/errors.d.ts", "./node_modules/zod/dist/types/v3/helpers/parseutil.d.ts", "./node_modules/zod/dist/types/v3/helpers/enumutil.d.ts", "./node_modules/zod/dist/types/v3/helpers/errorutil.d.ts", "./node_modules/zod/dist/types/v3/helpers/partialutil.d.ts", "./node_modules/zod/dist/types/v3/standard-schema.d.ts", "./node_modules/zod/dist/types/v3/types.d.ts", "./node_modules/zod/dist/types/v3/external.d.ts", "./node_modules/zod/dist/types/v3/index.d.ts", "./node_modules/zod/dist/types/index.d.ts", "./node_modules/zod/dist/types/v4/core/standard-schema.d.ts", "./node_modules/zod/dist/types/v4/core/util.d.ts", "./node_modules/zod/dist/types/v4/core/versions.d.ts", "./node_modules/zod/dist/types/v4/core/schemas.d.ts", "./node_modules/zod/dist/types/v4/core/checks.d.ts", "./node_modules/zod/dist/types/v4/core/errors.d.ts", "./node_modules/zod/dist/types/v4/core/core.d.ts", "./node_modules/zod/dist/types/v4/core/parse.d.ts", "./node_modules/zod/dist/types/v4/core/regexes.d.ts", "./node_modules/zod/dist/types/v4/locales/ar.d.ts", "./node_modules/zod/dist/types/v4/locales/az.d.ts", "./node_modules/zod/dist/types/v4/locales/be.d.ts", "./node_modules/zod/dist/types/v4/locales/ca.d.ts", "./node_modules/zod/dist/types/v4/locales/cs.d.ts", "./node_modules/zod/dist/types/v4/locales/de.d.ts", "./node_modules/zod/dist/types/v4/locales/en.d.ts", "./node_modules/zod/dist/types/v4/locales/es.d.ts", "./node_modules/zod/dist/types/v4/locales/fa.d.ts", "./node_modules/zod/dist/types/v4/locales/fi.d.ts", "./node_modules/zod/dist/types/v4/locales/fr.d.ts", "./node_modules/zod/dist/types/v4/locales/fr-ca.d.ts", "./node_modules/zod/dist/types/v4/locales/he.d.ts", "./node_modules/zod/dist/types/v4/locales/hu.d.ts", "./node_modules/zod/dist/types/v4/locales/id.d.ts", "./node_modules/zod/dist/types/v4/locales/it.d.ts", "./node_modules/zod/dist/types/v4/locales/ja.d.ts", "./node_modules/zod/dist/types/v4/locales/kh.d.ts", "./node_modules/zod/dist/types/v4/locales/ko.d.ts", "./node_modules/zod/dist/types/v4/locales/mk.d.ts", "./node_modules/zod/dist/types/v4/locales/ms.d.ts", "./node_modules/zod/dist/types/v4/locales/nl.d.ts", "./node_modules/zod/dist/types/v4/locales/no.d.ts", "./node_modules/zod/dist/types/v4/locales/ota.d.ts", "./node_modules/zod/dist/types/v4/locales/ps.d.ts", "./node_modules/zod/dist/types/v4/locales/pl.d.ts", "./node_modules/zod/dist/types/v4/locales/pt.d.ts", "./node_modules/zod/dist/types/v4/locales/ru.d.ts", "./node_modules/zod/dist/types/v4/locales/sl.d.ts", "./node_modules/zod/dist/types/v4/locales/sv.d.ts", "./node_modules/zod/dist/types/v4/locales/ta.d.ts", "./node_modules/zod/dist/types/v4/locales/th.d.ts", "./node_modules/zod/dist/types/v4/locales/tr.d.ts", "./node_modules/zod/dist/types/v4/locales/ua.d.ts", "./node_modules/zod/dist/types/v4/locales/ur.d.ts", "./node_modules/zod/dist/types/v4/locales/vi.d.ts", "./node_modules/zod/dist/types/v4/locales/zh-cn.d.ts", "./node_modules/zod/dist/types/v4/locales/zh-tw.d.ts", "./node_modules/zod/dist/types/v4/locales/index.d.ts", "./node_modules/zod/dist/types/v4/core/registries.d.ts", "./node_modules/zod/dist/types/v4/core/doc.d.ts", "./node_modules/zod/dist/types/v4/core/function.d.ts", "./node_modules/zod/dist/types/v4/core/api.d.ts", "./node_modules/zod/dist/types/v4/core/json-schema.d.ts", "./node_modules/zod/dist/types/v4/core/to-json-schema.d.ts", "./node_modules/zod/dist/types/v4/core/index.d.ts", "./node_modules/@hookform/resolvers/zod/dist/zod.d.ts", "./node_modules/@hookform/resolvers/zod/dist/index.d.ts", "./app/inventory/products/productform.tsx", "./app/inventory/products/productmodal.tsx", "./app/inventory/products/create.tsx", "./app/inventory/products/page.tsx", "./app/inventory/products/[uuid]/edit.tsx", "./app/inventory/products/categories/page.tsx", "./node_modules/goober/goober.d.ts", "./node_modules/react-hot-toast/dist/index.d.ts", "./app/inventory/stock/adjustments/components/productselectionmodal.tsx", "./app/inventory/stock/adjustments/page.tsx", "./app/inventory/stock/alerts/page.tsx", "./app/inventory/stock/levels/page.tsx", "./app/inventory/stock/movements/page.tsx", "./app/inventory/stock/transfers/page.tsx", "./app/inventory/stock/vans/page.tsx", "./app/inventory/stock-levels/page.tsx", "./app/logistics/layout.tsx", "./app/logistics/regions/regionform.tsx", "./components/protectedroute.tsx", "./app/logistics/regions/page.tsx", "./node_modules/@types/geojson/index.d.ts", "./node_modules/@types/leaflet/index.d.ts", "./node_modules/react-leaflet/lib/hooks.d.ts", "./node_modules/react-leaflet/lib/attributioncontrol.d.ts", "./node_modules/@react-leaflet/core/lib/attribution.d.ts", "./node_modules/@react-leaflet/core/lib/context.d.ts", "./node_modules/@react-leaflet/core/lib/element.d.ts", "./node_modules/@react-leaflet/core/lib/events.d.ts", "./node_modules/@react-leaflet/core/lib/layer.d.ts", "./node_modules/@react-leaflet/core/lib/path.d.ts", "./node_modules/@react-leaflet/core/lib/circle.d.ts", "./node_modules/@react-leaflet/core/lib/div-overlay.d.ts", "./node_modules/@react-leaflet/core/lib/component.d.ts", "./node_modules/@react-leaflet/core/lib/control.d.ts", "./node_modules/@react-leaflet/core/lib/dom.d.ts", "./node_modules/@react-leaflet/core/lib/generic.d.ts", "./node_modules/@react-leaflet/core/lib/grid-layer.d.ts", "./node_modules/@react-leaflet/core/lib/media-overlay.d.ts", "./node_modules/@react-leaflet/core/lib/pane.d.ts", "./node_modules/@react-leaflet/core/lib/index.d.ts", "./node_modules/react-leaflet/lib/circle.d.ts", "./node_modules/react-leaflet/lib/circlemarker.d.ts", "./node_modules/react-leaflet/lib/layergroup.d.ts", "./node_modules/react-leaflet/lib/featuregroup.d.ts", "./node_modules/react-leaflet/lib/geojson.d.ts", "./node_modules/react-leaflet/lib/imageoverlay.d.ts", "./node_modules/react-leaflet/lib/layerscontrol.d.ts", "./node_modules/react-leaflet/lib/mapcontainer.d.ts", "./node_modules/react-leaflet/lib/marker.d.ts", "./node_modules/react-leaflet/lib/pane.d.ts", "./node_modules/react-leaflet/lib/polygon.d.ts", "./node_modules/react-leaflet/lib/polyline.d.ts", "./node_modules/react-leaflet/lib/popup.d.ts", "./node_modules/react-leaflet/lib/rectangle.d.ts", "./node_modules/react-leaflet/lib/scalecontrol.d.ts", "./node_modules/react-leaflet/lib/svgoverlay.d.ts", "./node_modules/react-leaflet/lib/tilelayer.d.ts", "./node_modules/react-leaflet/lib/tooltip.d.ts", "./node_modules/react-leaflet/lib/videooverlay.d.ts", "./node_modules/react-leaflet/lib/wmstilelayer.d.ts", "./node_modules/react-leaflet/lib/zoomcontrol.d.ts", "./node_modules/react-leaflet/lib/index.d.ts", "./app/logistics/routes/components/routemap.tsx", "./app/logistics/routes/components/computeroutemodal.tsx", "./app/logistics/routes/components/routelist.tsx", "./app/logistics/routes/page.tsx", "./app/logistics/van-loading/page.tsx", "./app/logistics/van-stock/page.tsx", "./app/logistics/vans/vanform.tsx", "./app/logistics/vans/vanmodal.tsx", "./app/logistics/vans/page.tsx", "./app/logistics/warehouses/page.tsx", "./app/logs/layout.tsx", "./app/logs/components/logdetailsmodal.tsx", "./app/logs/page.tsx", "./app/logs/test-diff/page.tsx", "./app/purchase/layout.tsx", "./app/purchase/page.tsx", "./app/purchase/orders/page.tsx", "./app/purchase/purchase/pos/components/quantitymodal.tsx", "./app/purchasing/layout.tsx", "./app/purchasing/goods-receipt/page.tsx", "./app/purchasing/orders/page.tsx", "./app/purchasing/returns/page.tsx", "./app/purchasing/suppliers/components/suppliermodal.tsx", "./app/purchasing/suppliers/page.tsx", "./app/reports/layout.tsx", "./app/reports/financial/page.tsx", "./app/reports/inventory/page.tsx", "./app/reports/sales/page.tsx", "./app/reports/van-performance/page.tsx", "./app/sales/layout.tsx", "./app/sales/customers/customerdetailsmodal.tsx", "./app/sales/customers/customermodal.tsx", "./app/sales/customers/page.tsx", "./app/sales/customers/locations/customermap.tsx", "./app/sales/customers/locations/page.tsx", "./app/sales/customers/payments/customerpaymentdetailsmodal.tsx", "./app/sales/customers/payments/customerpaymentmodal.tsx", "./app/sales/customers/payments/page.tsx", "./app/sales/orders/page.tsx", "./app/sales/quotes/page.tsx", "./app/sales/sales/page.tsx", "./app/settings/layout.tsx", "./app/settings/data/page.tsx", "./components/errortoast.tsx", "./app/settings/profile/page.tsx", "./app/settings/roles/page.tsx", "./app/settings/system/page.tsx", "./node_modules/@heroicons/react/20/solid/academiccapicon.d.ts", "./node_modules/@heroicons/react/20/solid/adjustmentshorizontalicon.d.ts", "./node_modules/@heroicons/react/20/solid/adjustmentsverticalicon.d.ts", "./node_modules/@heroicons/react/20/solid/archiveboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/archiveboxxmarkicon.d.ts", "./node_modules/@heroicons/react/20/solid/archiveboxicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdowncircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdownlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdownonsquarestackicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdownonsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdownrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdowntrayicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowleftcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowleftendonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowleftonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowleftstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowlongdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowlonglefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowlongrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowlongupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowpathroundedsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowpathicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowrightcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowrightendonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowrightonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowrightstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsmalldownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsmalllefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsmallrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsmallupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowtoprightonsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowtrendingdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowtrendingupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturndownlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturndownrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnleftdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnleftupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnrightdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnrightupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnuplefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowturnuprighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowupcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuplefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuponsquarestackicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuponsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuprighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuptrayicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuturndownicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuturnlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuturnrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowuturnupicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowspointinginicon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowspointingouticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsrightlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/arrowsupdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/atsymbolicon.d.ts", "./node_modules/@heroicons/react/20/solid/backspaceicon.d.ts", "./node_modules/@heroicons/react/20/solid/backwardicon.d.ts", "./node_modules/@heroicons/react/20/solid/banknotesicon.d.ts", "./node_modules/@heroicons/react/20/solid/bars2icon.d.ts", "./node_modules/@heroicons/react/20/solid/bars3bottomlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/bars3bottomrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/bars3centerlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/bars3icon.d.ts", "./node_modules/@heroicons/react/20/solid/bars4icon.d.ts", "./node_modules/@heroicons/react/20/solid/barsarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/barsarrowupicon.d.ts", "./node_modules/@heroicons/react/20/solid/battery0icon.d.ts", "./node_modules/@heroicons/react/20/solid/battery100icon.d.ts", "./node_modules/@heroicons/react/20/solid/battery50icon.d.ts", "./node_modules/@heroicons/react/20/solid/beakericon.d.ts", "./node_modules/@heroicons/react/20/solid/bellalerticon.d.ts", "./node_modules/@heroicons/react/20/solid/bellslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/bellsnoozeicon.d.ts", "./node_modules/@heroicons/react/20/solid/bellicon.d.ts", "./node_modules/@heroicons/react/20/solid/boldicon.d.ts", "./node_modules/@heroicons/react/20/solid/boltslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/bolticon.d.ts", "./node_modules/@heroicons/react/20/solid/bookopenicon.d.ts", "./node_modules/@heroicons/react/20/solid/bookmarkslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/bookmarksquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/bookmarkicon.d.ts", "./node_modules/@heroicons/react/20/solid/briefcaseicon.d.ts", "./node_modules/@heroicons/react/20/solid/buganticon.d.ts", "./node_modules/@heroicons/react/20/solid/buildinglibraryicon.d.ts", "./node_modules/@heroicons/react/20/solid/buildingoffice2icon.d.ts", "./node_modules/@heroicons/react/20/solid/buildingofficeicon.d.ts", "./node_modules/@heroicons/react/20/solid/buildingstorefronticon.d.ts", "./node_modules/@heroicons/react/20/solid/cakeicon.d.ts", "./node_modules/@heroicons/react/20/solid/calculatoricon.d.ts", "./node_modules/@heroicons/react/20/solid/calendardaterangeicon.d.ts", "./node_modules/@heroicons/react/20/solid/calendardaysicon.d.ts", "./node_modules/@heroicons/react/20/solid/calendaricon.d.ts", "./node_modules/@heroicons/react/20/solid/cameraicon.d.ts", "./node_modules/@heroicons/react/20/solid/chartbarsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/chartbaricon.d.ts", "./node_modules/@heroicons/react/20/solid/chartpieicon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubblebottomcentertexticon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubblebottomcentericon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubbleleftellipsisicon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubbleleftrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubblelefticon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubbleovalleftellipsisicon.d.ts", "./node_modules/@heroicons/react/20/solid/chatbubbleovallefticon.d.ts", "./node_modules/@heroicons/react/20/solid/checkbadgeicon.d.ts", "./node_modules/@heroicons/react/20/solid/checkcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/checkicon.d.ts", "./node_modules/@heroicons/react/20/solid/chevrondoubledownicon.d.ts", "./node_modules/@heroicons/react/20/solid/chevrondoublelefticon.d.ts", "./node_modules/@heroicons/react/20/solid/chevrondoublerighticon.d.ts", "./node_modules/@heroicons/react/20/solid/chevrondoubleupicon.d.ts", "./node_modules/@heroicons/react/20/solid/chevrondownicon.d.ts", "./node_modules/@heroicons/react/20/solid/chevronlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/chevronrighticon.d.ts", "./node_modules/@heroicons/react/20/solid/chevronupdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/chevronupicon.d.ts", "./node_modules/@heroicons/react/20/solid/circlestackicon.d.ts", "./node_modules/@heroicons/react/20/solid/clipboarddocumentcheckicon.d.ts", "./node_modules/@heroicons/react/20/solid/clipboarddocumentlisticon.d.ts", "./node_modules/@heroicons/react/20/solid/clipboarddocumenticon.d.ts", "./node_modules/@heroicons/react/20/solid/clipboardicon.d.ts", "./node_modules/@heroicons/react/20/solid/clockicon.d.ts", "./node_modules/@heroicons/react/20/solid/cloudarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/cloudarrowupicon.d.ts", "./node_modules/@heroicons/react/20/solid/cloudicon.d.ts", "./node_modules/@heroicons/react/20/solid/codebracketsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/codebracketicon.d.ts", "./node_modules/@heroicons/react/20/solid/cog6toothicon.d.ts", "./node_modules/@heroicons/react/20/solid/cog8toothicon.d.ts", "./node_modules/@heroicons/react/20/solid/cogicon.d.ts", "./node_modules/@heroicons/react/20/solid/commandlineicon.d.ts", "./node_modules/@heroicons/react/20/solid/computerdesktopicon.d.ts", "./node_modules/@heroicons/react/20/solid/cpuchipicon.d.ts", "./node_modules/@heroicons/react/20/solid/creditcardicon.d.ts", "./node_modules/@heroicons/react/20/solid/cubetransparenticon.d.ts", "./node_modules/@heroicons/react/20/solid/cubeicon.d.ts", "./node_modules/@heroicons/react/20/solid/currencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/20/solid/currencydollaricon.d.ts", "./node_modules/@heroicons/react/20/solid/currencyeuroicon.d.ts", "./node_modules/@heroicons/react/20/solid/currencypoundicon.d.ts", "./node_modules/@heroicons/react/20/solid/currencyrupeeicon.d.ts", "./node_modules/@heroicons/react/20/solid/currencyyenicon.d.ts", "./node_modules/@heroicons/react/20/solid/cursorarrowraysicon.d.ts", "./node_modules/@heroicons/react/20/solid/cursorarrowrippleicon.d.ts", "./node_modules/@heroicons/react/20/solid/devicephonemobileicon.d.ts", "./node_modules/@heroicons/react/20/solid/devicetableticon.d.ts", "./node_modules/@heroicons/react/20/solid/divideicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentarrowupicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentchartbaricon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcheckicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencydollaricon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencyeuroicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencypoundicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencyrupeeicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentcurrencyyenicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentduplicateicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentmagnifyingglassicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentminusicon.d.ts", "./node_modules/@heroicons/react/20/solid/documentplusicon.d.ts", "./node_modules/@heroicons/react/20/solid/documenttexticon.d.ts", "./node_modules/@heroicons/react/20/solid/documenticon.d.ts", "./node_modules/@heroicons/react/20/solid/ellipsishorizontalcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/ellipsishorizontalicon.d.ts", "./node_modules/@heroicons/react/20/solid/ellipsisverticalicon.d.ts", "./node_modules/@heroicons/react/20/solid/envelopeopenicon.d.ts", "./node_modules/@heroicons/react/20/solid/envelopeicon.d.ts", "./node_modules/@heroicons/react/20/solid/equalsicon.d.ts", "./node_modules/@heroicons/react/20/solid/exclamationcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/exclamationtriangleicon.d.ts", "./node_modules/@heroicons/react/20/solid/eyedroppericon.d.ts", "./node_modules/@heroicons/react/20/solid/eyeslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/eyeicon.d.ts", "./node_modules/@heroicons/react/20/solid/facefrownicon.d.ts", "./node_modules/@heroicons/react/20/solid/facesmileicon.d.ts", "./node_modules/@heroicons/react/20/solid/filmicon.d.ts", "./node_modules/@heroicons/react/20/solid/fingerprinticon.d.ts", "./node_modules/@heroicons/react/20/solid/fireicon.d.ts", "./node_modules/@heroicons/react/20/solid/flagicon.d.ts", "./node_modules/@heroicons/react/20/solid/folderarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/folderminusicon.d.ts", "./node_modules/@heroicons/react/20/solid/folderopenicon.d.ts", "./node_modules/@heroicons/react/20/solid/folderplusicon.d.ts", "./node_modules/@heroicons/react/20/solid/foldericon.d.ts", "./node_modules/@heroicons/react/20/solid/forwardicon.d.ts", "./node_modules/@heroicons/react/20/solid/funnelicon.d.ts", "./node_modules/@heroicons/react/20/solid/gificon.d.ts", "./node_modules/@heroicons/react/20/solid/gifttopicon.d.ts", "./node_modules/@heroicons/react/20/solid/gifticon.d.ts", "./node_modules/@heroicons/react/20/solid/globealticon.d.ts", "./node_modules/@heroicons/react/20/solid/globeamericasicon.d.ts", "./node_modules/@heroicons/react/20/solid/globeasiaaustraliaicon.d.ts", "./node_modules/@heroicons/react/20/solid/globeeuropeafricaicon.d.ts", "./node_modules/@heroicons/react/20/solid/h1icon.d.ts", "./node_modules/@heroicons/react/20/solid/h2icon.d.ts", "./node_modules/@heroicons/react/20/solid/h3icon.d.ts", "./node_modules/@heroicons/react/20/solid/handraisedicon.d.ts", "./node_modules/@heroicons/react/20/solid/handthumbdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/handthumbupicon.d.ts", "./node_modules/@heroicons/react/20/solid/hashtagicon.d.ts", "./node_modules/@heroicons/react/20/solid/hearticon.d.ts", "./node_modules/@heroicons/react/20/solid/homemodernicon.d.ts", "./node_modules/@heroicons/react/20/solid/homeicon.d.ts", "./node_modules/@heroicons/react/20/solid/identificationicon.d.ts", "./node_modules/@heroicons/react/20/solid/inboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/20/solid/inboxstackicon.d.ts", "./node_modules/@heroicons/react/20/solid/inboxicon.d.ts", "./node_modules/@heroicons/react/20/solid/informationcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/italicicon.d.ts", "./node_modules/@heroicons/react/20/solid/keyicon.d.ts", "./node_modules/@heroicons/react/20/solid/languageicon.d.ts", "./node_modules/@heroicons/react/20/solid/lifebuoyicon.d.ts", "./node_modules/@heroicons/react/20/solid/lightbulbicon.d.ts", "./node_modules/@heroicons/react/20/solid/linkslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/linkicon.d.ts", "./node_modules/@heroicons/react/20/solid/listbulleticon.d.ts", "./node_modules/@heroicons/react/20/solid/lockclosedicon.d.ts", "./node_modules/@heroicons/react/20/solid/lockopenicon.d.ts", "./node_modules/@heroicons/react/20/solid/magnifyingglasscircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/magnifyingglassminusicon.d.ts", "./node_modules/@heroicons/react/20/solid/magnifyingglassplusicon.d.ts", "./node_modules/@heroicons/react/20/solid/magnifyingglassicon.d.ts", "./node_modules/@heroicons/react/20/solid/mappinicon.d.ts", "./node_modules/@heroicons/react/20/solid/mapicon.d.ts", "./node_modules/@heroicons/react/20/solid/megaphoneicon.d.ts", "./node_modules/@heroicons/react/20/solid/microphoneicon.d.ts", "./node_modules/@heroicons/react/20/solid/minuscircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/minussmallicon.d.ts", "./node_modules/@heroicons/react/20/solid/minusicon.d.ts", "./node_modules/@heroicons/react/20/solid/moonicon.d.ts", "./node_modules/@heroicons/react/20/solid/musicalnoteicon.d.ts", "./node_modules/@heroicons/react/20/solid/newspapericon.d.ts", "./node_modules/@heroicons/react/20/solid/nosymbolicon.d.ts", "./node_modules/@heroicons/react/20/solid/numberedlisticon.d.ts", "./node_modules/@heroicons/react/20/solid/paintbrushicon.d.ts", "./node_modules/@heroicons/react/20/solid/paperairplaneicon.d.ts", "./node_modules/@heroicons/react/20/solid/paperclipicon.d.ts", "./node_modules/@heroicons/react/20/solid/pausecircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/pauseicon.d.ts", "./node_modules/@heroicons/react/20/solid/pencilsquareicon.d.ts", "./node_modules/@heroicons/react/20/solid/pencilicon.d.ts", "./node_modules/@heroicons/react/20/solid/percentbadgeicon.d.ts", "./node_modules/@heroicons/react/20/solid/phonearrowdownlefticon.d.ts", "./node_modules/@heroicons/react/20/solid/phonearrowuprighticon.d.ts", "./node_modules/@heroicons/react/20/solid/phonexmarkicon.d.ts", "./node_modules/@heroicons/react/20/solid/phoneicon.d.ts", "./node_modules/@heroicons/react/20/solid/photoicon.d.ts", "./node_modules/@heroicons/react/20/solid/playcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/playpauseicon.d.ts", "./node_modules/@heroicons/react/20/solid/playicon.d.ts", "./node_modules/@heroicons/react/20/solid/pluscircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/plussmallicon.d.ts", "./node_modules/@heroicons/react/20/solid/plusicon.d.ts", "./node_modules/@heroicons/react/20/solid/powericon.d.ts", "./node_modules/@heroicons/react/20/solid/presentationchartbaricon.d.ts", "./node_modules/@heroicons/react/20/solid/presentationchartlineicon.d.ts", "./node_modules/@heroicons/react/20/solid/printericon.d.ts", "./node_modules/@heroicons/react/20/solid/puzzlepieceicon.d.ts", "./node_modules/@heroicons/react/20/solid/qrcodeicon.d.ts", "./node_modules/@heroicons/react/20/solid/questionmarkcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/queuelisticon.d.ts", "./node_modules/@heroicons/react/20/solid/radioicon.d.ts", "./node_modules/@heroicons/react/20/solid/receiptpercenticon.d.ts", "./node_modules/@heroicons/react/20/solid/receiptrefundicon.d.ts", "./node_modules/@heroicons/react/20/solid/rectanglegroupicon.d.ts", "./node_modules/@heroicons/react/20/solid/rectanglestackicon.d.ts", "./node_modules/@heroicons/react/20/solid/rocketlaunchicon.d.ts", "./node_modules/@heroicons/react/20/solid/rssicon.d.ts", "./node_modules/@heroicons/react/20/solid/scaleicon.d.ts", "./node_modules/@heroicons/react/20/solid/scissorsicon.d.ts", "./node_modules/@heroicons/react/20/solid/serverstackicon.d.ts", "./node_modules/@heroicons/react/20/solid/servericon.d.ts", "./node_modules/@heroicons/react/20/solid/shareicon.d.ts", "./node_modules/@heroicons/react/20/solid/shieldcheckicon.d.ts", "./node_modules/@heroicons/react/20/solid/shieldexclamationicon.d.ts", "./node_modules/@heroicons/react/20/solid/shoppingbagicon.d.ts", "./node_modules/@heroicons/react/20/solid/shoppingcarticon.d.ts", "./node_modules/@heroicons/react/20/solid/signalslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/signalicon.d.ts", "./node_modules/@heroicons/react/20/solid/slashicon.d.ts", "./node_modules/@heroicons/react/20/solid/sparklesicon.d.ts", "./node_modules/@heroicons/react/20/solid/speakerwaveicon.d.ts", "./node_modules/@heroicons/react/20/solid/speakerxmarkicon.d.ts", "./node_modules/@heroicons/react/20/solid/square2stackicon.d.ts", "./node_modules/@heroicons/react/20/solid/square3stack3dicon.d.ts", "./node_modules/@heroicons/react/20/solid/squares2x2icon.d.ts", "./node_modules/@heroicons/react/20/solid/squaresplusicon.d.ts", "./node_modules/@heroicons/react/20/solid/staricon.d.ts", "./node_modules/@heroicons/react/20/solid/stopcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/stopicon.d.ts", "./node_modules/@heroicons/react/20/solid/strikethroughicon.d.ts", "./node_modules/@heroicons/react/20/solid/sunicon.d.ts", "./node_modules/@heroicons/react/20/solid/swatchicon.d.ts", "./node_modules/@heroicons/react/20/solid/tablecellsicon.d.ts", "./node_modules/@heroicons/react/20/solid/tagicon.d.ts", "./node_modules/@heroicons/react/20/solid/ticketicon.d.ts", "./node_modules/@heroicons/react/20/solid/trashicon.d.ts", "./node_modules/@heroicons/react/20/solid/trophyicon.d.ts", "./node_modules/@heroicons/react/20/solid/truckicon.d.ts", "./node_modules/@heroicons/react/20/solid/tvicon.d.ts", "./node_modules/@heroicons/react/20/solid/underlineicon.d.ts", "./node_modules/@heroicons/react/20/solid/usercircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/usergroupicon.d.ts", "./node_modules/@heroicons/react/20/solid/userminusicon.d.ts", "./node_modules/@heroicons/react/20/solid/userplusicon.d.ts", "./node_modules/@heroicons/react/20/solid/usericon.d.ts", "./node_modules/@heroicons/react/20/solid/usersicon.d.ts", "./node_modules/@heroicons/react/20/solid/variableicon.d.ts", "./node_modules/@heroicons/react/20/solid/videocameraslashicon.d.ts", "./node_modules/@heroicons/react/20/solid/videocameraicon.d.ts", "./node_modules/@heroicons/react/20/solid/viewcolumnsicon.d.ts", "./node_modules/@heroicons/react/20/solid/viewfindercircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/walleticon.d.ts", "./node_modules/@heroicons/react/20/solid/wifiicon.d.ts", "./node_modules/@heroicons/react/20/solid/windowicon.d.ts", "./node_modules/@heroicons/react/20/solid/wrenchscrewdrivericon.d.ts", "./node_modules/@heroicons/react/20/solid/wrenchicon.d.ts", "./node_modules/@heroicons/react/20/solid/xcircleicon.d.ts", "./node_modules/@heroicons/react/20/solid/xmarkicon.d.ts", "./node_modules/@heroicons/react/20/solid/index.d.ts", "./app/settings/users/components/usertable.tsx", "./node_modules/@headlessui/react/dist/types.d.ts", "./node_modules/@headlessui/react/dist/utils/render.d.ts", "./node_modules/@headlessui/react/dist/components/button/button.d.ts", "./node_modules/@headlessui/react/dist/components/checkbox/checkbox.d.ts", "./node_modules/@headlessui/react/dist/components/close-button/close-button.d.ts", "./node_modules/@headlessui/react/dist/hooks/use-by-comparator.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.d.mts", "./node_modules/@floating-ui/react/dist/floating-ui.react.d.mts", "./node_modules/@headlessui/react/dist/internal/floating.d.ts", "./node_modules/@headlessui/react/dist/components/label/label.d.ts", "./node_modules/@headlessui/react/dist/components/combobox/combobox.d.ts", "./node_modules/@headlessui/react/dist/components/data-interactive/data-interactive.d.ts", "./node_modules/@headlessui/react/dist/components/description/description.d.ts", "./node_modules/@headlessui/react/dist/components/dialog/dialog.d.ts", "./node_modules/@headlessui/react/dist/components/disclosure/disclosure.d.ts", "./node_modules/@headlessui/react/dist/components/field/field.d.ts", "./node_modules/@headlessui/react/dist/components/fieldset/fieldset.d.ts", "./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.d.ts", "./node_modules/@headlessui/react/dist/components/input/input.d.ts", "./node_modules/@headlessui/react/dist/components/legend/legend.d.ts", "./node_modules/@headlessui/react/dist/components/listbox/listbox.d.ts", "./node_modules/@headlessui/react/dist/components/menu/menu.d.ts", "./node_modules/@headlessui/react/dist/components/popover/popover.d.ts", "./node_modules/@headlessui/react/dist/components/portal/portal.d.ts", "./node_modules/@headlessui/react/dist/components/radio-group/radio-group.d.ts", "./node_modules/@headlessui/react/dist/components/select/select.d.ts", "./node_modules/@headlessui/react/dist/components/switch/switch.d.ts", "./node_modules/@headlessui/react/dist/components/tabs/tabs.d.ts", "./node_modules/@headlessui/react/dist/components/textarea/textarea.d.ts", "./node_modules/@headlessui/react/dist/internal/close-provider.d.ts", "./node_modules/@headlessui/react/dist/components/transition/transition.d.ts", "./node_modules/@headlessui/react/dist/index.d.ts", "./app/settings/users/components/addusermodal.tsx", "./app/settings/users/components/editusermodal.tsx", "./app/settings/users/components/deleteuserdialog.tsx", "./node_modules/@types/qrcode/index.d.ts", "./app/settings/users/components/userdetailsmodal.tsx", "./app/settings/users/page.tsx", "./components/dynamictable/dynamictable.tsx", "./components/itemstable/loadingdemo.tsx", "./.next/types/app/layout.ts", "./.next/types/app/page.ts", "./.next/types/app/auth/page.ts", "./.next/types/app/dashboard/layout.ts", "./.next/types/app/dashboard/page.ts", "./.next/types/app/logs/layout.ts", "./.next/types/app/logs/page.ts", "./.next/types/app/purchasing/layout.ts", "./.next/types/app/purchasing/suppliers/page.ts", "./.next/types/app/sales/layout.ts", "./.next/types/app/sales/orders/page.ts", "./.next/types/app/sales/quotes/page.ts", "./.next/types/app/sales/sales/page.ts", "./node_modules/@types/json5/index.d.ts", "./node_modules/@types/parse-json/index.d.ts", "./node_modules/@types/raf/index.d.ts", "./node_modules/@types/react-transition-group/config.d.ts", "./node_modules/@types/react-transition-group/csstransition.d.ts", "./node_modules/@types/react-transition-group/switchtransition.d.ts", "./node_modules/@types/react-transition-group/transitiongroup.d.ts", "./node_modules/@types/react-transition-group/index.d.ts", "./node_modules/@types/trusted-types/lib/index.d.ts", "./node_modules/@types/trusted-types/index.d.ts"], "fileIdsList": [[97, 139, 355, 1605], [97, 139, 355, 1608], [97, 139, 355, 1612], [97, 139, 355, 1600], [97, 139, 355, 1787], [97, 139, 355, 1789], [97, 139, 355, 1602], [97, 139, 355, 1795], [97, 139, 355, 1800], [97, 139, 355, 1806], [97, 139, 355, 1815], [97, 139, 355, 1816], [97, 139, 355, 1817], [97, 139, 400], [97, 139], [85, 97, 139, 390, 407, 440, 1603, 1604], [85, 97, 139, 390, 407, 440, 1594, 1603, 1604], [85, 97, 139, 384], [85, 97, 139, 410], [97, 139, 408, 409], [97, 139, 410, 439, 440], [97, 139, 1601, 1607], [85, 97, 139, 440, 441, 1609, 1610, 1611], [85, 97, 139, 1601, 1607], [85, 97, 139, 390, 440, 443, 1715], [85, 97, 139, 439, 440, 442, 470, 471], [85, 97, 139, 439, 440, 442, 443, 470, 471, 1715], [85, 97, 139, 439, 440, 442, 1643, 1657, 1714], [85, 97, 139], [97, 139, 408, 409, 442], [85, 97, 139, 439, 440, 442, 443, 445], [85, 97, 139, 440, 446, 469, 470], [85, 97, 139, 440, 442, 443, 469, 1722], [85, 97, 139, 440, 443, 444, 469, 1643, 1722, 1723], [97, 139, 403, 477, 1597, 1599], [85, 97, 139, 440, 447, 470, 471, 1722, 1732, 1733], [85, 97, 139, 448, 469, 886], [85, 97, 139, 448, 469], [85, 97, 139, 448, 469, 886, 1736, 1776], [85, 97, 139, 409, 440, 447, 448, 469, 886, 1722, 1733, 1777, 1778, 1779], [85, 97, 139, 440, 449, 470, 471, 1722, 1783, 1784], [85, 97, 139, 449, 1783], [85, 97, 139, 440, 459, 470, 820, 1722, 1733], [97, 139, 450], [97, 139, 450, 451], [85, 97, 139, 450, 451, 459], [97, 139, 409], [85, 97, 139, 440, 450, 451, 459, 470, 1002, 1004, 1733, 1788], [85, 97, 139, 450, 1788], [97, 139, 1601], [85, 97, 139, 439, 440, 1598], [85, 97, 139, 459, 826], [97, 139, 460, 468, 474, 475, 476, 823, 824, 825, 827], [85, 97, 139, 468, 474], [85, 97, 139, 453, 823], [85, 97, 139, 390, 440, 453, 459, 467, 472, 477, 812, 813, 814, 822], [85, 97, 139, 453, 454, 459, 473], [85, 97, 139, 453], [85, 97, 139, 453, 459, 467], [85, 97, 139, 454, 459], [85, 97, 139, 453, 454, 459, 470, 471, 473], [97, 139, 829, 831, 832], [85, 97, 139, 440, 453, 454, 477, 818, 830], [85, 97, 139, 439, 440, 453], [85, 97, 139, 440, 822], [85, 97, 139, 390, 440, 454, 459, 477, 813, 828, 832, 833, 1733], [85, 97, 139, 440, 442], [97, 139, 479, 480, 481, 482, 483, 484, 485, 811], [85, 97, 139, 472], [85, 97, 139, 472, 810], [85, 97, 139, 472, 473, 478], [85, 97, 139, 472, 473, 481, 482, 483], [85, 97, 139, 459, 472, 478], [97, 139, 815, 816, 819, 821], [85, 97, 139, 440, 467, 477, 816, 818], [85, 97, 139, 440, 445, 472, 818, 820], [85, 97, 139, 440, 467, 472], [97, 139, 408, 409, 467, 817], [97, 139, 472], [97, 139, 453], [85, 97, 139, 390, 440, 459, 477, 834, 859, 869, 872, 873, 1733], [97, 139, 827, 835, 836, 837, 838, 839, 866, 867, 868], [85, 97, 139, 826, 837], [85, 97, 139, 826, 866], [85, 97, 139, 390, 440, 459, 477, 826, 841, 850, 851, 858, 859, 864, 865], [85, 97, 139, 826], [85, 97, 139, 459, 834], [85, 97, 139, 459, 470, 826, 836], [97, 139, 870, 871, 872], [85, 97, 139, 440, 477, 826, 830, 834, 865], [85, 97, 139, 439, 440, 826], [85, 97, 139, 440, 864], [97, 139, 842, 844, 845, 846, 847, 848, 849, 850], [85, 97, 139, 841], [85, 97, 139, 810, 841], [85, 97, 139, 841, 843], [85, 97, 139, 840, 841, 843], [85, 97, 139, 459, 840, 841, 843, 845, 846, 847], [85, 97, 139, 459, 840, 841], [97, 139, 860, 861, 862, 863], [85, 97, 139, 440, 826, 858, 860], [85, 97, 139, 440, 841], [85, 97, 139, 440, 841, 858], [97, 139, 408, 409, 817, 858, 865], [97, 139, 841], [97, 139, 826], [97, 139, 1733], [97, 139, 876, 877], [85, 97, 139, 469], [85, 97, 139, 865], [85, 97, 139, 469, 865], [97, 139, 879, 880], [85, 97, 139, 477, 865, 875], [85, 97, 139, 440, 470, 477, 865, 875, 878, 881, 1799], [97, 139, 439, 865], [97, 139, 883], [85, 97, 139, 459, 882], [85, 97, 139, 469, 886], [97, 139, 889, 890], [85, 97, 139, 886, 888], [85, 97, 139, 886], [97, 139, 408], [97, 139, 892, 894], [85, 97, 139, 477, 886, 888, 893], [85, 97, 139, 469, 886, 1736, 1776], [85, 97, 139, 371, 440, 469, 886, 888, 1733, 1810], [85, 97, 139, 440, 470, 477, 886, 888, 891, 893, 895, 1807, 1808], [85, 97, 139, 469, 885, 887], [85, 97, 139, 440, 469, 477, 885, 887, 888], [85, 97, 139, 440, 469, 470, 471, 477, 885, 887, 888, 1733, 1812, 1813], [97, 139, 439, 885], [97, 139, 439, 886], [97, 139, 883, 898, 899, 902, 903, 904, 923, 924, 925], [85, 97, 139, 899, 902], [85, 97, 139, 459, 896, 897, 901], [85, 97, 139, 896], [85, 97, 139, 459, 467, 896], [85, 97, 139, 459], [85, 97, 139, 459, 470, 471, 896, 897, 901], [85, 97, 139, 896, 923], [85, 97, 139, 390, 440, 459, 467, 477, 896, 900, 914, 915, 916, 922], [97, 139, 927, 928, 929], [85, 97, 139, 440, 477, 830, 886, 896, 897], [85, 97, 139, 439, 440, 896], [85, 97, 139, 440, 922], [97, 139, 896], [85, 97, 139, 390, 440, 459, 477, 897, 915, 926, 929, 930, 1733], [97, 139, 906, 907, 908, 909, 910, 911, 912, 913], [85, 97, 139, 900], [85, 97, 139, 900, 901, 908, 909, 910], [85, 97, 139, 810, 900], [85, 97, 139, 900, 901, 905], [85, 97, 139, 459, 900, 905], [97, 139, 917, 919, 920, 921], [85, 97, 139, 440, 467, 477, 896, 917, 918], [85, 97, 139, 440, 445, 820, 900, 918], [85, 97, 139, 440, 467, 900], [97, 139, 408, 409, 467, 817, 886, 896], [97, 139, 900], [97, 139, 883, 933, 934, 937, 938, 939, 960, 961, 962], [85, 97, 139, 934, 937], [85, 97, 139, 931, 960], [85, 97, 139, 390, 440, 459, 467, 477, 931, 935, 949, 950, 951, 959], [85, 97, 139, 459, 931, 932, 936], [85, 97, 139, 931], [85, 97, 139, 459, 467, 931], [85, 97, 139, 459, 932], [85, 97, 139, 459, 470, 471, 931, 932, 936], [97, 139, 964, 965, 966], [85, 97, 139, 440, 477, 830, 886, 931, 932, 954], [85, 97, 139, 439, 440, 931], [85, 97, 139, 440, 959], [85, 97, 139, 390, 440, 459, 477, 932, 950, 963, 966, 967, 1733], [97, 139, 941, 942, 943, 944, 945, 946, 947, 948], [85, 97, 139, 935], [85, 97, 139, 810, 935], [85, 97, 139, 935, 936, 940], [85, 97, 139, 935, 936, 943, 944, 945], [85, 97, 139, 459, 935, 940], [97, 139, 952, 953, 955, 956, 957, 958], [85, 97, 139, 440, 467, 477, 953, 954], [85, 97, 139, 440, 445, 820, 935, 954], [85, 97, 139, 440, 467, 935], [85, 97, 139, 408, 409, 440, 467, 477, 957], [97, 139, 408, 409, 467, 817, 886, 931], [97, 139, 467], [97, 139, 935], [97, 139, 931], [97, 139, 883, 969, 970, 973, 974, 975, 993, 994, 995], [85, 97, 139, 970, 973], [85, 97, 139, 882, 993], [85, 97, 139, 390, 440, 459, 467, 477, 882, 893, 971, 985, 986, 992], [85, 97, 139, 459, 882, 968, 972], [85, 97, 139, 882], [85, 97, 139, 459, 467, 882], [85, 97, 139, 459, 968], [85, 97, 139, 459, 470, 471, 882, 968, 972], [97, 139, 997, 998, 999], [85, 97, 139, 440, 477, 830, 882, 886, 968, 989], [85, 97, 139, 439, 440, 882], [85, 97, 139, 440, 992], [85, 97, 139, 390, 440, 459, 477, 893, 968, 996, 999, 1000, 1733], [97, 139, 977, 978, 979, 980, 981, 982, 983, 984], [85, 97, 139, 971], [85, 97, 139, 810, 971], [85, 97, 139, 971, 972, 976], [85, 97, 139, 971, 972, 979, 980, 981], [85, 97, 139, 459, 971, 976], [97, 139, 987, 988, 990, 991], [85, 97, 139, 440, 467, 477, 988, 989], [85, 97, 139, 440, 445, 820, 971, 989], [85, 97, 139, 440, 467, 971], [97, 139, 408, 409, 467, 817, 882, 886], [97, 139, 971], [97, 139, 882], [85, 97, 139, 439, 440, 459, 477, 817, 830, 1002, 1733, 1820], [85, 97, 139, 439, 440, 470, 471, 1001, 1643], [85, 97, 139, 449, 1002, 1643, 1657, 1714, 2148, 2185], [85, 97, 139, 2185], [85, 97, 139, 439, 440, 449, 1002, 2185, 2189], [85, 97, 139, 449, 470, 471, 1002, 2148], [85, 97, 139, 439, 440, 449, 477, 1002, 1820, 2149, 2186, 2187, 2188, 2190], [97, 139, 409, 461], [85, 97, 139, 440, 459, 461, 463, 464, 465], [85, 97, 139, 440, 461, 462], [97, 139, 461, 462, 463, 464, 466], [97, 139, 461], [85, 97, 139, 1167, 1515, 1518, 1521, 1524, 1533, 1536, 1539, 1591, 1592], [97, 139, 1591], [97, 139, 1590], [85, 97, 139, 470, 471], [85, 97, 139, 390, 440], [85, 97, 139, 390, 401, 440, 459], [97, 139, 852], [85, 97, 139, 852, 853], [97, 139, 852, 853, 854, 855, 857], [85, 97, 139, 440, 459, 852, 854, 855, 856], [85, 97, 139, 382, 390, 401, 440, 459, 820], [97, 139, 1003], [85, 97, 139, 440, 459, 1002], [85, 97, 139, 390], [85, 97, 139, 1593], [97, 139, 403, 404], [97, 139, 1011, 1012], [97, 139, 1013, 1014], [97, 139, 1013], [85, 97, 139, 1017, 1020], [85, 97, 139, 1015], [97, 139, 1011, 1017], [97, 139, 1015, 1017, 1018, 1019, 1020, 1022, 1023, 1024, 1025, 1026], [85, 97, 139, 1021], [97, 139, 1017], [85, 97, 139, 1019], [97, 139, 1021], [97, 139, 1027], [83, 97, 139, 1011], [97, 139, 1016], [97, 139, 1007], [97, 139, 1017, 1028, 1029, 1030], [97, 139, 1017, 1028, 1029], [97, 139, 1031, 1032], [97, 139, 1031], [97, 139, 1009], [97, 139, 1008], [97, 139, 1010], [97, 139, 2156], [97, 139, 2157, 2158], [85, 97, 139, 2159], [85, 97, 139, 2160], [85, 97, 139, 2150, 2151], [85, 97, 139, 2152], [85, 97, 139, 2150, 2151, 2155, 2162, 2163], [85, 97, 139, 2150, 2151, 2166], [85, 97, 139, 2150, 2151, 2163], [85, 97, 139, 2150, 2151, 2162], [85, 97, 139, 2150, 2151, 2155, 2163, 2166], [85, 97, 139, 2150, 2151, 2163, 2166], [97, 139, 2152, 2153, 2154, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184], [85, 97, 139, 2160, 2161], [85, 97, 139, 2150], [97, 139, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147], [97, 139, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809], [97, 139, 1713], [97, 139, 1643, 1657, 1712], [85, 97, 139, 1145, 1152, 1166, 1170, 1223, 1316, 1590], [97, 139, 1316, 1317], [85, 97, 139, 1145, 1156, 1310, 1590], [97, 139, 1310, 1311], [85, 97, 139, 1145, 1156, 1313, 1590], [97, 139, 1313, 1314], [85, 97, 139, 1145, 1152, 1161, 1170, 1319, 1590], [97, 139, 1319, 1320], [85, 97, 139, 1005, 1145, 1155, 1156, 1164, 1167, 1168, 1170, 1590], [97, 139, 1168, 1171], [85, 97, 139, 1145, 1175, 1176, 1590], [97, 139, 1176, 1177], [85, 97, 139, 1005, 1145, 1152, 1166, 1179, 1590], [97, 139, 1179, 1180], [85, 97, 139, 1005, 1145, 1156, 1164, 1167, 1170, 1184, 1210, 1212, 1213, 1590], [97, 139, 1213, 1214], [85, 97, 139, 1005, 1145, 1152, 1155, 1170, 1216, 1590], [97, 139, 1216, 1217], [85, 97, 139, 1005, 1145, 1170, 1218, 1219, 1590], [97, 139, 1219, 1220], [85, 97, 139, 1145, 1152, 1170, 1223, 1225, 1226, 1590], [97, 139, 1226, 1227], [85, 97, 139, 1005, 1145, 1152, 1170, 1229, 1590], [97, 139, 1229, 1230], [85, 97, 139, 1145, 1152, 1235, 1590], [97, 139, 1235, 1236], [85, 97, 139, 1145, 1152, 1161, 1170, 1232, 1590], [97, 139, 1232, 1233], [85, 97, 139, 1145, 1152, 1155, 1170, 1238, 1590], [97, 139, 1238, 1239], [85, 97, 139, 1005, 1145, 1152, 1161, 1246, 1590], [97, 139, 1246, 1247], [85, 97, 139, 1145, 1152, 1158, 1159, 1590], [97, 139, 1157, 1159, 1160], [85, 97, 139, 1156, 1157], [85, 97, 139, 1005, 1145, 1152, 1241, 1590], [85, 97, 139, 1242], [97, 139, 1241, 1242, 1243, 1244], [85, 97, 139, 1005, 1145, 1152, 1167, 1264, 1590], [97, 139, 1264, 1265], [85, 97, 139, 1145, 1152, 1161, 1170, 1249, 1590], [97, 139, 1249, 1250], [85, 97, 139, 1145, 1156, 1252, 1590], [97, 139, 1252, 1253], [85, 97, 139, 1145, 1152, 1255, 1590], [97, 139, 1255, 1256], [85, 97, 139, 1145, 1152, 1170, 1175, 1258, 1590], [97, 139, 1258, 1259], [85, 97, 139, 1145, 1152, 1261, 1590], [97, 139, 1261, 1262], [85, 97, 139, 1005, 1145, 1156, 1170, 1268, 1269, 1590], [97, 139, 1269, 1270], [85, 97, 139, 1005, 1145, 1152, 1170, 1182, 1590], [97, 139, 1182, 1183], [85, 97, 139, 1005, 1145, 1156, 1272, 1590], [97, 139, 1272, 1273], [97, 139, 1464], [85, 97, 139, 1145, 1156, 1223, 1275, 1590], [97, 139, 1275, 1276], [85, 97, 139, 1145, 1152, 1278, 1590], [97, 139, 1145], [97, 139, 1278, 1279], [85, 97, 139, 1590], [97, 139, 1281], [85, 97, 139, 1145, 1156, 1167, 1170, 1223, 1228, 1295, 1296, 1590], [97, 139, 1296, 1297], [85, 97, 139, 1145, 1156, 1283, 1590], [97, 139, 1283, 1284], [85, 97, 139, 1145, 1156, 1286, 1590], [97, 139, 1286, 1287], [85, 97, 139, 1145, 1152, 1175, 1289, 1590], [97, 139, 1289, 1290], [85, 97, 139, 1145, 1152, 1175, 1299, 1590], [97, 139, 1299, 1300], [85, 97, 139, 1005, 1145, 1152, 1302, 1590], [97, 139, 1302, 1303], [85, 97, 139, 1145, 1156, 1167, 1170, 1223, 1228, 1295, 1306, 1307, 1590], [97, 139, 1307, 1308], [85, 97, 139, 1005, 1145, 1152, 1161, 1322, 1590], [97, 139, 1322, 1323], [85, 97, 139, 1223], [97, 139, 1224], [97, 139, 1145, 1156, 1327, 1328, 1590], [97, 139, 1328, 1329], [85, 97, 139, 1005, 1145, 1152, 1334, 1590], [85, 97, 139, 1335], [97, 139, 1334, 1335, 1336, 1337], [97, 139, 1336], [85, 97, 139, 1145, 1156, 1170, 1175, 1331, 1590], [97, 139, 1331, 1332], [85, 97, 139, 1145, 1156, 1339, 1590], [97, 139, 1339, 1340], [85, 97, 139, 1005, 1145, 1152, 1342, 1590], [97, 139, 1342, 1343], [85, 97, 139, 1005, 1145, 1152, 1345, 1590], [97, 139, 1345, 1346], [97, 139, 1005, 1145, 1590], [97, 139, 1351, 1352], [85, 97, 139, 1005, 1145, 1152, 1348, 1590], [97, 139, 1348, 1349], [85, 97, 139, 1005, 1145, 1152, 1354, 1590], [97, 139, 1354, 1355], [85, 97, 139, 1005, 1145, 1152, 1161, 1162, 1590], [97, 139, 1162, 1163], [85, 97, 139, 1005, 1145, 1152, 1357, 1590], [97, 139, 1357, 1358], [85, 97, 139, 1145, 1152, 1363, 1590], [97, 139, 1363, 1364], [85, 97, 139, 1145, 1156, 1360, 1590], [97, 139, 1360, 1361], [97, 139, 1372, 1373], [97, 139, 1145, 1156, 1327, 1372, 1590], [97, 139, 1366, 1367], [85, 97, 139, 1145, 1152, 1366, 1590], [97, 139, 1325, 1326], [85, 97, 139, 1005, 1145, 1156, 1325, 1590], [97, 139, 1369, 1370], [85, 97, 139, 1005, 1145, 1152, 1347, 1369, 1590], [97, 139, 1005, 1590], [85, 97, 139, 1156, 1161, 1170, 1267], [97, 139, 1375, 1376], [85, 97, 139, 1005, 1145, 1156, 1375, 1590], [97, 139, 1378, 1379], [85, 97, 139, 1005, 1145, 1152, 1175, 1378, 1590], [97, 139, 1399, 1400], [85, 97, 139, 1145, 1152, 1399, 1590], [97, 139, 1387, 1388], [85, 97, 139, 1145, 1152, 1387, 1590], [97, 139, 1381, 1382], [97, 139, 1145, 1156, 1381, 1590], [97, 139, 1390, 1391], [85, 97, 139, 1145, 1152, 1161, 1390, 1590], [97, 139, 1384, 1385], [85, 97, 139, 1145, 1156, 1384, 1590], [97, 139, 1393, 1394], [85, 97, 139, 1145, 1156, 1393, 1590], [97, 139, 1396, 1397], [85, 97, 139, 1145, 1156, 1170, 1175, 1396, 1590], [97, 139, 1402, 1403], [85, 97, 139, 1145, 1152, 1402, 1590], [97, 139, 1413, 1414], [85, 97, 139, 1145, 1156, 1167, 1170, 1223, 1228, 1295, 1409, 1412, 1413, 1590], [97, 139, 1405, 1406], [85, 97, 139, 1145, 1152, 1161, 1405, 1590], [97, 139, 1408], [85, 97, 139, 1152, 1401], [97, 139, 1416, 1417], [85, 97, 139, 1145, 1156, 1167, 1170, 1377, 1416, 1590], [97, 139, 1292, 1293, 1294], [85, 97, 139, 1005, 1145, 1152, 1170, 1205, 1228, 1293, 1590], [97, 139, 1420, 1421], [85, 97, 139, 1145, 1156, 1374, 1419, 1420, 1590], [85, 97, 139, 1145, 1590], [97, 139, 1424, 1425], [85, 97, 139, 1145, 1156, 1170, 1327, 1424, 1590], [85, 97, 139, 1005, 1590], [97, 139, 1428, 1429], [85, 97, 139, 1005, 1145, 1156, 1427, 1428, 1590], [97, 139, 1431, 1432], [85, 97, 139, 1005, 1145, 1152, 1170, 1427, 1431, 1590], [97, 139, 1165, 1166], [85, 97, 139, 1005, 1145, 1152, 1165, 1590], [97, 139, 1410, 1411], [85, 97, 139, 1145, 1156, 1167, 1169, 1170, 1223, 1228, 1295, 1410, 1590], [85, 97, 139, 1170, 1202, 1205, 1206], [97, 139, 1207, 1208, 1209], [85, 97, 139, 1145, 1207, 1590], [97, 139, 1203, 1204], [85, 97, 139, 1203], [97, 139, 1439, 1440], [85, 97, 139, 1005, 1145, 1156, 1170, 1268, 1439, 1590], [97, 139, 1434, 1436, 1437], [85, 97, 139, 1341], [97, 139, 1341], [97, 139, 1435], [97, 139, 1442, 1443], [85, 97, 139, 1005, 1145, 1152, 1170, 1442, 1590], [97, 139, 1445, 1446], [85, 97, 139, 1145, 1152, 1445, 1590], [97, 139, 1449, 1450], [85, 97, 139, 1145, 1156, 1330, 1374, 1415, 1426, 1448, 1449, 1590], [85, 97, 139, 1145, 1415, 1590], [97, 139, 1452, 1453], [85, 97, 139, 1005, 1145, 1152, 1452, 1590], [97, 139, 1305], [97, 139, 1458, 1459], [85, 97, 139, 1005, 1145, 1152, 1170, 1455, 1457, 1458, 1590], [85, 97, 139, 1456], [97, 139, 1466, 1467], [85, 97, 139, 1145, 1156, 1170, 1223, 1463, 1465, 1466, 1590], [97, 139, 1461, 1462], [85, 97, 139, 1145, 1156, 1167, 1461, 1590], [97, 139, 1470, 1471], [85, 97, 139, 1145, 1156, 1170, 1324, 1469, 1470, 1590], [97, 139, 1476, 1477], [85, 97, 139, 1145, 1156, 1170, 1324, 1475, 1476, 1590], [97, 139, 1479, 1480], [85, 97, 139, 1145, 1156, 1479, 1590], [97, 139, 1482, 1483], [85, 97, 139, 1145, 1152, 1570], [97, 139, 1504, 1505, 1506], [85, 97, 139, 1145, 1152, 1504, 1590], [97, 139, 1485, 1486], [85, 97, 139, 1145, 1152, 1161, 1485, 1590], [97, 139, 1488, 1489], [85, 97, 139, 1145, 1156, 1488, 1590], [97, 139, 1491, 1492], [85, 97, 139, 1145, 1156, 1170, 1223, 1277, 1491, 1590], [97, 139, 1494, 1495], [85, 97, 139, 1145, 1155, 1156, 1494, 1590], [97, 139, 1497, 1498], [85, 97, 139, 1145, 1156, 1170, 1496, 1497, 1590], [97, 139, 1500, 1501, 1502], [85, 97, 139, 1145, 1152, 1167, 1500, 1590], [97, 139, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1564, 1565, 1566, 1570], [97, 139, 1564, 1565, 1566], [97, 139, 1569], [83, 97, 139, 1145], [97, 139, 1568, 1569], [97, 139, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1567, 1569], [97, 139, 1005, 1122, 1145, 1147, 1149, 1151, 1567, 1568], [85, 97, 139, 1146, 1147], [97, 139, 1146], [97, 139, 1005, 1006, 1122, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1564, 1565, 1566, 1567, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589], [97, 139, 1145, 1155, 1158, 1161, 1164, 1167, 1172, 1175, 1178, 1181, 1184, 1210, 1215, 1218, 1221, 1228, 1231, 1234, 1237, 1240, 1245, 1248, 1251, 1254, 1257, 1260, 1263, 1266, 1271, 1274, 1277, 1280, 1285, 1288, 1291, 1295, 1298, 1301, 1304, 1309, 1312, 1315, 1318, 1321, 1324, 1327, 1330, 1333, 1338, 1341, 1344, 1347, 1350, 1353, 1356, 1359, 1362, 1365, 1368, 1371, 1374, 1377, 1380, 1383, 1386, 1389, 1392, 1395, 1398, 1401, 1404, 1407, 1409, 1412, 1415, 1418, 1422, 1426, 1430, 1433, 1438, 1441, 1444, 1447, 1451, 1454, 1460, 1463, 1468, 1472, 1475, 1478, 1481, 1484, 1487, 1490, 1493, 1496, 1499, 1503, 1507, 1512, 1515, 1518, 1521, 1524, 1527, 1531, 1533, 1536, 1539, 1542, 1545, 1548, 1554, 1557, 1560, 1563, 1564], [97, 139, 1155, 1158, 1161, 1164, 1167, 1172, 1175, 1178, 1181, 1184, 1210, 1215, 1218, 1221, 1228, 1231, 1234, 1237, 1240, 1245, 1248, 1251, 1254, 1257, 1260, 1263, 1266, 1271, 1274, 1277, 1280, 1282, 1285, 1288, 1291, 1295, 1298, 1301, 1304, 1309, 1312, 1315, 1318, 1321, 1324, 1327, 1330, 1333, 1338, 1341, 1344, 1347, 1350, 1353, 1356, 1359, 1362, 1365, 1368, 1371, 1374, 1377, 1380, 1383, 1386, 1389, 1392, 1395, 1398, 1401, 1404, 1407, 1409, 1412, 1415, 1418, 1422, 1423, 1426, 1430, 1433, 1438, 1441, 1444, 1447, 1451, 1454, 1460, 1463, 1468, 1472, 1475, 1478, 1481, 1484, 1487, 1490, 1493, 1496, 1499, 1503, 1507, 1509, 1512, 1515, 1518, 1521, 1524, 1527, 1531, 1533, 1536, 1539, 1542, 1545, 1548, 1554, 1557, 1560, 1563], [97, 139, 1145, 1148], [97, 139, 1145, 1570, 1578, 1579], [85, 97, 139, 1122, 1145, 1568], [85, 97, 139, 1114, 1145, 1569], [97, 139, 1570], [97, 139, 1567, 1570], [97, 139, 1145, 1564], [97, 139, 1153, 1154], [85, 97, 139, 1005, 1145, 1152, 1153, 1590], [97, 139, 1508], [85, 97, 139, 1170, 1309], [97, 139, 1510, 1511], [85, 97, 139, 1005, 1145, 1156, 1170, 1268, 1510, 1590], [97, 139, 1546, 1547], [85, 97, 139, 1145, 1152, 1161, 1546, 1590], [97, 139, 1534, 1535], [85, 97, 139, 1005, 1145, 1152, 1534, 1590], [97, 139, 1513, 1514], [85, 97, 139, 1145, 1152, 1513, 1590], [97, 139, 1516, 1517], [85, 97, 139, 1005, 1145, 1156, 1516, 1590], [97, 139, 1519, 1520], [85, 97, 139, 1145, 1152, 1519, 1590], [97, 139, 1543, 1544], [85, 97, 139, 1145, 1152, 1543, 1590], [97, 139, 1522, 1523], [85, 97, 139, 1145, 1152, 1522, 1590], [97, 139, 1528, 1532], [85, 97, 139, 1145, 1152, 1164, 1170, 1407, 1451, 1518, 1527, 1528, 1531, 1590], [97, 139, 1525, 1526], [85, 97, 139, 1155, 1163], [97, 139, 1537, 1538], [85, 97, 139, 1145, 1152, 1537, 1590], [97, 139, 1540, 1541], [85, 97, 139, 1145, 1152, 1161, 1170, 1540, 1590], [97, 139, 1552, 1553], [85, 97, 139, 1005, 1145, 1152, 1155, 1170, 1551, 1552, 1590], [97, 139, 1549, 1550], [85, 97, 139, 1145, 1155, 1161, 1170, 1549, 1590], [97, 139, 1555, 1556], [85, 97, 139, 1005, 1145, 1156, 1170, 1327, 1330, 1338, 1344, 1371, 1374, 1426, 1451, 1555, 1590], [97, 139, 1558, 1559], [85, 97, 139, 1005, 1145, 1152, 1161, 1558, 1590], [97, 139, 1561, 1562], [85, 97, 139, 1005, 1145, 1156, 1561, 1590], [97, 139, 1529, 1530], [85, 97, 139, 1005, 1145, 1152, 1529, 1590], [97, 139, 1473, 1474], [85, 97, 139, 1145, 1156, 1170, 1210, 1223, 1473, 1590], [97, 139, 1223], [85, 97, 139, 1222], [97, 139, 1173, 1174], [85, 97, 139, 1005, 1145, 1148, 1152, 1173, 1590], [97, 139, 1211], [85, 97, 139, 1005], [97, 139, 1107, 1570], [97, 139, 1145, 1169, 1590], [97, 139, 1080, 1082, 1084], [97, 139, 1081], [97, 139, 1080], [97, 139, 1083], [85, 97, 139, 1028], [97, 139, 1036], [83, 97, 139, 1028, 1033, 1035, 1037], [97, 139, 1034], [97, 139, 1040], [97, 139, 1041], [85, 97, 139, 1005, 1040, 1042, 1052, 1057, 1061, 1063, 1065, 1067, 1069, 1071, 1073, 1075, 1077, 1089], [97, 139, 1090, 1091], [97, 139, 1038, 1040, 1043, 1052, 1057], [97, 139, 1058], [97, 139, 1108], [97, 139, 1060], [97, 139, 1005, 1128], [85, 97, 139, 1005, 1052, 1057, 1127], [85, 97, 139, 1005, 1038, 1057, 1128], [97, 139, 1127, 1128, 1130], [97, 139, 1005, 1057, 1092], [97, 139, 1093], [97, 139, 1005], [97, 139, 1043], [85, 97, 139, 1038, 1052, 1057], [97, 139, 1095], [97, 139, 1038], [97, 139, 1038, 1043, 1044, 1045, 1052, 1053, 1055], [97, 139, 1053, 1056], [97, 139, 1054], [97, 139, 1066], [85, 97, 139, 1114, 1115, 1116], [97, 139, 1118], [97, 139, 1115, 1117, 1118, 1119, 1120, 1121], [97, 139, 1115], [97, 139, 1062], [97, 139, 1064], [97, 139, 1078], [85, 97, 139, 1038, 1057], [97, 139, 1086], [85, 97, 139, 1005, 1038, 1096, 1103, 1132], [97, 139, 1005, 1132], [97, 139, 1043, 1045, 1052, 1132], [85, 97, 139, 1005, 1052, 1057, 1092], [97, 139, 1132, 1133, 1134, 1135, 1136, 1137], [97, 139, 1038, 1040, 1042, 1043, 1044, 1045, 1052, 1055, 1057, 1059, 1061, 1063, 1065, 1067, 1069, 1071, 1073, 1075, 1077, 1079, 1085, 1087, 1089, 1092, 1094, 1096, 1098, 1101, 1103, 1105, 1107, 1109, 1111, 1112, 1118, 1120, 1122, 1123, 1124, 1126, 1129, 1131, 1138, 1143, 1144], [97, 139, 1113], [97, 139, 1068], [97, 139, 1070], [97, 139, 1125], [97, 139, 1072], [97, 139, 1074], [97, 139, 1088], [85, 97, 139, 1005, 1038, 1043, 1045, 1096, 1139], [97, 139, 1139, 1140, 1141, 1142], [97, 139, 1005, 1139], [97, 139, 1039], [97, 139, 1097], [97, 139, 1096], [97, 139, 1046], [97, 139, 1049], [97, 139, 1046, 1047, 1048, 1049, 1050, 1051], [83, 97, 139], [83, 97, 139, 1038, 1046, 1047, 1048], [97, 139, 1110], [97, 139, 1085], [97, 139, 1076], [97, 139, 1106], [97, 139, 1102], [97, 139, 1057], [97, 139, 1099, 1100], [97, 139, 1104], [97, 139, 1201], [97, 139, 1195, 1197], [97, 139, 1185, 1195, 1196, 1198, 1199, 1200], [97, 139, 1195], [97, 139, 1185, 1195], [97, 139, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1194], [97, 139, 1186, 1190, 1191, 1194, 1195, 1198], [97, 139, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1194, 1195, 1196, 1198, 1199], [97, 139, 1185, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1194], [97, 139, 1736], [85, 97, 139, 1736, 1744], [85, 97, 139, 1741, 1746], [85, 97, 139, 1736], [97, 139, 1736, 1741], [97, 139, 1736, 1740, 1741, 1743], [85, 97, 139, 1740], [85, 97, 139, 1736, 1740, 1741, 1743, 1744, 1746, 1747], [97, 139, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753], [97, 139, 1736, 1740, 1741, 1742], [97, 139, 1736, 1743], [97, 139, 1736, 1740], [97, 139, 1736, 1741, 1743], [97, 139, 412], [97, 139, 411, 412], [97, 139, 411, 412, 413, 414, 415, 416, 417, 418, 419], [97, 139, 411, 412, 413], [85, 97, 139, 420], [85, 97, 139, 281, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438], [97, 139, 420, 421], [85, 97, 139, 281], [97, 139, 420], [97, 139, 420, 421, 430], [97, 139, 420, 421, 423], [97, 139, 1735], [97, 136, 139], [97, 138, 139], [139], [97, 139, 144, 173], [97, 139, 140, 145, 151, 152, 159, 170, 181], [97, 139, 140, 141, 151, 159], [92, 93, 94, 97, 139], [97, 139, 142, 182], [97, 139, 143, 144, 152, 160], [97, 139, 144, 170, 178], [97, 139, 145, 147, 151, 159], [97, 138, 139, 146], [97, 139, 147, 148], [97, 139, 149, 151], [97, 138, 139, 151], [97, 139, 151, 152, 153, 170, 181], [97, 139, 151, 152, 153, 166, 170, 173], [97, 134, 139], [97, 139, 147, 151, 154, 159, 170, 181], [97, 139, 151, 152, 154, 155, 159, 170, 178, 181], [97, 139, 154, 156, 170, 178, 181], [95, 96, 97, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187], [97, 139, 151, 157], [97, 139, 158, 181, 186], [97, 139, 147, 151, 159, 170], [97, 139, 160], [97, 139, 161], [97, 138, 139, 162], [97, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187], [97, 139, 164], [97, 139, 165], [97, 139, 151, 166, 167], [97, 139, 166, 168, 182, 184], [97, 139, 151, 170, 171, 173], [97, 139, 172, 173], [97, 139, 170, 171], [97, 139, 173], [97, 139, 174], [97, 136, 139, 170], [97, 139, 151, 176, 177], [97, 139, 176, 177], [97, 139, 144, 159, 170, 178], [97, 139, 179], [97, 139, 159, 180], [97, 139, 154, 165, 181], [97, 139, 144, 182], [97, 139, 170, 183], [97, 139, 158, 184], [97, 139, 185], [97, 139, 151, 153, 162, 170, 173, 181, 184, 186], [97, 139, 170, 187], [97, 139, 170, 188], [85, 97, 139, 192, 193, 194], [85, 97, 139, 192, 193], [97, 139, 1222, 2210, 2211, 2212, 2213], [85, 89, 97, 139, 191, 356, 399], [85, 89, 97, 139, 190, 356, 399], [82, 83, 84, 97, 139], [97, 139, 2215], [90, 97, 139], [97, 139, 360], [97, 139, 362, 363, 364], [97, 139, 366], [97, 139, 197, 207, 213, 215, 356], [97, 139, 197, 204, 206, 209, 227], [97, 139, 207], [97, 139, 207, 209, 334], [97, 139, 262, 280, 295, 402], [97, 139, 304], [97, 139, 197, 207, 214, 248, 258, 331, 332, 402], [97, 139, 214, 402], [97, 139, 207, 258, 259, 260, 402], [97, 139, 207, 214, 248, 402], [97, 139, 402], [97, 139, 197, 214, 215, 402], [97, 139, 288], [97, 138, 139, 188, 287], [85, 97, 139, 281, 282, 283, 301, 302], [97, 139, 271], [97, 139, 270, 272, 376], [85, 97, 139, 281, 282, 299], [97, 139, 277, 302, 388], [97, 139, 386, 387], [97, 139, 221, 385], [97, 139, 274], [97, 138, 139, 188, 221, 237, 270, 271, 272, 273], [85, 97, 139, 299, 301, 302], [97, 139, 299, 301], [97, 139, 299, 300, 302], [97, 139, 165, 188], [97, 139, 269], [97, 138, 139, 188, 206, 208, 265, 266, 267, 268], [85, 97, 139, 198, 379], [85, 97, 139, 181, 188], [85, 97, 139, 214, 246], [85, 97, 139, 214], [97, 139, 244, 249], [85, 97, 139, 245, 359], [97, 139, 1595], [85, 89, 97, 139, 154, 188, 190, 191, 356, 397, 398], [97, 139, 356], [97, 139, 196], [97, 139, 349, 350, 351, 352, 353, 354], [97, 139, 351], [85, 97, 139, 245, 281, 359], [85, 97, 139, 281, 357, 359], [85, 97, 139, 281, 359], [97, 139, 154, 188, 208, 359], [97, 139, 154, 188, 205, 206, 217, 235, 237, 269, 274, 275, 297, 299], [97, 139, 266, 269, 274, 282, 284, 285, 286, 288, 289, 290, 291, 292, 293, 294, 402], [97, 139, 267], [85, 97, 139, 165, 188, 206, 207, 235, 237, 238, 240, 265, 297, 298, 302, 356, 402], [97, 139, 154, 188, 208, 209, 221, 222, 270], [97, 139, 154, 188, 207, 209], [97, 139, 154, 170, 188, 205, 208, 209], [97, 139, 154, 165, 181, 188, 205, 206, 207, 208, 209, 214, 217, 218, 228, 229, 231, 234, 235, 237, 238, 239, 240, 264, 265, 298, 299, 307, 309, 312, 314, 317, 319, 320, 321, 322], [97, 139, 154, 170, 188], [97, 139, 197, 198, 199, 205, 206, 356, 359, 402], [97, 139, 154, 170, 181, 188, 202, 333, 335, 336, 402], [97, 139, 165, 181, 188, 202, 205, 208, 225, 229, 231, 232, 233, 238, 265, 312, 323, 325, 331, 345, 346], [97, 139, 207, 211, 265], [97, 139, 205, 207], [97, 139, 218, 313], [97, 139, 315, 316], [97, 139, 315], [97, 139, 313], [97, 139, 315, 318], [97, 139, 201, 202], [97, 139, 201, 241], [97, 139, 201], [97, 139, 203, 218, 311], [97, 139, 310], [97, 139, 202, 203], [97, 139, 203, 308], [97, 139, 202], [97, 139, 297], [97, 139, 154, 188, 205, 217, 236, 256, 262, 276, 279, 296, 299], [97, 139, 250, 251, 252, 253, 254, 255, 277, 278, 302, 357], [97, 139, 306], [97, 139, 154, 188, 205, 217, 236, 242, 303, 305, 307, 356, 359], [97, 139, 154, 181, 188, 198, 205, 207, 264], [97, 139, 261], [97, 139, 154, 188, 339, 344], [97, 139, 228, 237, 264, 359], [97, 139, 327, 331, 345, 348], [97, 139, 154, 211, 331, 339, 340, 348], [97, 139, 197, 207, 228, 239, 342], [97, 139, 154, 188, 207, 214, 239, 326, 327, 337, 338, 341, 343], [97, 139, 189, 235, 236, 237, 356, 359], [97, 139, 154, 165, 181, 188, 203, 205, 206, 208, 211, 216, 217, 225, 228, 229, 231, 232, 233, 234, 238, 240, 264, 265, 309, 323, 324, 359], [97, 139, 154, 188, 205, 207, 211, 325, 347], [97, 139, 154, 188, 206, 208], [85, 97, 139, 154, 165, 188, 196, 198, 205, 206, 209, 217, 234, 235, 237, 238, 240, 306, 356, 359], [97, 139, 154, 165, 181, 188, 200, 203, 204, 208], [97, 139, 201, 263], [97, 139, 154, 188, 201, 206, 217], [97, 139, 154, 188, 207, 218], [97, 139, 154, 188], [97, 139, 221], [97, 139, 220], [97, 139, 222], [97, 139, 207, 219, 221, 225], [97, 139, 207, 219, 221], [97, 139, 154, 188, 200, 207, 208, 214, 222, 223, 224], [85, 97, 139, 299, 300, 301], [97, 139, 257], [85, 97, 139, 198], [85, 97, 139, 231], [85, 97, 139, 189, 234, 237, 240, 356, 359], [97, 139, 198, 379, 380], [85, 97, 139, 249], [85, 97, 139, 165, 181, 188, 196, 243, 245, 247, 248, 359], [97, 139, 208, 214, 231], [97, 139, 230], [85, 97, 139, 152, 154, 165, 188, 196, 249, 258, 356, 357, 358], [81, 85, 86, 87, 88, 97, 139, 190, 191, 356, 399], [97, 139, 144], [97, 139, 328, 329, 330], [97, 139, 328], [97, 139, 368], [97, 139, 370], [97, 139, 372], [97, 139, 1596], [97, 139, 374], [97, 139, 377], [97, 139, 381], [89, 91, 97, 139, 356, 361, 365, 367, 369, 371, 373, 375, 378, 382, 384, 390, 391, 393, 400, 401, 402], [97, 139, 383], [97, 139, 389], [97, 139, 245], [97, 139, 392], [97, 138, 139, 222, 223, 224, 225, 394, 395, 396, 399], [97, 139, 188], [85, 89, 97, 139, 154, 156, 165, 188, 190, 191, 192, 194, 196, 209, 348, 355, 359, 399], [85, 97, 139, 1628], [97, 139, 1628, 1629, 1630, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1642], [97, 139, 1628], [97, 139, 1631, 1632], [85, 97, 139, 1626, 1628], [97, 139, 1623, 1624, 1626], [97, 139, 1619, 1622, 1624, 1626], [97, 139, 1623, 1626], [85, 97, 139, 1614, 1615, 1616, 1619, 1620, 1621, 1623, 1624, 1625, 1626], [97, 139, 1616, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627], [97, 139, 1623], [97, 139, 1617, 1623, 1624], [97, 139, 1617, 1618], [97, 139, 1622, 1624, 1625], [97, 139, 1622], [97, 139, 1614, 1619, 1624, 1625], [97, 139, 1640, 1641], [85, 97, 139, 1721], [97, 139, 458], [97, 139, 455, 456, 457], [85, 97, 139, 1736, 1754], [85, 97, 139, 1736, 1754, 1757], [85, 97, 139, 1735, 1736, 1754, 1757], [97, 139, 1737, 1738, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775], [85, 97, 139, 1735, 1736, 1754], [97, 106, 110, 139, 181], [97, 106, 139, 170, 181], [97, 101, 139], [97, 103, 106, 139, 178, 181], [97, 139, 159, 178], [97, 101, 139, 188], [97, 103, 106, 139, 159, 181], [97, 98, 99, 102, 105, 139, 151, 170, 181], [97, 106, 113, 139], [97, 98, 104, 139], [97, 106, 127, 128, 139], [97, 102, 106, 139, 173, 181, 188], [97, 127, 139, 188], [97, 100, 101, 139, 188], [97, 106, 139], [97, 100, 101, 102, 103, 104, 105, 106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 128, 129, 130, 131, 132, 133, 139], [97, 106, 121, 139], [97, 106, 113, 114, 139], [97, 104, 106, 114, 115, 139], [97, 105, 139], [97, 98, 101, 106, 139], [97, 106, 110, 114, 115, 139], [97, 110, 139], [97, 104, 106, 109, 139, 181], [97, 98, 103, 106, 113, 139], [97, 139, 170], [97, 101, 106, 127, 139, 186, 188], [97, 139, 1656], [97, 139, 1646, 1647], [97, 139, 1644, 1645, 1646, 1648, 1649, 1654], [97, 139, 1645, 1646], [97, 139, 1654], [97, 139, 1655], [97, 139, 1646], [97, 139, 1644, 1645, 1646, 1649, 1650, 1651, 1652, 1653], [97, 139, 1644, 1645, 1656], [97, 139, 1659, 1661, 1662, 1663, 1664], [97, 139, 1659, 1661, 1663, 1664], [97, 139, 1659, 1661, 1663], [97, 139, 1659, 1661, 1662, 1664], [97, 139, 1659, 1661, 1664], [97, 139, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1705, 1706, 1707, 1708, 1709, 1710, 1711], [97, 139, 1661, 1664], [97, 139, 1658, 1659, 1660, 1662, 1663, 1664], [97, 139, 1661, 1706, 1710], [97, 139, 1661, 1662, 1663, 1664], [97, 139, 1663], [97, 139, 1667, 1668, 1669, 1670, 1671, 1672, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1692, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0990a7576222f248f0a3b888adcb7389f957928ce2afb1cd5128169086ff4d29", "impliedFormat": 1}, {"version": "eb5b19b86227ace1d29ea4cf81387279d04bb34051e944bc53df69f58914b788", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "87d9d29dbc745f182683f63187bf3d53fd8673e5fca38ad5eaab69798ed29fbc", "impliedFormat": 1}, {"version": "472f5aab7edc498a0a761096e8e254c5bc3323d07a1e7f5f8b8ec0d6395b60a0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cc69795d9954ee4ad57545b10c7bf1a7260d990231b1685c147ea71a6faa265c", "impliedFormat": 1}, {"version": "8bc6c94ff4f2af1f4023b7bb2379b08d3d7dd80c698c9f0b07431ea16101f05f", "impliedFormat": 1}, {"version": "1b61d259de5350f8b1e5db06290d31eaebebc6baafd5f79d314b5af9256d7153", "impliedFormat": 1}, {"version": "57194e1f007f3f2cbef26fa299d4c6b21f4623a2eddc63dfeef79e38e187a36e", "impliedFormat": 1}, {"version": "0f6666b58e9276ac3a38fdc80993d19208442d6027ab885580d93aec76b4ef00", "impliedFormat": 1}, {"version": "05fd364b8ef02fb1e174fbac8b825bdb1e5a36a016997c8e421f5fab0a6da0a0", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b042aa5d277ad6963e2837179fd2f8fbb01968ac67115b0833c0244e93d1d50", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "9e025aa38cad40827cc30aca974fe33fe2c4652fe8c88f48dadbbbd6300c8b07", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "5b75ca915164e4a7ad94a60729fe45b8a62e7750ab232d0122f8ccdd768f5314", "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "46c0484bf0a50d57256a8cfb87714450c2ecd1e5d0bc29f84740f16199f47d6a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "8caa5c86be1b793cd5f599e27ecb34252c41e011980f7d61ae4989a149ff6ccc", "impliedFormat": 1}, {"version": "3609e455ffcba8176c8ce0aa57f8258fe10cf03987e27f1fab68f702b4426521", "impliedFormat": 1}, {"version": "d1bd4e51810d159899aad1660ccb859da54e27e08b8c9862b40cd36c1d9ff00f", "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "impliedFormat": 1}, {"version": "1cfa8647d7d71cb03847d616bd79320abfc01ddea082a49569fda71ac5ece66b", "impliedFormat": 1}, {"version": "bb7a61dd55dc4b9422d13da3a6bb9cc5e89be888ef23bbcf6558aa9726b89a1c", "impliedFormat": 1}, {"version": "db6d2d9daad8a6d83f281af12ce4355a20b9a3e71b82b9f57cddcca0a8964a96", "impliedFormat": 1}, {"version": "cfe4ef4710c3786b6e23dae7c086c70b4f4835a2e4d77b75d39f9046106e83d3", "impliedFormat": 1}, {"version": "cbea99888785d49bb630dcbb1613c73727f2b5a2cf02e1abcaab7bcf8d6bf3c5", "impliedFormat": 1}, {"version": "3a8bddb66b659f6bd2ff641fc71df8a8165bafe0f4b799cc298be5cd3755bb20", "impliedFormat": 1}, {"version": "a86f82d646a739041d6702101afa82dcb935c416dd93cbca7fd754fd0282ce1f", "impliedFormat": 1}, {"version": "2dad084c67e649f0f354739ec7df7c7df0779a28a4f55c97c6b6883ae850d1ce", "impliedFormat": 1}, {"version": "fa5bbc7ab4130dd8cdc55ea294ec39f76f2bc507a0f75f4f873e38631a836ca7", "impliedFormat": 1}, {"version": "df45ca1176e6ac211eae7ddf51336dc075c5314bc5c253651bae639defd5eec5", "impliedFormat": 1}, {"version": "cf86de1054b843e484a3c9300d62fbc8c97e77f168bbffb131d560ca0474d4a8", "impliedFormat": 1}, {"version": "196c960b12253fde69b204aa4fbf69470b26daf7a430855d7f94107a16495ab0", "impliedFormat": 1}, {"version": "ee15ea5dd7a9fc9f5013832e5843031817a880bf0f24f37a29fd8337981aae07", "impliedFormat": 1}, {"version": "bf24f6d35f7318e246010ffe9924395893c4e96d34324cde77151a73f078b9ad", "impliedFormat": 1}, {"version": "ea53732769832d0f127ae16620bd5345991d26bf0b74e85e41b61b27d74ea90f", "impliedFormat": 1}, {"version": "10595c7ff5094dd5b6a959ccb1c00e6a06441b4e10a87bc09c15f23755d34439", "impliedFormat": 1}, {"version": "9620c1ff645afb4a9ab4044c85c26676f0a93e8c0e4b593aea03a89ccb47b6d0", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "a9af0e608929aaf9ce96bd7a7b99c9360636c31d73670e4af09a09950df97841", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "c86fe861cf1b4c46a0fb7d74dffe596cf679a2e5e8b1456881313170f092e3fa", "impliedFormat": 1}, {"version": "08ed0b3f0166787f84a6606f80aa3b1388c7518d78912571b203817406e471da", "impliedFormat": 1}, {"version": "47e5af2a841356a961f815e7c55d72554db0c11b4cba4d0caab91f8717846a94", "impliedFormat": 1}, {"version": "65f43099ded6073336e697512d9b80f2d4fec3182b7b2316abf712e84104db00", "impliedFormat": 1}, {"version": "f5f541902bf7ae0512a177295de9b6bcd6809ea38307a2c0a18bfca72212f368", "impliedFormat": 1}, {"version": "b0decf4b6da3ebc52ea0c96095bdfaa8503acc4ac8e9081c5f2b0824835dd3bd", "impliedFormat": 1}, {"version": "ca1b882a105a1972f82cc58e3be491e7d750a1eb074ffd13b198269f57ed9e1b", "impliedFormat": 1}, {"version": "fc3e1c87b39e5ba1142f27ec089d1966da168c04a859a4f6aab64dceae162c2b", "impliedFormat": 1}, {"version": "3b414b99a73171e1c4b7b7714e26b87d6c5cb03d200352da5342ab4088a54c85", "impliedFormat": 1}, {"version": "61888522cec948102eba94d831c873200aa97d00d8989fdfd2a3e0ee75ec65a2", "impliedFormat": 1}, {"version": "4e10622f89fea7b05dd9b52fb65e1e2b5cbd96d4cca3d9e1a60bb7f8a9cb86a1", "impliedFormat": 1}, {"version": "74b2a5e5197bd0f2e0077a1ea7c07455bbea67b87b0869d9786d55104006784f", "impliedFormat": 1}, {"version": "59bf32919de37809e101acffc120596a9e45fdbab1a99de5087f31fdc36e2f11", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "faa03dffb64286e8304a2ca96dd1317a77db6bfc7b3fb385163648f67e535d77", "impliedFormat": 1}, {"version": "c40c848daad198266370c1c72a7a8c3d18d2f50727c7859fcfefd3ff69a7f288", "impliedFormat": 1}, {"version": "ac60bbee0d4235643cc52b57768b22de8c257c12bd8c2039860540cab1fa1d82", "impliedFormat": 1}, {"version": "6428e6edd944ce6789afdf43f9376c1f2e4957eea34166177625aaff4c0da1a0", "impliedFormat": 1}, {"version": "ada39cbb2748ab2873b7835c90c8d4620723aedf323550e8489f08220e477c7f", "impliedFormat": 1}, {"version": "6e5f5cee603d67ee1ba6120815497909b73399842254fc1e77a0d5cdc51d8c9c", "impliedFormat": 1}, {"version": "8dba67056cbb27628e9b9a1cba8e57036d359dceded0725c72a3abe4b6c79cd4", "impliedFormat": 1}, {"version": "70f3814c457f54a7efe2d9ce9d2686de9250bb42eb7f4c539bd2280a42e52d33", "impliedFormat": 1}, {"version": "154dd2e22e1e94d5bc4ff7726706bc0483760bae40506bdce780734f11f7ec47", "impliedFormat": 1}, {"version": "ef61792acbfa8c27c9bd113f02731e66229f7d3a169e3c1993b508134f1a58e0", "impliedFormat": 1}, {"version": "9c82171d836c47486074e4ca8e059735bf97b205e70b196535b5efd40cbe1bc5", "impliedFormat": 1}, {"version": "0131e203d8560edb39678abe10db42564a068f98c4ebd1ed9ffe7279c78b3c81", "impliedFormat": 1}, {"version": "f6404e7837b96da3ea4d38c4f1a3812c96c9dcdf264e93d5bdb199f983a3ef4b", "impliedFormat": 1}, {"version": "c5426dbfc1cf90532f66965a7aa8c1136a78d4d0f96d8180ecbfc11d7722f1a5", "impliedFormat": 1}, {"version": "65a15fc47900787c0bd18b603afb98d33ede930bed1798fc984d5ebb78b26cf9", "impliedFormat": 1}, {"version": "9d202701f6e0744adb6314d03d2eb8fc994798fc83d91b691b75b07626a69801", "impliedFormat": 1}, {"version": "de9d2df7663e64e3a91bf495f315a7577e23ba088f2949d5ce9ec96f44fba37d", "impliedFormat": 1}, {"version": "c7af78a2ea7cb1cd009cfb5bdb48cd0b03dad3b54f6da7aab615c2e9e9d570c5", "impliedFormat": 1}, {"version": "1ee45496b5f8bdee6f7abc233355898e5bf9bd51255db65f5ff7ede617ca0027", "impliedFormat": 1}, {"version": "8b8f00491431fe82f060dfe8c7f2180a9fb239f3d851527db909b83230e75882", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "db01d18853469bcb5601b9fc9826931cc84cc1a1944b33cad76fd6f1e3d8c544", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dba114fb6a32b355a9cfc26ca2276834d72fe0e94cd2c3494005547025015369", "impliedFormat": 1}, {"version": "903e299a28282fa7b714586e28409ed73c3b63f5365519776bf78e8cf173db36", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fa6c12a7c0f6b84d512f200690bfc74819e99efae69e4c95c4cd30f6884c526e", "impliedFormat": 1}, {"version": "f1c32f9ce9c497da4dc215c3bc84b722ea02497d35f9134db3bb40a8d918b92b", "impliedFormat": 1}, {"version": "b73c319af2cc3ef8f6421308a250f328836531ea3761823b4cabbd133047aefa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e433b0337b8106909e7953015e8fa3f2d30797cea27141d1c5b135365bb975a6", "impliedFormat": 1}, {"version": "dd3900b24a6a8745efeb7ad27629c0f8a626470ac229c1d73f1fe29d67e44dca", "impliedFormat": 1}, {"version": "ddff7fc6edbdc5163a09e22bf8df7bef75f75369ebd7ecea95ba55c4386e2441", "impliedFormat": 1}, {"version": "106c6025f1d99fd468fd8bf6e5bda724e11e5905a4076c5d29790b6c3745e50c", "impliedFormat": 1}, {"version": "ec29be0737d39268696edcec4f5e97ce26f449fa9b7afc2f0f99a86def34a418", "impliedFormat": 1}, {"version": "aeab39e8e0b1a3b250434c3b2bb8f4d17bbec2a9dbce5f77e8a83569d3d2cbc2", "impliedFormat": 1}, {"version": "ec6cba1c02c675e4dd173251b156792e8d3b0c816af6d6ad93f1a55d674591aa", "impliedFormat": 1}, {"version": "b620391fe8060cf9bedc176a4d01366e6574d7a71e0ac0ab344a4e76576fcbb8", "impliedFormat": 1}, {"version": "d729408dfde75b451530bcae944cf89ee8277e2a9df04d1f62f2abfd8b03c1e1", "impliedFormat": 1}, {"version": "e15d3c84d5077bb4a3adee4c791022967b764dc41cb8fa3cfa44d4379b2c95f5", "impliedFormat": 1}, {"version": "5f58e28cd22e8fc1ac1b3bc6b431869f1e7d0b39e2c21fbf79b9fa5195a85980", "impliedFormat": 1}, {"version": "e1fc1a1045db5aa09366be2b330e4ce391550041fc3e925f60998ca0b647aa97", "impliedFormat": 1}, {"version": "63533978dcda286422670f6e184ac516805a365fb37a086eeff4309e812f1402", "impliedFormat": 1}, {"version": "43ba4f2fa8c698f5c304d21a3ef596741e8e85a810b7c1f9b692653791d8d97a", "impliedFormat": 1}, {"version": "31fb49ef3aa3d76f0beb644984e01eab0ea222372ea9b49bb6533be5722d756c", "impliedFormat": 1}, {"version": "33cd131e1461157e3e06b06916b5176e7a8ec3fce15a5cfe145e56de744e07d2", "impliedFormat": 1}, {"version": "889ef863f90f4917221703781d9723278db4122d75596b01c429f7c363562b86", "impliedFormat": 1}, {"version": "3556cfbab7b43da96d15a442ddbb970e1f2fc97876d055b6555d86d7ac57dae5", "impliedFormat": 1}, {"version": "437751e0352c6e924ddf30e90849f1d9eb00ca78c94d58d6a37202ec84eb8393", "impliedFormat": 1}, {"version": "48e8af7fdb2677a44522fd185d8c87deff4d36ee701ea003c6c780b1407a1397", "impliedFormat": 1}, {"version": "d11308de5a36c7015bb73adb5ad1c1bdaac2baede4cc831a05cf85efa3cc7f2f", "impliedFormat": 1}, {"version": "38e4684c22ed9319beda6765bab332c724103d3a966c2e5e1c5a49cf7007845f", "impliedFormat": 1}, {"version": "f9812cfc220ecf7557183379531fa409acd249b9e5b9a145d0d52b76c20862de", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e650298721abc4f6ae851e60ae93ee8199791ceec4b544c3379862f81f43178c", "impliedFormat": 1}, {"version": "2e4f37ffe8862b14d8e24ae8763daaa8340c0df0b859d9a9733def0eee7562d9", "impliedFormat": 1}, {"version": "13283350547389802aa35d9f2188effaeac805499169a06ef5cd77ce2a0bd63f", "impliedFormat": 1}, {"version": "680793958f6a70a44c8d9ae7d46b7a385361c69ac29dcab3ed761edce1c14ab8", "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "913ddbba170240070bd5921b8f33ea780021bdf42fbdfcd4fcb2691b1884ddde", "impliedFormat": 1}, {"version": "b4e6d416466999ff40d3fe5ceb95f7a8bfb7ac2262580287ac1a8391e5362431", "impliedFormat": 1}, {"version": "5fe23bd829e6be57d41929ac374ee9551ccc3c44cee893167b7b5b77be708014", "impliedFormat": 1}, {"version": "0a626484617019fcfbfc3c1bc1f9e84e2913f1adb73692aa9075817404fb41a1", "impliedFormat": 1}, {"version": "438c7513b1df91dcef49b13cd7a1c4720f91a36e88c1df731661608b7c055f10", "impliedFormat": 1}, {"version": "cf185cc4a9a6d397f416dd28cca95c227b29f0f27b160060a95c0e5e36cda865", "impliedFormat": 1}, {"version": "0086f3e4ad898fd7ca56bb223098acfacf3fa065595182aaf0f6c4a6a95e6fbd", "impliedFormat": 1}, {"version": "efaa078e392f9abda3ee8ade3f3762ab77f9c50b184e6883063a911742a4c96a", "impliedFormat": 1}, {"version": "54a8bb487e1dc04591a280e7a673cdfb272c83f61e28d8a64cf1ac2e63c35c51", "impliedFormat": 1}, {"version": "021a9498000497497fd693dd315325484c58a71b5929e2bbb91f419b04b24cea", "impliedFormat": 1}, {"version": "9385cdc09850950bc9b59cca445a3ceb6fcca32b54e7b626e746912e489e535e", "impliedFormat": 1}, {"version": "2894c56cad581928bb37607810af011764a2f511f575d28c9f4af0f2ef02d1ab", "impliedFormat": 1}, {"version": "0a72186f94215d020cb386f7dca81d7495ab6c17066eb07d0f44a5bf33c1b21a", "impliedFormat": 1}, {"version": "84124384abae2f6f66b7fbfc03862d0c2c0b71b826f7dbf42c8085d31f1d3f95", "impliedFormat": 1}, {"version": "63a8e96f65a22604eae82737e409d1536e69a467bb738bec505f4f97cce9d878", "impliedFormat": 1}, {"version": "3fd78152a7031315478f159c6a5872c712ece6f01212c78ea82aef21cb0726e2", "impliedFormat": 1}, {"version": "b01bd582a6e41457bc56e6f0f9de4cb17f33f5f3843a7cf8210ac9c18472fb0f", "impliedFormat": 1}, {"version": "58b49e5c1def740360b5ae22ae2405cfac295fee74abd88d74ac4ea42502dc03", "impliedFormat": 1}, {"version": "512fc15cca3a35b8dbbf6e23fe9d07e6f87ad03c895acffd3087ce09f352aad0", "impliedFormat": 1}, {"version": "9a0946d15a005832e432ea0cd4da71b57797efb25b755cc07f32274296d62355", "impliedFormat": 1}, {"version": "a52ff6c0a149e9f370372fc3c715d7f2beee1f3bab7980e271a7ab7d313ec677", "impliedFormat": 1}, {"version": "fd933f824347f9edd919618a76cdb6a0c0085c538115d9a287fa0c7f59957ab3", "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "impliedFormat": 1}, {"version": "6a1aa3e55bdc50503956c5cd09ae4cd72e3072692d742816f65c66ca14f4dfdd", "impliedFormat": 1}, {"version": "ab75cfd9c4f93ffd601f7ca1753d6a9d953bbedfbd7a5b3f0436ac8a1de60dfa", "impliedFormat": 1}, {"version": "f95180f03d827525ca4f990f49e17ec67198c316dd000afbe564655141f725cd", "impliedFormat": 1}, {"version": "b73cbf0a72c8800cf8f96a9acfe94f3ad32ca71342a8908b8ae484d61113f647", "impliedFormat": 1}, {"version": "bae6dd176832f6423966647382c0d7ba9e63f8c167522f09a982f086cd4e8b23", "impliedFormat": 1}, {"version": "1364f64d2fb03bbb514edc42224abd576c064f89be6a990136774ecdd881a1da", "impliedFormat": 1}, {"version": "c9958eb32126a3843deedda8c22fb97024aa5d6dd588b90af2d7f2bfac540f23", "impliedFormat": 1}, {"version": "950fb67a59be4c2dbe69a5786292e60a5cb0e8612e0e223537784c731af55db1", "impliedFormat": 1}, {"version": "e927c2c13c4eaf0a7f17e6022eee8519eb29ef42c4c13a31e81a611ab8c95577", "impliedFormat": 1}, {"version": "07ca44e8d8288e69afdec7a31fa408ce6ab90d4f3d620006701d5544646da6aa", "impliedFormat": 1}, {"version": "70246ad95ad8a22bdfe806cb5d383a26c0c6e58e7207ab9c431f1cb175aca657", "impliedFormat": 1}, {"version": "f00f3aa5d64ff46e600648b55a79dcd1333458f7a10da2ed594d9f0a44b76d0b", "impliedFormat": 1}, {"version": "772d8d5eb158b6c92412c03228bd9902ccb1457d7a705b8129814a5d1a6308fc", "impliedFormat": 1}, {"version": "4e4475fba4ed93a72f167b061cd94a2e171b82695c56de9899275e880e06ba41", "impliedFormat": 1}, {"version": "97c5f5d580ab2e4decd0a3135204050f9b97cd7908c5a8fbc041eadede79b2fa", "impliedFormat": 1}, {"version": "c99a3a5f2215d5b9d735aa04cec6e61ed079d8c0263248e298ffe4604d4d0624", "impliedFormat": 1}, {"version": "49b2375c586882c3ac7f57eba86680ff9742a8d8cb2fe25fe54d1b9673690d41", "impliedFormat": 1}, {"version": "802e797bcab5663b2c9f63f51bdf67eff7c41bc64c0fd65e6da3e7941359e2f7", "impliedFormat": 1}, {"version": "847e160d709c74cc714fbe1f99c41d3425b74cd47b1be133df1623cd87014089", "impliedFormat": 1}, {"version": "3ecfccf916fea7c6c34394413b55eb70e817a73e39b4417d6573e523784e3f8e", "impliedFormat": 1}, {"version": "5cdc27fbc5c166fc5c763a30ac21cbac9859dc5ba795d3230db6d4e52a1965bb", "impliedFormat": 1}, {"version": "6459054aabb306821a043e02b89d54da508e3a6966601a41e71c166e4ea1474f", "impliedFormat": 1}, {"version": "f416c9c3eee9d47ff49132c34f96b9180e50485d435d5748f0e8b72521d28d2e", "impliedFormat": 1}, {"version": "05c97cddbaf99978f83d96de2d8af86aded9332592f08ce4a284d72d0952c391", "impliedFormat": 1}, {"version": "14e5cdec6f8ae82dfd0694e64903a0a54abdfe37e1d966de3d4128362acbf35f", "impliedFormat": 1}, {"version": "bbc183d2d69f4b59fd4dd8799ffdf4eb91173d1c4ad71cce91a3811c021bf80c", "impliedFormat": 1}, {"version": "7b6ff760c8a240b40dab6e4419b989f06a5b782f4710d2967e67c695ef3e93c4", "impliedFormat": 1}, {"version": "8dbc4134a4b3623fc476be5f36de35c40f2768e2e3d9ed437e0d5f1c4cd850f6", "impliedFormat": 1}, {"version": "4e06330a84dec7287f7ebdd64978f41a9f70a668d3b5edc69d5d4a50b9b376bb", "impliedFormat": 1}, {"version": "65bfa72967fbe9fc33353e1ac03f0480aa2e2ea346d61ff3ea997dfd850f641a", "impliedFormat": 1}, {"version": "c06f0bb92d1a1a5a6c6e4b5389a5664d96d09c31673296cb7da5fe945d54d786", "impliedFormat": 1}, {"version": "f974e4a06953682a2c15d5bd5114c0284d5abf8bc0fe4da25cb9159427b70072", "impliedFormat": 1}, {"version": "872caaa31423f4345983d643e4649fb30f548e9883a334d6d1c5fff68ede22d4", "impliedFormat": 1}, {"version": "94404c4a878fe291e7578a2a80264c6f18e9f1933fbb57e48f0eb368672e389c", "impliedFormat": 1}, {"version": "5c1b7f03aa88be854bc15810bfd5bd5a1943c5a7620e1c53eddd2a013996343e", "impliedFormat": 1}, {"version": "09dfc64fcd6a2785867f2368419859a6cc5a8d4e73cbe2538f205b1642eb0f51", "impliedFormat": 1}, {"version": "bcf6f0a323653e72199105a9316d91463ad4744c546d1271310818b8cef7c608", "impliedFormat": 1}, {"version": "01aa917531e116485beca44a14970834687b857757159769c16b228eb1e49c5f", "impliedFormat": 1}, {"version": "351475f9c874c62f9b45b1f0dc7e2704e80dfd5f1af83a3a9f841f9dfe5b2912", "impliedFormat": 1}, {"version": "ac457ad39e531b7649e7b40ee5847606eac64e236efd76c5d12db95bf4eacd17", "impliedFormat": 1}, {"version": "187a6fdbdecb972510b7555f3caacb44b58415da8d5825d03a583c4b73fde4cf", "impliedFormat": 1}, {"version": "d4c3250105a612202289b3a266bb7e323db144f6b9414f9dea85c531c098b811", "impliedFormat": 1}, {"version": "95b444b8c311f2084f0fb51c616163f950fb2e35f4eaa07878f313a2d36c98a4", "impliedFormat": 1}, {"version": "741067675daa6d4334a2dc80a4452ca3850e89d5852e330db7cb2b5f867173b1", "impliedFormat": 1}, {"version": "f8acecec1114f11690956e007d920044799aefeb3cece9e7f4b1f8a1d542b2c9", "impliedFormat": 1}, {"version": "178071ccd043967a58c5d1a032db0ddf9bd139e7920766b537d9783e88eb615e", "impliedFormat": 1}, {"version": "3a17f09634c50cce884721f54fd9e7b98e03ac505889c560876291fcf8a09e90", "impliedFormat": 1}, {"version": "32531dfbb0cdc4525296648f53b2b5c39b64282791e2a8c765712e49e6461046", "impliedFormat": 1}, {"version": "0ce1b2237c1c3df49748d61568160d780d7b26693bd9feb3acb0744a152cd86d", "impliedFormat": 1}, {"version": "e489985388e2c71d3542612685b4a7db326922b57ac880f299da7026a4e8a117", "impliedFormat": 1}, {"version": "5cad4158616d7793296dd41e22e1257440910ea8d01c7b75045d4dfb20c5a41a", "impliedFormat": 1}, {"version": "04d3aad777b6af5bd000bfc409907a159fe77e190b9d368da4ba649cdc28d39e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74efc1d6523bd57eb159c18d805db4ead810626bc5bc7002a2c7f483044b2e0f", "impliedFormat": 1}, {"version": "19252079538942a69be1645e153f7dbbc1ef56b4f983c633bf31fe26aeac32cd", "impliedFormat": 1}, {"version": "bc11f3ac00ac060462597add171220aed628c393f2782ac75dd29ff1e0db871c", "impliedFormat": 1}, {"version": "616775f16134fa9d01fc677ad3f76e68c051a056c22ab552c64cc281a9686790", "impliedFormat": 1}, {"version": "65c24a8baa2cca1de069a0ba9fba82a173690f52d7e2d0f1f7542d59d5eb4db0", "impliedFormat": 1}, {"version": "f9fe6af238339a0e5f7563acee3178f51db37f32a2e7c09f85273098cee7ec49", "impliedFormat": 1}, {"version": "3b0b1d352b8d2e47f1c4df4fb0678702aee071155b12ef0185fce9eb4fa4af1e", "impliedFormat": 1}, {"version": "77e71242e71ebf8528c5802993697878f0533db8f2299b4d36aa015bae08a79c", "impliedFormat": 1}, {"version": "a344403e7a7384e0e7093942533d309194ad0a53eca2a3100c0b0ab4d3932773", "impliedFormat": 1}, {"version": "b7fff2d004c5879cae335db8f954eb1d61242d9f2d28515e67902032723caeab", "impliedFormat": 1}, {"version": "5f3dc10ae646f375776b4e028d2bed039a93eebbba105694d8b910feebbe8b9c", "impliedFormat": 1}, {"version": "bb18bf4a61a17b4a6199eb3938ecfa4a59eb7c40843ad4a82b975ab6f7e3d925", "impliedFormat": 1}, {"version": "4545c1a1ceca170d5d83452dd7c4994644c35cf676a671412601689d9a62da35", "impliedFormat": 1}, {"version": "e9b6fc05f536dfddcdc65dbcf04e09391b1c968ab967382e48924f5cb90d88e1", "impliedFormat": 1}, {"version": "a2d648d333cf67b9aeac5d81a1a379d563a8ffa91ddd61c6179f68de724260ff", "impliedFormat": 1}, {"version": "2b664c3cc544d0e35276e1fb2d4989f7d4b4027ffc64da34ec83a6ccf2e5c528", "impliedFormat": 1}, {"version": "a3f41ed1b4f2fc3049394b945a68ae4fdefd49fa1739c32f149d32c0545d67f5", "impliedFormat": 1}, {"version": "3cd8f0464e0939b47bfccbb9bb474a6d87d57210e304029cd8eb59c63a81935d", "impliedFormat": 1}, {"version": "47699512e6d8bebf7be488182427189f999affe3addc1c87c882d36b7f2d0b0e", "impliedFormat": 1}, {"version": "3026abd48e5e312f2328629ede6e0f770d21c3cd32cee705c450e589d015ee09", "impliedFormat": 1}, {"version": "8b140b398a6afbd17cc97c38aea5274b2f7f39b1ae5b62952cfe65bf493e3e75", "impliedFormat": 1}, {"version": "7663d2c19ce5ef8288c790edba3d45af54e58c84f1b37b1249f6d49d962f3d91", "impliedFormat": 1}, {"version": "5cce3b975cdb72b57ae7de745b3c5de5790781ee88bcb41ba142f07c0fa02e97", "impliedFormat": 1}, {"version": "00bd6ebe607246b45296aa2b805bd6a58c859acecda154bfa91f5334d7c175c6", "impliedFormat": 1}, {"version": "ad036a85efcd9e5b4f7dd5c1a7362c8478f9a3b6c3554654ca24a29aa850a9c5", "impliedFormat": 1}, {"version": "fedebeae32c5cdd1a85b4e0504a01996e4a8adf3dfa72876920d3dd6e42978e7", "impliedFormat": 1}, {"version": "0d28b974a7605c4eda20c943b3fa9ae16cb452c1666fc9b8c341b879992c7612", "impliedFormat": 1}, {"version": "cdf21eee8007e339b1b9945abf4a7b44930b1d695cc528459e68a3adc39a622e", "impliedFormat": 1}, {"version": "db036c56f79186da50af66511d37d9fe77fa6793381927292d17f81f787bb195", "impliedFormat": 1}, {"version": "87ac2fb61e629e777f4d161dff534c2023ee15afd9cb3b1589b9b1f014e75c58", "impliedFormat": 1}, {"version": "13c8b4348db91e2f7d694adc17e7438e6776bc506d5c8f5de9ad9989707fa3fe", "impliedFormat": 1}, {"version": "3c1051617aa50b38e9efaabce25e10a5dd9b1f42e372ef0e8a674076a68742ed", "impliedFormat": 1}, {"version": "07a3e20cdcb0f1182f452c0410606711fbea922ca76929a41aacb01104bc0d27", "impliedFormat": 1}, {"version": "1de80059b8078ea5749941c9f863aa970b4735bdbb003be4925c853a8b6b4450", "impliedFormat": 1}, {"version": "1d079c37fa53e3c21ed3fa214a27507bda9991f2a41458705b19ed8c2b61173d", "impliedFormat": 1}, {"version": "4cd4b6b1279e9d744a3825cbd7757bbefe7f0708f3f1069179ad535f19e8ed2c", "impliedFormat": 1}, {"version": "5835a6e0d7cd2738e56b671af0e561e7c1b4fb77751383672f4b009f4e161d70", "impliedFormat": 1}, {"version": "c0eeaaa67c85c3bb6c52b629ebbfd3b2292dc67e8c0ffda2fc6cd2f78dc471e6", "impliedFormat": 1}, {"version": "4b7f74b772140395e7af67c4841be1ab867c11b3b82a51b1aeb692822b76c872", "impliedFormat": 1}, {"version": "27be6622e2922a1b412eb057faa854831b95db9db5035c3f6d4b677b902ab3b7", "impliedFormat": 1}, {"version": "b95a6f019095dd1d48fd04965b50dfd63e5743a6e75478343c46d2582a5132bf", "impliedFormat": 99}, {"version": "c2008605e78208cfa9cd70bd29856b72dda7ad89df5dc895920f8e10bcb9cd0a", "impliedFormat": 99}, {"version": "b97cb5616d2ab82a98ec9ada7b9e9cabb1f5da880ec50ea2b8dc5baa4cbf3c16", "impliedFormat": 99}, {"version": "d23df9ff06ae8bf1dcb7cc933e97ae7da418ac77749fecee758bb43a8d69f840", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "040c71dde2c406f869ad2f41e8d4ce579cc60c8dbe5aa0dd8962ac943b846572", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3586f5ea3cc27083a17bd5c9059ede9421d587286d5a47f4341a4c2d00e4fa91", "impliedFormat": 1}, {"version": "a6df929821e62f4719551f7955b9f42c0cd53c1370aec2dd322e24196a7dfe33", "impliedFormat": 1}, {"version": "b789bf89eb19c777ed1e956dbad0925ca795701552d22e68fd130a032008b9f9", "impliedFormat": 1}, "e462a655754db9df18b4a657454a7b6a88717ffded4e89403b2b3a47c6603fc3", {"version": "33f870d088fa9245195339072faa9cc1de1d2a162311183fee07165a163e9544", "signature": "af13879bf2f57af9259a66ce6499ca1bdb8a54954bbb449d4e309856f2ab7472"}, {"version": "ec2f5819760220cdc4658dc22d5e512a1bb6ec72fa662e55e139ea70a68aef46", "signature": "37e72922a1df47eb3a520036df62fd3054c307b5ce9ba64eca803e3e47cd6d2e"}, {"version": "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "impliedFormat": 99}, {"version": "b57d734a47e4a491e0e698936e012cd9008526e2bd6a46dd7b78b557545b31df", "signature": "eb057c6b76f09c0ae21f3c6f70b0d20f66c6f5bee05a0e57da5635e898827d5b"}, {"version": "c0d6d887021adcd63368403ed96acd5341b4c07f65b60b5de87e3001911ab95d", "signature": "f7570afceffb8d708d6e536bcb6d05166c04d03c5458797cc0b9bb0f667d7c1f"}, {"version": "9971931daaf18158fc38266e838d56eb5d9d1f13360b1181bb4735a05f534c03", "impliedFormat": 99}, {"version": "50cf7a23fc93928995caec8d7956206990f82113beeb6b3242dae8124edc3ca0", "impliedFormat": 99}, {"version": "62443d6fbad6830cc1ec6a96ccb346b9c8fac320d954f7ba14ec84ac489c89e5", "impliedFormat": 99}, {"version": "bd85074aed3cfd83d906643c80cda912732d688b0a20791cd8df5b7ff0eba59e", "impliedFormat": 99}, {"version": "909e8848dd7005329d4841027b51d09efcfb131e14268b091e830ab7adea9849", "impliedFormat": 99}, {"version": "0c5b705d31420477189618154d1b6a9bb62a34fa6055f56ade1a316f6adb6b3a", "impliedFormat": 99}, {"version": "352031ac2e53031b69a09355e09ad7d95361edf32cc827cfe2417d80247a5a50", "impliedFormat": 99}, {"version": "853b8bdb5da8c8e5d31e4d715a8057d8e96059d6774b13545c3616ed216b890c", "impliedFormat": 99}, {"version": "b9bd72693123f4548f67d5ba060cedc22755606d3bd63bb1d719970086799965", "impliedFormat": 99}, {"version": "9bd5be6049c58f5a7a1699c3c8c4db44d634f2a861de445dda907011167317b1", "impliedFormat": 99}, {"version": "1af42015f6353472dd424dcaa8b6909dfe90ce03978e1755e356780ff4ed0eb5", "impliedFormat": 99}, {"version": "c363b57a3dfab561bfe884baacf8568eea085bd5e11ccf0992fac67537717d90", "impliedFormat": 99}, {"version": "1757a53a602a8991886070f7ba4d81258d70e8dca133b256ae6a1a9f08cd73b3", "impliedFormat": 99}, {"version": "084c09a35a9611e1777c02343c11ab8b1be48eb4895bbe6da90222979940b4a6", "impliedFormat": 99}, {"version": "4b3049a2c849f0217ff4def308637931661461c329e4cf36aeb31db34c4c0c64", "impliedFormat": 99}, {"version": "6245aa515481727f994d1cf7adfc71e36b5fc48216a92d7e932274cee3268000", "impliedFormat": 99}, {"version": "d542fb814a8ceb7eb858ecd5a41434274c45a7d511b9d46feb36d83b437b08d5", "impliedFormat": 99}, {"version": "998d9f1da9ec63fca4cc1acb3def64f03d6bd1df2da1519d9249c80cfe8fece6", "impliedFormat": 99}, {"version": "b7d9ca4e3248f643fa86ff11872623fdc8ed2c6009836bec0e38b163b6faed0c", "impliedFormat": 99}, {"version": "ac7a28ab421ea564271e1a9de78d70d68c65fab5cbb6d5c5568afcf50496dd61", "impliedFormat": 99}, {"version": "d4f7a7a5f66b9bc6fbfd53fa08dcf8007ff752064df816da05edfa35abd2c97c", "impliedFormat": 99}, {"version": "1f38ecf63dead74c85180bf18376dc6bc152522ef3aedf7b588cadbbd5877506", "impliedFormat": 99}, {"version": "24af06c15fba5a7447d97bcacbcc46997c3b023e059c040740f1c6d477929142", "impliedFormat": 99}, {"version": "facde2bec0f59cf92f4635ece51b2c3fa2d0a3bbb67458d24af61e7e6b8f003c", "impliedFormat": 99}, {"version": "4669194e4ca5f7c160833bbb198f25681e629418a6326aba08cf0891821bfe8f", "impliedFormat": 99}, {"version": "f919471289119d2e8f71aba81869b01f30f790e8322cf5aa7e7dee8c8dadd00a", "impliedFormat": 99}, {"version": "a95cd11c5c8bc03eab4011f8e339a48f9a87293e90c0bf3e9003d7a6f833f557", "impliedFormat": 99}, {"version": "e9bc0db0144701fab1e98c4d595a293c7c840d209b389144142f0adbc36b5ec2", "impliedFormat": 99}, {"version": "b1f00d7e185339b76f12179fa934088e28a92eb705f512fbe813107f0e2e2eb8", "impliedFormat": 99}, {"version": "b28a60406a3d803788bc1335f4fc5459b06b6cfe3716fcdb59ed75c9e08970c2", "signature": "c5a56f965b885fd7d0032d6bf08b08eeb8dd3d59a5d81d26e3ce572a314acca4"}, {"version": "1a8a2c59ef5d3469dad1fbeb6ad963d4fa28f36541693a804153c856a1710e71", "signature": "f6fe261a1ce22d36e2625f9e710fffc51638fbe42733261d7986653ec053e54a"}, "e41123a9f0110c799cfeaf56109f67ab0cb9ea091b851811ff44d16c70c3fea2", "b094656b421a7c5e8bd8b2a2733c3a22c9a3d868c90664579df97e9899898434", "278d062eaaf2f56ec64a2ace4316d9c96e61a224d0d675e1a0ede568e20c2b26", "d6481a40a66c864ff7cc5abb3bf78c6bd0608011c145f8fb0d944c9871718a0b", "001ce58a981502018a7f313186df9ee2b143012269593d08a005592ee014e155", "c927a1208c521bb012030d0ed1395c39cef2b0cf217495b429d733e64a74bed4", "4c3b8de3c02b1e4b1423d482abd745cbd16f658e05edbe878c5cf74d7bba111c", "a7986a40940685b77fdbe71122487337e14aa758d51b11f1107f37af62b99ce1", {"version": "74dfa565a45712c8ae9624bf3992a6c862bc082186461d739dd2eeb315eb6545", "signature": "eb8256b6300c7defe3be2a4265a4361d6a7d99ffb9d9b87a9d8599d532579637"}, {"version": "e159e4cf31f4913e7102abe674d8d73fa199931668942af29e095f3f532adff7", "signature": "4d80ea8d59190dc24a97a6f273e7453ed0b223b8ab5852fe5c82135590665222"}, {"version": "9abb44d93b3569a2940f5f6286a2b3b78c6991ac172a3d9c39e5ffee082eb956", "signature": "ad7c93b9b36f61e4ce11c9652cc45b014234cc924ee75558b6fd62568efc3df1"}, {"version": "533e17445973ad13a20b08bc8a729abdaa215582dfd9f9c9e41ab658cf3eb29b", "signature": "9834a9af9d112ccfe0d6e67386ae6c69484cef357a5c38ac7523df4d5a69f37f"}, {"version": "ae647970c29c0c2d6e644fd9f812718e0cd40cab36ed78a417000b282f452a66", "signature": "51423cda936c31348248759c66ed3b335864dcbf942f33e9be1b5e1b48e6fa4e"}, {"version": "d04f947114fa00a20ee3c3182bb2863c30869df93293cc673f200defadbd69d9", "impliedFormat": 1}, {"version": "4c629a21fb1b4f2428660f662d5fef6282e359d369f9e5ec5fd6ac197c1906ee", "impliedFormat": 1}, {"version": "785926dee839d0b3f5e479615d5653d77f6a9ef8aa4eea5bbdce2703c860b254", "impliedFormat": 1}, {"version": "66d5c68894bb2975727cd550b53cd6f9d99f7cb77cb0cbecdd4af1c9332b01dd", "impliedFormat": 1}, {"version": "a25e1d0a7361b34c12d07305c1ee1cb79ef14572e92d587464218d25edf29b73", "impliedFormat": 1}, {"version": "d65e4bd246a0d50336d982fcbe426a8fac08baec5ad89b858d38356de69921f7", "signature": "f4782f8e0bb3cf861a1a9bd7c8cbef582e9100fd173130142d9bf4247c9eb844"}, {"version": "00bedb92ef40524a567a7866889e8fae06ea4644a6f09ec6dbb49e08bb6fba4b", "signature": "9ef59e0a1bff6b8c8a1616e42cd07135ee04f8df4985e40b9b66124cfe520053"}, "a6bfe148a25bcd130784f67bd2b6680570d71b20d9f0d484a3314b9f2bc119d6", "53d2626432e63fec72f82cdc15454938c67d9c7c9e59595cd368f39356320bc6", {"version": "4f4588ff57f848b707de2c92335d7ab27395279f484cedcbe3b6251b15b40a31", "signature": "026729fe4a1065fa5a3311ccb51e60341c6e6598ada9d0ef20cb9661727e6a66"}, {"version": "aea5188baf956f8cfd03cc5521fff8944cea9d0091da33fcc5d02eae2aabb5ed", "signature": "9a85ce768aa8aefa0c7c10d18d50cad000750dd98aed2b131e5ceb3115df0c80"}, "f8551d99adb9e927a0cfa570dff1b3230436ebc94d19a77ce8150382fb48dd85", "df5e635648d4466c6b55a1e2cec8464d88a4193b7558dfda1b07ad154b514cbc", {"version": "2772dcdb3600a37d46edb2e8cada6f0353161c0211f449d2a465d664b4e9fefd", "signature": "ba81f62f3d0ed0130cc9a1905f7fb8f5a67a8656c1c3cb4cdefcb16bc4a342a6"}, {"version": "427b51589d90e5449d9bb4870b40d7bffef8321e23ebddc32a9ca9245030a1e4", "impliedFormat": 1}, {"version": "b6020abf893d8e9bcab64923c9c8ce8fe9ae80d11594d0ae490cce5b92588bf2", "signature": "8f48c63f166a350a0dcecc02a47e687465986b1bde4d8459315a81abbf9f5f8d"}, {"version": "e65216c25e9afd47b27edad97d44ba1b19c8c8f06ca46a6e605b1e5b43db2b77", "signature": "6f25f424196aca7f71817f67dd978b3b655742f49f642367cc0ea0bdeb3175eb"}, {"version": "f2457e80a7c8d9aa34667d20b37dba514c79c08bdb69da1c6bdd6899c3c83998", "signature": "d439a07209ce55273cf032f2c4b8f6b56d677979a5f7546b0cb22887bab76817"}, {"version": "8831c2c3b91e686da81a74d07213eed4d32c95522a2e2495eab31e1ebb2bfd73", "signature": "6fe16a195c83458eeea6463eb509b30e805cc942deb0a125798318b99605c609"}, {"version": "416129514bd878ccd797782e2906b5e2634353304ea924496903aa08c78ce222", "signature": "c07a19417c4a582a23d9bce9a696acda7a3e154cd99a0b1769964de7e971f2c6"}, {"version": "f77c8d322dfd625eda9ce0d4cfc94cd20c7b2976f82e08dd81f0359327a4bb04", "signature": "2591e39ffc8eb51ea9797e3bd0ba937534a69f1c4f2be75a366a829100782159"}, {"version": "0324fbf002293ebca97b0eee4081d9cea0f9949d10b166d674d014739eaf138d", "signature": "2abbf64b8e41236a19186167460ec7c9a394e56792fde1d8f0eef9acd6c1b1d1"}, {"version": "207764c6e73aad189cbe0662cfe9823d6532d31a4fb94adefdfe436850654327", "impliedFormat": 99}, {"version": "4bc877e129f6baa0e463eb77e072513708ff8be35f53f58fe3de24eaa6c48ac3", "signature": "183a1d0bf9c2de892eda1b16ee903b53c63273fcf05d1a2ebbb388fb4ac223d7"}, {"version": "0e3838fe752a2cda512b4197b8b201eea875200dfdbcbe4acf127e474bd59b24", "signature": "4ea875226f99072704464a241faee5100109d84b2ffa701e30419525c3780337"}, {"version": "e24b020b452811aa40cfb5ba279d11857612970a06f84d6447664461a0c90750", "signature": "3270d3f6f7cd84cacd8e14f0113b0c949da26b931193a946780bd3b90702aa0d"}, {"version": "0ffff6b1150547a379f5431a856e8dfe5e13b8a63d786167742b66a33dc14c51", "signature": "e5517a8812c22929ce36edb8b2f63843b8d17285549ba1a1f18bf8547abcccb7"}, {"version": "cc0ae141ab3ce2f8a28a68cab312530395ba52f3db85b6d22d806ee3797ab4b5", "signature": "7572c7ac3e62ea3d9bc1b6f28530bffcc189a685f1b9d09b085471060f6a87a3"}, {"version": "e0370c55c4ef6e46a18a133286bfe6c3dbd046a8cb3b0cdfda656f4958173215", "signature": "194bd2008f4c8a9d8209d92f7bc1104bf65639aaf360c37aee62c8447190898d"}, {"version": "b88ce45b0b6a3e665c70e8c318744c70ae6a6d46a6e60f3c15ec31896e033698", "signature": "96958b4ce765bed7c2875d5b4f7e2f53f7a2138f363f931be1d7b1110fcf347b"}, {"version": "cde586a63dc3759cdb62423fc5c20cf9bfd81bf61ccee4375001c7f59752653c", "signature": "8ad0319f7c482c981e080125fed3a930b9a46b2c1eff132546464aa2032a0fb9"}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "impliedFormat": 1}, {"version": "3911de3c66dd5e3730e6181b85b97e7b120818e510378a16f50407a01aa7e768", "signature": "7e068cc625b1f3c58591488d5fc8bbdd4c8728191e73fe17dbf0dc5725fd7065"}, {"version": "53425e0564d0ff69561b1a2ff5d6d5ec798356bc2960a58c6f84be0563e850db", "signature": "8ca9c47ab4a527e080aa7385be820517c289312be0c754a4ad59ae60dda67a68"}, {"version": "a98750036f961e713cc15de1a6ded42672dd00e8e8e758063ac7da1a3a0dbb7d", "signature": "0859faa9750214ab8506124d2f82e1b758942bcaeff17a152eaee1ccdd5f2735"}, {"version": "a31f38d08a399cc055ce589e851329df0768787edacc551c20bc78588bee868d", "signature": "774ad1ba34213f2c9eab918406b2262275146f7320864b83d27dc046e5505d2f"}, {"version": "6b573e0f71387c820231e809475c45f894632e3fdad7309d7cf1d2d98742cbae", "signature": "f000791a6cd6f0a7f3e8f3e183c50a162c880a2998aa11b8a4125e5d429ff3ef"}, {"version": "1d55a3aca36ea51d5d723244848d9eb481fa58fe1dfe501e0eb6204f62725ebe", "signature": "2213146310a08583c3e262c1293763c098695830777c9d8944094fd2168e8f2f"}, "ba3d38667da86a635409952acc1d14dcfef32b55983c604e9e4687f479478160", {"version": "69f5a99e03498a73a26c7624aa80710cfc635a337fd8419138861b9dd8bf5bc3", "signature": "e17744d9efcab8b3f272bff476e5b3582a99402ccc272a0b1d02d27f98a85008"}, {"version": "131fc900fd35225e8ea9311131ea7bda36add78361347f00fa15ffd593d4351e", "signature": "90b62534c77a9a82892dadb999ec4c1a8ae4c7c7fd7fbe75135f6f122ff6e8f0"}, "7e3d79f5d2e5422a430e2928b7bee5062b1062523b91e65783fb4fe76cee4540", {"version": "d320bf32df07bccaada5364b3a63c52504bee612c5773f66ec2d36c9bada41e2", "signature": "ff10b35b4439befdebc5dcfab3f72e9755f4545f156081e4b98fc755837cc334"}, {"version": "424a52fb4619f63af3a88d57e18f30db54ffd43cc19f3e81a50172b94bedaef4", "signature": "1fbacc67dbc89a6673586d4553cd83ebf34233acdaba437b5bd8599390177587"}, {"version": "33fcfa78c27725eca9d339fa876fe267aca47b920d053785ce86b0a61be6c2c2", "signature": "5be82440b98fe7af48c607a23f590ff54ae99a41beec622150ff1c0a739e3187"}, {"version": "c9e92f2b808a5c53383e8afce2f8c7af69763978f4ef87d7572712b95817a01e", "signature": "7828de911ffff769573553d46690a9ef32c0310bfefe83408d5cc2ee7e226d74"}, {"version": "a1e36576a129e3a227d28566cd5447500de714b7e6e490e1b1bfe632b6b114d4", "signature": "97705478dbc02ec5fb81d8e696477b162df50258bb51f32987038ac92854d1dc"}, {"version": "dd01514e54a4060e0f8b65dd208feaf5c804abf8ca39266e248464f6e02119e7", "signature": "f395f4cc0a9d0b9313227d0393bd487c8505165bb71900da1b354540564b88bd"}, {"version": "8fa85c48012386e55c65e96ce6d032863573e728363f28befe173405b5fa54f1", "signature": "84c5cdac3c046a258d081b8d6ea763cc97582cc763345564a1fe370cae06df6d"}, {"version": "185350a358c878bf57b11c5f7a595d0e2c32e4b7cd22da3cfb35ce3c68506d00", "signature": "81c36c36bed930e665108a2c91416789bb228c4dc4f130e3ce8f1240cda7e205"}, {"version": "06a6bbb16b75aab37762cff2d21ac22939e89e248475afd40bafd47f3bf58056", "signature": "fed699e7783a454e922b6cf88185d2c3b07afb2f222bed5f9c79f4690652e166"}, {"version": "f73bdc274a07e4b17235301fe8e8d46248bc839f84b2c461acff9c71cf563975", "signature": "7c753a7e189e7341612206783f7068156f84ccd1f12472efa5b9dcd04fff901f"}, {"version": "f66e2c6f787e827826668c17d0fe64f14d7534f7706268346db34ccca5e212c0", "signature": "4f07cc63561384d30a27d03fa0232ac6b1aa597f4221a8d743be8270b4a9c5d7"}, {"version": "83ed0b04bca8cc787d388728255bd44fe55243ff7849d7be51bca99759d77ec0", "signature": "52a16ef57ad36c3ee3d86199bed0c1575e2aeb00490c6d72637ae92fccb73b9d"}, {"version": "688cfb712013e387143fa701c203d2dca93e0bd878502f8b7b144cd390a21bc4", "signature": "542d56bddb9bfefe83e7157abebe7b6e1ceb6a0fc8b3ab063499fa89bcf3b95f"}, {"version": "7c061e2d07cf311dbbfabb113a77da3a51f9f519518e69f0637661652249ef8e", "signature": "51423cda936c31348248759c66ed3b335864dcbf942f33e9be1b5e1b48e6fa4e"}, {"version": "9123187ec81ca119ec71930d05f7ea35c8fdf1dd052277fb1efdb18f6a1e8369", "signature": "c95212b533b5e587e10f812287bcebf4d53ffb5170a3b01d2e9d28f98809b3e3"}, {"version": "d6628dd44367e754cdfb46e26554fc045a2973594786ac54e51fa4a694c15ad8", "signature": "a68468600c0887c7c4a6975fe1dba2d7a5b70fba52760c9d92ee688daae3e7ca"}, {"version": "e5a59648735e89fc2d4df3a87d854737268c48a1416e1199bdc88afbcf105738", "signature": "d27168da211ab1e79d20c9b4db73396a5d93afa977a12a5f85085f4e7e9130b5"}, {"version": "cc950712a029f8c6874dfdb8052f31df1b7073a504bbd70f4bcc44d663b295c9", "signature": "98e401e717f7e669769dbce7230d795407fe952696223dc60084940864d8860a"}, {"version": "30fc0533589943e6fd87c5c9fa18aef4f9abb28a0dc59ef9d07b41520994eb8d", "signature": "958a11ac8db69a8c01baf3eb008a60d57c77e525512553c7cd9d840ed53629d8"}, {"version": "54e27d36aa26415897f1557a02b1e4395aa03a839e7d24b9eb80966ec6ab5f88", "signature": "183a1d0bf9c2de892eda1b16ee903b53c63273fcf05d1a2ebbb388fb4ac223d7"}, {"version": "7b8719162ebdb0f2d94a08838dba66344120b40f9737718d8e6b57c1802a9f73", "signature": "08fb914281d4c2dc80a3108ab7c2c94afede6806de33a3af88cc5f32ef5917d3"}, {"version": "a66e37fbecb69a24fdbadb602c9adc1add456a753246b83c1e1b8da0dafceb36", "signature": "4ea875226f99072704464a241faee5100109d84b2ffa701e30419525c3780337"}, {"version": "01066e29c88987c3215085c446ade095d059bcc23fb90f9a646039c413452569", "signature": "be08535f05b5992522339ca1284e9a97baf7b645c59954d45400a687718eef92"}, {"version": "6ad2b242d158ee8f7c455ede9378f43d63d4f6bbef8f282e7b9c93f1725fce54", "signature": "3270d3f6f7cd84cacd8e14f0113b0c949da26b931193a946780bd3b90702aa0d"}, {"version": "c0588437f7ce628d19e85da90e01d2e260dec75655e705b1d12a0dd960e33ccd", "signature": "a2570a067fae9aa609a19d7f3b3276760f4e665a8c744db191b92dbff11ed2ce"}, {"version": "98314c52745b16739c2b381063b1f0977cdd40795a9b85ade782533890cd2dd7", "signature": "fe2dab970230d5fa1df8af9a8a364c65d0ccadeeef7afdefe5a32bb3da98e001"}, {"version": "dc57fab39c48d0e0396b49b016b3cd396d6d52ef179baedae6200badbe569652", "signature": "6d297ff369b5fba06a71f8bc3cdbb174baed77d7f2b14ec59a57f293a18a1cc3"}, {"version": "743d3aea9ccf1b733a7beeffe85f7b9581bbb4db5bbc7ccfacb3c4974a604bc4", "signature": "c9c0a3a23c33abef9fa93d697cedd1ef534cc02b77ba9975e636c5283ca0fc22"}, {"version": "c63a0a23b4e1568a8e8e05620741f988361a18b54dbcf85d40824c0fb8b3535a", "signature": "7e068cc625b1f3c58591488d5fc8bbdd4c8728191e73fe17dbf0dc5725fd7065"}, {"version": "c9d27775df3282c482e6b6c3c52cf8d124364ae61662b75c169a3078a93dd412", "signature": "036a79ac7a112f0460b93477b1f7d07cf8216af45f45c405444817c86061168c"}, {"version": "81af677a884ec2e0f1c7f93d2a38d6fab2528b0a087341f85cfc9163315ba768", "signature": "1baf666d8ea0d56433387eb29ffc17f1c3ac5951e452f9ccfeccbd0bd870cce7"}, {"version": "c83ea9c7e00367d32c90fa4e0a5372bf683865f1e3dc44c292e4c5e4a585a578", "signature": "749f48599bfe5506fc4b2d49ed125aaf661fa7f3b607555f266ebd94db4691df"}, {"version": "e94b714135891c502b2801742ec823a4bccb78e8508c3a562f0ba8eaabe6b9e5", "signature": "7501c4f4d2926ce0bf5fa71f9f772b2f1c57cd3f8407cfbe33f3c8a39c5fe1e3"}, {"version": "b0404b9f7c8ece46cb37c9c4ef08345a93320605e34096b5d718f180bef48390", "signature": "ba8617ed83cb51c25bd99c2b8e7a2480b8cedbd66cb7f3af0f7d4d7ec44b882d"}, {"version": "7787cfe45050d8bc55fb0b99b69d8fb4a1c959a1e94b133da64825f8f152af98", "signature": "bda3685e3a85eac6742136741fc3e0cb6d2562a9077e9ca1b180a8caff1114d8"}, {"version": "1a6c6f8bf7862152c377ff237b81e968bc73e3f0dbcb3334be8d4c05eff054a4", "signature": "c5da51cb9aaab2853bb27c6fd0b340a58af09aaaadabb52757f66528dc5dd783"}, {"version": "1d58314b306b11abfcee9588ee47284cb75aa300e94213679232600d37bb522a", "signature": "a7e18b0ec0d330361b80b09ec7e46c8468562bf4b90fbf5145504488bb5e0c6a"}, {"version": "da2f355d7f3d79152ced3a08be79c2b93b4bdac69d02985ce52e17130a0dc8b3", "signature": "8566a31e3ad643546101ffdadedef1043280a637cd9544ce2ce4a41fd7795e05"}, {"version": "df10bbf9b87c68255a9f011acb240d8f930a6d3ffa6cc5cae451402ef015f816", "signature": "d1b017868277a15f0b28618b614773d88835df314ebc030566c26d8135b65521"}, {"version": "d6c5b4f0ec35ce4a668bfb3c0272d837543ad3d412709b86e9468c2dafcd3615", "signature": "0bf343ebc371d927b4d904b46d71dc0bf611cae8d02b74ed1d9a635c38af1c82"}, {"version": "74b19712c5b444a575db973c52e3c5ad7914f4597cf74de994009b32c306a216", "signature": "89424769f09ca9f11453536d8e1d497113bec0041b96607666447f9436d8fd36"}, {"version": "9287dbddbe1f94e44b993e5ad1925e3bcb4658a387624f8bad1ad49887ea8f5f", "signature": "94f088d7aaee1a1b5c7492ec6f472e1d05ac752a087363e1123e3fa3a8f548d4"}, {"version": "d514598475f601371cd91e3b14b11e1edda033f40259b801985866aad1eb7458", "signature": "5c17d767a303be30b8a35a9715d85da024864daf997752e62eb302fc8d56b7f3"}, {"version": "247e71351675281e335a3aaa8a500a48ff4af2eca9cc4faea4e62b31c8a262cc", "signature": "c64d7a5daf23c394b1d03de1df88e7ddd81aaa34ab106ed7b61785a0ef813183"}, {"version": "9377865948aa0283ca84b344438c4a37cf48a370f1fa4a2531054b79a6a622b0", "signature": "2265bb60b3be9e8b4df5832364f1e167d54047f891514b160eb9c0cff6f84cf0"}, {"version": "7b1928082ccd23ec4eef639c122dd227ea700e4906f1d95fe183dcca2cc31205", "signature": "95cd543e28e21993aeb32d207abd95bd78830d9aaedd4633cc88555dcf90fe91"}, {"version": "47fb8d7ec15f4c1e241bef8aae36274a2dc25efa2a54dab47157b6514e640a92", "signature": "152715bef81559ca667a6c692c2b793225d0e27b1b779705a62f91ef9eec8607"}, {"version": "63ecd887000a48fbbe0f3d26af30967509eb810c0a63b533f0bcfde3f15faf26", "signature": "a472e969d11686c4e4c270784927aed150133c9fc5717966962ba7daa323ce4f"}, {"version": "5fb3025290d4a2116ca96ce52bdb7c34b7956f879dbf0fd87472ffb917b5fc76", "signature": "49780db58c6fcdb9ee4d56bebf8276b68f0fa466255ab41d380347cc7f1ea047"}, {"version": "80844a887f736f8899d4a2b14510993bcdede800947b2c2c11ec838ca806e619", "signature": "9a5cfd9f698687446f2db3e3d645039bd89a00c0ea1b140d2234c5bc6a0866e1"}, {"version": "93fa84aa8dd155488a80489ad08baba3410fff27524d15a1d6ab857d6721b795", "signature": "0bda82fe5a80a2e594745798ffb9b4e6f48035f6a8347fc631f2381efaee87d2"}, {"version": "45339cec1694e7d23879064cc33e9922ea50bb1155323f2ab90564b798d05fdb", "signature": "d7c79d4f1e706051211ad3bfeab9438a2eaa159bd7ffa062a0c1baffee2291c1"}, "c6f6b02afebca90f75277a5c345d1e66c13482d6c03db432ba8849352d161747", {"version": "d5fd6e251385060e1c439bca3de30af7bd9156b6c61094f4df045ee4eaa57455", "signature": "6568bc7cfcd55484b5a5d09a34a65db4f8a5ffa0520860177ce6b2fb146e3498"}, {"version": "8b9f245db2fb4280ebd2296947648ffaa1df320e66ba65dbff951fa6585563d7", "signature": "e95106feed432fea0ab80f64bc6a2495c540214b9b3774572a019516a8259e61"}, {"version": "0aaad0d5f4a2a1709ad66f91d81cf90cef9b17c9011467c087531fe2c0216dc6", "signature": "719b4398ca3dea817ef2ffb2ab296bfd1c019b5895bfd48a39ea9acdb29dcdf4"}, {"version": "9702002ab02a1833ce9aee8ac09eb57e2541a9f12f301f26894bb2edcba44706", "signature": "1edda2ae5588a4378a6ccee2c1bf04fb28a33fbea32221ef612336f062a66615"}, {"version": "3c943361a719a654db0dc2bc94782888e809ef41b3691221cf5555a31394e33e", "signature": "94b8d5d372f8a672dcb223f62ac11d482518846c50af24dcd8e5896e441f732a"}, {"version": "f55663f6445e99c4e970e6df6669509dd1b1e30efc6b727ba7dde13efd9a23a7", "signature": "7d07ca3f8ac77d0dff17acc059b95eaffe1c94756e940c8a6a912cb81548789d"}, {"version": "1428c42cae763b0805baa231784eae4e1f8e704e9414fa8f0a46d850d156232b", "signature": "e21547e4ea559910b7fa775263d508ea315dc0a9679770176fa7bbb4d6b70b45"}, {"version": "4896588110cb6fd91132e4e9bca7731ec94c015067a9823baa21b4d728ff2283", "signature": "70eaed5cf78054a666344b5988b2ed0bc3397fe6f7819df716094274d02c4db6"}, {"version": "8985507c5183e2866c72be86fdd4928c6cb3e485359ec854eba0dcb712fcf843", "signature": "3c199c9f0110e458b2dc01e55d5f2f6e98ec1cd42d5b062a62d24e70c522a961"}, "cb972e063c819cb2e661de84a6e90c2584906f2927594420734402d935a03ba5", {"version": "177888dd274294d8797b20eba053b66a60d549470723a8190371dd7db023044e", "signature": "dca713419c98f0b092ea4e26edc3608a3790b66d4d9bed4332d685e5a14f3bc0"}, {"version": "6e5177872432c9fc9d17df8d925c16fbe185c0a81296e9ecef8682be962edcbf", "signature": "be462e126cceca7db9c313ebeca1be2b08855df022fe676fc2b930667715177f"}, {"version": "504e4b91aee182ef5fc6a01bb2a226bfe9dd4fd1621963a47287cd3e0ad6220c", "signature": "bbe2558d393e691752d269da837bacc1a1103af54aee9803ff34178a650bd0fc"}, {"version": "4133257b9abec8e394fa381e24f13841fbbf7ed99d8cba17b996c011355b6b5c", "signature": "ee3d4e3a35414602195445bf9743c7fa0ad630e056cd2f6cf9b1bb8cb6321a0a"}, {"version": "54f6cbec0806be8597fd5ea023d57cbdb321379145a46fe491cee69b094e39ee", "signature": "580b8caf5342d32cf5236e5d8e59bcabefe8b1b5fca90d2b36e9d111c673cadb"}, {"version": "d0d89ebf7a1754b6d520a76359e3373e2ae14610cab0a9e0824761792e40106a", "signature": "33f2b122c0265c4f092ca4ecbafe236a194b8bd477e88f9e9f338b0c02c88433"}, "78cfc53e2e90a5c1676998674674b45d27ab9306f4a94bba03cd40c56b7efef2", "703b0560f838b0ad12a5e6f36ea9050a1035c57886f058a235f655ed7bbd48fe", "a625428b548d478191ab4aca9111bc7e9abc3eb1c17a8c9cc9050c5c33742938", {"version": "a98750036f961e713cc15de1a6ded42672dd00e8e8e758063ac7da1a3a0dbb7d", "signature": "0859faa9750214ab8506124d2f82e1b758942bcaeff17a152eaee1ccdd5f2735"}, "34237bc00ab9f7782745d17e8e0f0462b4b62335d43494d107f47fc705006534", "b0b2267f826c3124d76ca5174992cac3d67f45b6f4e9e064493849ce5675ac0e", {"version": "fcf7ceba697e78f2fb55b6e9d51055fcca550a5cb1cbe4575384939256407967", "signature": "eacb07f24301ceb9348d53c544d0fdf60b9a70035a39df8120baf5b4723b70dd"}, "7e1431b20629554c1b7a60c416a9fec9870404b6cfbe9d05b1aa0b58aa0c5605", {"version": "a83985c655966f5de35cea6e1f05ff587d8e645ca49e7b7d3041ac33337de00a", "signature": "161686facb6995e3c5301c0a8d07db0f72be8ddb77f0772bdab52a3a1a2ba0d4"}, {"version": "4b75eff868c243322ea65d4c88d2e27b4c7e5a9bb76fcf1b0e10d6b6a3bd6890", "signature": "54c8e82faac5716a7817ec1bedc3270069d3ecd7c9545c8c948f1d898cffc317"}, {"version": "9a17ff42b448946496c8e12e4c00efb8e67f784004ccc767a56633d98fabc5da", "signature": "3bef42b5bc054fd183ff02c3dc526f8129bb9fcb8040d88a1b3a2438ded4ca50"}, {"version": "2de2fcc6e7734da4a06972c0eed9db02ef811b6d5ddea43ca6602eb1fe86271e", "signature": "6fe16a195c83458eeea6463eb509b30e805cc942deb0a125798318b99605c609"}, {"version": "c8ffac49f0a6c9381fb376c5ba56323b8b7ea54dfcf38c9bcff8fe5add53928f", "signature": "c6dc2d007b2251b877fda057265511e44d7b4ca02b459d895f3e2bae54d25f26"}, {"version": "fe5638af5fcc9591547b8395b377ee3eb2b113b1fdc40457eb202400df9b49d3", "signature": "2c717b05fbf4ca257ca5c9a429afef9408c0e4d4d55ac840653ecd2f9ede0dd4"}, {"version": "4a1924298b3809bc7803f3c552af487638272cd1abec93516b41e1ee1d278355", "signature": "9532fe4344afd59e6fb0cefb6a9efc47e96e5c3e5fad8b59643e17f8d8b9c65a"}, {"version": "4bc877e129f6baa0e463eb77e072513708ff8be35f53f58fe3de24eaa6c48ac3", "signature": "183a1d0bf9c2de892eda1b16ee903b53c63273fcf05d1a2ebbb388fb4ac223d7"}, "0e3838fe752a2cda512b4197b8b201eea875200dfdbcbe4acf127e474bd59b24", "e24b020b452811aa40cfb5ba279d11857612970a06f84d6447664461a0c90750", "0ffff6b1150547a379f5431a856e8dfe5e13b8a63d786167742b66a33dc14c51", "cc0ae141ab3ce2f8a28a68cab312530395ba52f3db85b6d22d806ee3797ab4b5", "e0370c55c4ef6e46a18a133286bfe6c3dbd046a8cb3b0cdfda656f4958173215", {"version": "979047a1f744f95ce329f33ab2f73613977106d95fd468322b0f43beae2032ab", "signature": "3101cb8f1c5b96e2236fd00d8b3257099c1d15247fd8eff56e9958daf3d1433e"}, "cde586a63dc3759cdb62423fc5c20cf9bfd81bf61ccee4375001c7f59752653c", "3911de3c66dd5e3730e6181b85b97e7b120818e510378a16f50407a01aa7e768", {"version": "f5e69072ff451953c992950791968575bd0a6e2ed63957f76854d4b16836aca5", "signature": "9c46e0a67b195876f230c7fb03087b1a36e7ab19b195d7f17b852f27b9ae859b"}, {"version": "38cc1221f098355896664d98f97310cb8589c29a2d38a1c43fe3a66c0601bdb4", "signature": "3c5c95d72f35c70acb94625e3fa363c97eac4d5b668b1dfc8086410a975ffd23"}, {"version": "a31f38d08a399cc055ce589e851329df0768787edacc551c20bc78588bee868d", "signature": "774ad1ba34213f2c9eab918406b2262275146f7320864b83d27dc046e5505d2f"}, {"version": "8bbc59813419ea2358d3e22ffb92434446c6100789fcc90ffa18de89940dc05c", "signature": "4ba199191e1eb34dabc999a4ad2ffb849eca1eaef9dcf70f1cbfbfb6069c757d"}, {"version": "45b41194711c7d3ca9710cd7020df947bd81b15f182a5edb48272b16be062548", "signature": "cea10e9d84ee195f61aa55d0a5ee4de466cbfcec997da3784d305b04994e5853"}, {"version": "f7d6d1d8548612b460ee5f8fa69a7f37cd52328538150efd213aaaca25991407", "signature": "acb5a77807196a193583043007d8b25a973245c3c974e77955d037d8b94e18e1"}, "d320bf32df07bccaada5364b3a63c52504bee612c5773f66ec2d36c9bada41e2", "6b573e0f71387c820231e809475c45f894632e3fdad7309d7cf1d2d98742cbae", {"version": "2931eeba1c91672d9c313bf5d3fd70ec00718d0b60913fb54dfd01958756b8f0", "signature": "b86f3493065921d2423480087c328e29955aadafe8c1d0a179b13da903c17792"}, {"version": "e26d6ecdae53f1729dd6f5f44679cf4ce7b88cfb17c84108a94e09fbaf13d86f", "signature": "6a2c7e1be1e40a278dd7638cbe556c5e1bce7fc41e8362f7b7992869a88ad0f1"}, {"version": "c5864755ea70d41b413b2f2f12843035dd02ff0c04eba9b8130f7a03a61017ef", "signature": "fb43d01f4220d79bb59948ddcd57efb1c431126f495f321ad86e722e5587baab"}, "4cc576b31de61f3e055cea28b7179708266e0a2db02de85f044e3e1079e1a7e9", "23b50c72535f1f0faaa6c7bd8bc5f8a2724b776776a185a4c656764ea3d64b11", {"version": "3e0e36e7111b117583380e228609fabef9530256872cfd1239c67950745d45d1", "signature": "9dd55b13691dc48da7a417e903eec0c3bc08ef08b223ca699cf30459d08b8f29"}, {"version": "85813831a9adb90e80e608ff3ad14317124ce90b36b15e15eece2f08988fdc14", "signature": "7bc1535c3fe6ceb03565af98db00d5a1a0621c261e9a60c0f90e2931f1efe2c8"}, {"version": "451884305883046f9a19e0e0aae6a1f70894ac3f5a2e2846251f22ef484e2c79", "signature": "532746a88404516eda1bc12621c1eb1184e6d3837c52444367e154684c791140"}, "88b724a2879e5b34a1de747ce81f8d47dd6d49d90497990debd1dbcf2d15162f", {"version": "0b1093383f32e3024ed1b61c7e9b2a0c7cefca06c85fa5c78d2f9f435c68f680", "signature": "74ff77e6c1793ce56fe86bb0378adcefb7f032ea2db4cd52e8c6bc151b686e1b"}, "4d81a818c56fe83a3790852c3449d3733ad0121500a85811e96ec0f0ded26e34", "b0a8ed5a32cf372fe0562fc9832c2065a7593ba5414624ca92db3f1918765b4d", {"version": "59ffb08832988f488b01a472aab7c376fcfa67eae4ff863143d0ac7a9e83f410", "signature": "fc21244c7e234e2a412288a0791d80ba32bf746ad78bf1905c46ef6571e2dbb5"}, {"version": "2620e4f6b7e07287058f36c0b6b9971a34ffb2c41b34112bbe22df1098595696", "signature": "8c9672eeab7e9aa6548e6a97a4cab7709b38421554a9f6f5063f3e0b711a85ed"}, "8831c2c3b91e686da81a74d07213eed4d32c95522a2e2495eab31e1ebb2bfd73", {"version": "d151dc29b52b3daf6635c57882951748425cea3e3758f709d4ba184473462248", "signature": "a5ddb42becaae208f42df3eeb9d4ab9b6f9d9d65330519df09bf34ae81a8952a"}, {"version": "bd556a588a1707d61ea222cd4380b2cbd582630725a2b9d593a54aae8dfc5430", "signature": "e5b959b9349d25bedfe2a3b1872d4aea6b6572665c6da780b701686846e313e2"}, "9205295fdf54dce966928c5e8eefc5f2a5fff6fcd2421868d66f445c95b4d09d", {"version": "4bc877e129f6baa0e463eb77e072513708ff8be35f53f58fe3de24eaa6c48ac3", "signature": "183a1d0bf9c2de892eda1b16ee903b53c63273fcf05d1a2ebbb388fb4ac223d7"}, "0e3838fe752a2cda512b4197b8b201eea875200dfdbcbe4acf127e474bd59b24", "e24b020b452811aa40cfb5ba279d11857612970a06f84d6447664461a0c90750", "0ffff6b1150547a379f5431a856e8dfe5e13b8a63d786167742b66a33dc14c51", "cc0ae141ab3ce2f8a28a68cab312530395ba52f3db85b6d22d806ee3797ab4b5", "e0370c55c4ef6e46a18a133286bfe6c3dbd046a8cb3b0cdfda656f4958173215", {"version": "3b9e592591eb2fea658ab6d60f7bed915206d88cea0deb52b78110f7493ef02a", "signature": "2699c6b06912c031f7ca3ea2da3e9e9b44e2ee16a64814ac5fc9dd54937c1d8d"}, "cde586a63dc3759cdb62423fc5c20cf9bfd81bf61ccee4375001c7f59752653c", "3911de3c66dd5e3730e6181b85b97e7b120818e510378a16f50407a01aa7e768", "53425e0564d0ff69561b1a2ff5d6d5ec798356bc2960a58c6f84be0563e850db", {"version": "f86a02b44f36bc82174b2d101df06b17da892c57a5fd00ac646a66366d0d4482", "signature": "edf74a163becd3eed1b8637b45f7fc8d24015ee0f7783921bff9c07f0de0b416"}, {"version": "a31f38d08a399cc055ce589e851329df0768787edacc551c20bc78588bee868d", "signature": "774ad1ba34213f2c9eab918406b2262275146f7320864b83d27dc046e5505d2f"}, "6b573e0f71387c820231e809475c45f894632e3fdad7309d7cf1d2d98742cbae", {"version": "62030caa7f7ac76eb858106ccaa23681f73ee407750fae3194596bf6d53dbcd4", "signature": "6e4198e2416406f33ed683201c4f835979efdfe79e0b2950945388960c25d795"}, {"version": "ad8c92a3cba61cd6e111c6daa95b66f73815d9da080c1887266bfb0a65d921c0", "signature": "eaeaf360999efea1964fe33fde6d73fe973255a77b0b5c8a86df7c4182c31d14"}, {"version": "750769a72ea517dc3b3d481acdbd3cbdef9f4726f38d691767aacab84c501e8e", "signature": "37d092dacd325945ffbdedc99b9f4335ccb7ce6fa3ac6a3141747a8874dc726e"}, "d320bf32df07bccaada5364b3a63c52504bee612c5773f66ec2d36c9bada41e2", {"version": "c4f0f676c21cca7a8855317225ea211f3832a4781ff0d4a9822717a8ddf34c84", "signature": "c27f7b15bd22bdbab986bf08b5b60b304b8e9057c63ab66320c4923f8cd1633e"}, "12ed148ca874a606394cdc62eba4a81ee6e7be18347899bd01cb609557f2900c", "b3bed20112665cc38d31206b1360155183dedfa455129a123a66eb81724b1c36", "012d90810fe4cde7c5dbd9f5f113fffa219f42a4c1daf9af044f7b9ba8ed0551", "e2ee3b0aa1b9680b23b9dfc678f7d42e40924a31a2acec2fff3a6110b464eef6", "fc9f7abc4d0d71076e0b8ad9fffda4b0cf0035ef645a587e20e6571f37a06019", "3cd94dfb94c737f7dd48d1fa603d730cf10ccbc2d781887930f831a7b6bff8ef", {"version": "d3e99475268d099374b5a7fc0d52200917b2e91d21f4257318a4fdf641c34643", "signature": "5c532560290c6bb81969781ff7e0b332370cab928c547fa5343360e408a6fc58"}, {"version": "28f4e426a9670ac6b3e8ca81d233f32db1f7a0e838423029c72492672d934198", "signature": "9fc3fa567060fcc168393b9b764b9de544024871bd1e60f00a31c67ddcc006b6"}, "daf3cfa9e3b62206b778143f85cc5e5e848b229693bf694b599b57f98bc81a40", "a46f222a1b4018e804a7a81c8dc44dcea313f2638499be2ecf53684758a615bb", {"version": "ae647970c29c0c2d6e644fd9f812718e0cd40cab36ed78a417000b282f452a66", "signature": "51423cda936c31348248759c66ed3b335864dcbf942f33e9be1b5e1b48e6fa4e"}, "d65e4bd246a0d50336d982fcbe426a8fac08baec5ad89b858d38356de69921f7", "2772dcdb3600a37d46edb2e8cada6f0353161c0211f449d2a465d664b4e9fefd", {"version": "f2457e80a7c8d9aa34667d20b37dba514c79c08bdb69da1c6bdd6899c3c83998", "signature": "d439a07209ce55273cf032f2c4b8f6b56d677979a5f7546b0cb22887bab76817"}, {"version": "8831c2c3b91e686da81a74d07213eed4d32c95522a2e2495eab31e1ebb2bfd73", "signature": "6fe16a195c83458eeea6463eb509b30e805cc942deb0a125798318b99605c609"}, {"version": "416129514bd878ccd797782e2906b5e2634353304ea924496903aa08c78ce222", "signature": "c07a19417c4a582a23d9bce9a696acda7a3e154cd99a0b1769964de7e971f2c6"}, {"version": "f77c8d322dfd625eda9ce0d4cfc94cd20c7b2976f82e08dd81f0359327a4bb04", "signature": "2591e39ffc8eb51ea9797e3bd0ba937534a69f1c4f2be75a366a829100782159"}, "0324fbf002293ebca97b0eee4081d9cea0f9949d10b166d674d014739eaf138d", {"version": "4bc877e129f6baa0e463eb77e072513708ff8be35f53f58fe3de24eaa6c48ac3", "signature": "183a1d0bf9c2de892eda1b16ee903b53c63273fcf05d1a2ebbb388fb4ac223d7"}, "0e3838fe752a2cda512b4197b8b201eea875200dfdbcbe4acf127e474bd59b24", {"version": "e24b020b452811aa40cfb5ba279d11857612970a06f84d6447664461a0c90750", "signature": "3270d3f6f7cd84cacd8e14f0113b0c949da26b931193a946780bd3b90702aa0d"}, {"version": "0ffff6b1150547a379f5431a856e8dfe5e13b8a63d786167742b66a33dc14c51", "signature": "e5517a8812c22929ce36edb8b2f63843b8d17285549ba1a1f18bf8547abcccb7"}, {"version": "cc0ae141ab3ce2f8a28a68cab312530395ba52f3db85b6d22d806ee3797ab4b5", "signature": "7572c7ac3e62ea3d9bc1b6f28530bffcc189a685f1b9d09b085471060f6a87a3"}, {"version": "e0370c55c4ef6e46a18a133286bfe6c3dbd046a8cb3b0cdfda656f4958173215", "signature": "194bd2008f4c8a9d8209d92f7bc1104bf65639aaf360c37aee62c8447190898d"}, {"version": "b88ce45b0b6a3e665c70e8c318744c70ae6a6d46a6e60f3c15ec31896e033698", "signature": "96958b4ce765bed7c2875d5b4f7e2f53f7a2138f363f931be1d7b1110fcf347b"}, "cde586a63dc3759cdb62423fc5c20cf9bfd81bf61ccee4375001c7f59752653c", "3911de3c66dd5e3730e6181b85b97e7b120818e510378a16f50407a01aa7e768", "53425e0564d0ff69561b1a2ff5d6d5ec798356bc2960a58c6f84be0563e850db", "a31f38d08a399cc055ce589e851329df0768787edacc551c20bc78588bee868d", "6b573e0f71387c820231e809475c45f894632e3fdad7309d7cf1d2d98742cbae", {"version": "1d55a3aca36ea51d5d723244848d9eb481fa58fe1dfe501e0eb6204f62725ebe", "signature": "2213146310a08583c3e262c1293763c098695830777c9d8944094fd2168e8f2f"}, {"version": "3d3254f26dadf68e996e901821956a968311e0f33721c5080f733d7973a4b455", "signature": "3d6510a424f2a9d9b4fdc9b430475a349e1f3f8305e932eeaa157028a8e61076"}, {"version": "4e34a5d1542d433ac8d26291b11d29dbc8b7ab2b67c1de07134023918c587dfc", "signature": "90b62534c77a9a82892dadb999ec4c1a8ae4c7c7fd7fbe75135f6f122ff6e8f0"}, "d320bf32df07bccaada5364b3a63c52504bee612c5773f66ec2d36c9bada41e2", "424a52fb4619f63af3a88d57e18f30db54ffd43cc19f3e81a50172b94bedaef4", {"version": "33fcfa78c27725eca9d339fa876fe267aca47b920d053785ce86b0a61be6c2c2", "signature": "5be82440b98fe7af48c607a23f590ff54ae99a41beec622150ff1c0a739e3187"}, "c9e92f2b808a5c53383e8afce2f8c7af69763978f4ef87d7572712b95817a01e", "a1e36576a129e3a227d28566cd5447500de714b7e6e490e1b1bfe632b6b114d4", {"version": "185350a358c878bf57b11c5f7a595d0e2c32e4b7cd22da3cfb35ce3c68506d00", "signature": "81c36c36bed930e665108a2c91416789bb228c4dc4f130e3ce8f1240cda7e205"}, "06a6bbb16b75aab37762cff2d21ac22939e89e248475afd40bafd47f3bf58056", {"version": "f66e2c6f787e827826668c17d0fe64f14d7534f7706268346db34ccca5e212c0", "signature": "4f07cc63561384d30a27d03fa0232ac6b1aa597f4221a8d743be8270b4a9c5d7"}, "83ed0b04bca8cc787d388728255bd44fe55243ff7849d7be51bca99759d77ec0", "688cfb712013e387143fa701c203d2dca93e0bd878502f8b7b144cd390a21bc4", "aabd396a87c92096ab214622643a59ac44c57e65ef759a4a4e15dc39fd83d807", "7ffac5d368f45435ed9d4703393a2985b03ae5bf98411ea1bdbfe5ecb0807918", {"version": "79bd03209beea1eeeeb77e643a22894b21628628249e79d67a78c5b658242429", "signature": "96240f508bf527153778fa3a42693fb9dfbfe7539469c3171b52de92049c0bc4"}, {"version": "ce12fcc427f745af1fe5ca6595b486f2043fd32b430a9b4e6677fc1f768256c7", "signature": "be47111273757f05c289a47ec20c7f33ea31f1f76cc0f73bbd5b2deb416765e9"}, {"version": "c19012befc7fa0dca216cd574620b15da1cf4ad2b62957d835ba6ccdbb1a9c27", "impliedFormat": 99}, {"version": "cc0048f62d66e974d5c563bcc0b94476e8a005406ed07ef41e8693316b2e31bd", "impliedFormat": 99}, {"version": "4dcdbdbc992d114e52247e2f960b05cf9d65d3142114bf08552b18938cb3d56b", "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "impliedFormat": 99}, {"version": "ddb5454371b8da3a72ec536ad319f9f4e0a9851ffa961ae174484296a88a70db", "impliedFormat": 1}, {"version": "fb7c8a2d7e2b50ada1e15b223d3bb83690bd34fd764aa0e009918549e440db1d", "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "impliedFormat": 99}, {"version": "9c909c17f69f125976e5c320eded3e693890d21b18cbc4caa246ec4fda260dcd", "impliedFormat": 1}, {"version": "7915d50018073244a9bcb3621e79b8e0ad4eedfb6b053fc945cad60c983bb11b", "impliedFormat": 1}, {"version": "ea7b47bc357858506e6161065b1a8997cfbc5d1dcdf233966da9d01d74721ef8", "impliedFormat": 1}, {"version": "50444daaee4bf4ad85ad8eb52e3ad5c6bba420aad9e2a800043a78f4d8bc436c", "impliedFormat": 99}, {"version": "1fa33d8db2a9d2a7dbfb7a24718cccbcde8364d10cce29b1a7eea4cf3a530cbb", "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "impliedFormat": 99}, {"version": "90300bef1c0e2523c97fdd178b9d50e3f39646ade67faab69be4e445937c862a", "impliedFormat": 1}, {"version": "381437930df37907c030519b23ffea4d8113f46e4431a70bfe008a0c43c63648", "impliedFormat": 1}, {"version": "695cbb89013bc9e87fb24b0df020fe605c54f0ab5c267b5bf0490ed097044197", "impliedFormat": 1}, {"version": "f43780383543bfcdc0a2ee850375e1f03d94bdb1b85091d5b11bb8b2023c8b49", "impliedFormat": 1}, {"version": "303638e9e9378e3cce14c10a276251b2b6baea811f882b0adb6d8b7e44a8245e", "impliedFormat": 1}, {"version": "93fc1a008c4786aa9970b7a4c56295bef4d39c243af63cbfcbd5548ca4fdd535", "impliedFormat": 1}, {"version": "6b91aca1948fd92e4fb32e91e94955e7b7c12fb8cbc0a40eb55f1808886e53e8", "impliedFormat": 1}, {"version": "1e197b6e669b8ece0a68c684af9a4394d8c47e58eaa040391cbdadcc1b5020a0", "impliedFormat": 1}, {"version": "fccfc90c19498513d5c4b9c705706660eba9eb493bc38cdc16a11e9d384cd086", "impliedFormat": 1}, {"version": "b288bbe96ea05e353f008a4d445fb8589a82f2a1c4d4d0bdfc283a19020dc96f", "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "impliedFormat": 99}, {"version": "cf32b34fb9148d541c100a83fd3d204ced00c76871b4811d53b710ff15a948a1", "impliedFormat": 1}, {"version": "6940a178bd12acca76e270f0b0c4d907b9cc469d28833bd59a3276f11668591e", "impliedFormat": 1}, {"version": "b189e328b0f8cfffbaa9b705d5b1d7ff21a58d2910614d449ae052bd6f6977f1", "impliedFormat": 1}, {"version": "ea7b47bc357858506e6161065b1a8997cfbc5d1dcdf233966da9d01d74721ef8", "impliedFormat": 1}, {"version": "9dc9c7a268e5b2caa79a5a5040a86ba5ddf1cba20d8715ceaf2b76f79ee444fc", "impliedFormat": 99}, {"version": "84920f743c6fe02da67c1aeab9bd4e2d377ad96197e9960cb0e7738b8584ad0c", "impliedFormat": 99}, {"version": "c048b081418f530417dd4193b47890bc734711378df819f0ff217144f6775afa", "impliedFormat": 99}, {"version": "e6332e193ef43377d724d8f6efa5e2b36b5ea70389cad57e8a5176e8035ceac8", "impliedFormat": 99}, {"version": "bf0b1e6c1bb4930244203a593c6db7aed456e635c31aba73ee2102c55998861f", "impliedFormat": 99}, {"version": "5c21ec7196196aa797c5bcaa3bbd55f80091b4f793438947e9802376b3538927", "impliedFormat": 99}, {"version": "1f653a61528e5e86b4f6e754134fee266e67a1a63b951baccc4a7f138321e7e6", "impliedFormat": 99}, {"version": "76e3666a9f4495c6d15035095a9bb678a4c3e20014dc8eb9c8df8dc091ec8981", "impliedFormat": 99}, {"version": "055bc641ca1f1eed76df9bc84ec55aaff34e65d364fea6ae7f274ba301726768", "impliedFormat": 99}, {"version": "22ebe7ce1ddc8ee5e70f28c41930c63401e178c637d628b9af9f7a9c456e86b0", "impliedFormat": 99}, {"version": "041c4afbee0a17614e9d4a8aa4385ffbbbfa1a5d5148c9aab0dce964be1af0d6", "impliedFormat": 99}, {"version": "00d259e465df20202e848bf8d192056919e460a3de20aa14f59d523d3af38b29", "impliedFormat": 99}, {"version": "9cbb746b8d46880874f6a8f8c64dfa925ec0cf70412d4ad5e00a8756c82edf3c", "impliedFormat": 99}, {"version": "fd23901347e68e39f7043fc6787b2af6c7094d6c7ef6038ee909cfe26da625c1", "impliedFormat": 99}, {"version": "818a39ff71deaab13a1aa427802c76d3976c365302ddd862810da9e428c8ebb1", "impliedFormat": 99}, {"version": "ef3a6a6b54ff97244df620aa06d7df4d5474d0274617e265e041246c1b7d05c9", "impliedFormat": 99}, {"version": "881c9f22c8d6ffc25b57cc4cf60cc27576d979a8d54ce85dd740d83b0571a088", "impliedFormat": 99}, {"version": "3be840cd66eea7fddebcbc83265943f7f0029a8bff513919fb78450400054dba", "impliedFormat": 99}, {"version": "4904ff0e4bda91f1b7e50a3738c91f393345de5f7e5d0fea9da581e42ec92fb3", "impliedFormat": 99}, {"version": "5f6442d0a9bbb961b58f45d09690a034734aeea01f2875cb0e7ec31aa3676ef7", "impliedFormat": 99}, {"version": "6511839e63105744b3bb8b340791218b253bdae80c7d57c288dcc85bc6f91317", "impliedFormat": 99}, {"version": "14890b158c9bf9f4f6ccb8c8c071881439aea4301bbf5988fecd23f220e8156e", "impliedFormat": 99}, {"version": "3f01edcdc9641acfb6689126d9506248d3a3afe3e4a23e2f7588988ba693f349", "impliedFormat": 99}, {"version": "a12f75a9a3aefb304abb528b2898c085356d4876e77ccd2dd1c708bd660041cd", "impliedFormat": 99}, {"version": "6ac1b4401d51471ae0d6b6bcce637e550eb78d75b1cfe993b6eaca9898d74976", "impliedFormat": 99}, {"version": "aaba5744f8794b7cebab915aa45ca71d322bb2086d7c7aec6e858c313bf6cc69", "impliedFormat": 99}, {"version": "894395299a4761cd4e38c20bf17bfce27a3cbdc2650054e5fc28e692fddc4b4c", "impliedFormat": 99}, {"version": "7568f6aaaf6b62b7f3f72ebd07bbabd95749a0f969dfb15e7789d4a3c8e080a1", "impliedFormat": 99}, {"version": "039d7ce09e9246c255c7acc1c00ba3afe7e98b4767547ccb6b77274109f8a5c1", "impliedFormat": 99}, {"version": "b4b9514c90add4b59499251f760f01aa7fdaacb02894ff0d885286094cef8c2a", "impliedFormat": 99}, {"version": "f670e23ac2377ed32187f39d02be707c9c0cd61e95786a6ba49ea7f860baa50d", "impliedFormat": 99}, {"version": "25f27d8da6c42f1622b0b01fc5c78f48c79c645e10c4849fc8c5521faa9ace29", "impliedFormat": 99}, {"version": "54e17510b0440980e3bc8ce141c9b922adb6c8e77ee81c443870bf684679255a", "impliedFormat": 99}, {"version": "3e9e2f295358fa46f10faa524be6e99a42114752b0e195ae997f550968ea481f", "impliedFormat": 99}, {"version": "74cf1308a1f0de094f0e8567541b0a0e126426ec2eb4ef68c9cd97fa4d0d9272", "impliedFormat": 99}, {"version": "dcd1e783bde43c7d570ce309cc21e9d9d7b3110491aef9c5c5ce87c6a53f7e5d", "impliedFormat": 99}, {"version": "08bc14542d8d34fd138945413e31ecf65668e029f966b5aab5b25e8e421efead", "impliedFormat": 99}, {"version": "17648a898be56a6a9c4a6305e84ba220bc76d4355f0f55696726f1eb1fcd6d4d", "impliedFormat": 99}, {"version": "cc6c1ade000cc9b7f8c79d8bdddb145950bbe7d404e5b3b938537a0bbfba73bd", "impliedFormat": 99}, {"version": "eb97def43c2617552f76eb367e7f5531127fa03fdf991ef12cf5ae8fcc52c7ed", "impliedFormat": 99}, {"version": "f49bde1443de7aaf05371f049ee0710619bde1b7bb7042192512e5cab672b3fc", "impliedFormat": 99}, {"version": "a704c8b701194cc47d333b093f87db332694b124e304fb0167be09ff3304d353", "impliedFormat": 99}, {"version": "358f8d33b436d21a7c313f02e900b805eb1c6abda3d675f703ada38eea3b92d5", "impliedFormat": 99}, {"version": "dbcf8b1a2d94e9a1f0fa3fd5152114a14f83d8dba8d3f8dd773be476adac937f", "impliedFormat": 99}, {"version": "ee63e60be6f56e08cf8d7b5ab750078fc6d08f69cdf70ee43fd0693d10c65d2f", "impliedFormat": 99}, {"version": "4807b8b139747bd82ef181b5eaf8676c1f9012be0ad91feb1173bd57f08aaac8", "impliedFormat": 99}, {"version": "ceee442c1035bd941c9fbddbab08fce2e34d1e23d79d56a48c0444bb45d705b7", "impliedFormat": 99}, {"version": "fb9bcb4ee14feca03c05eaff9f1eb826bb1e75bade5e64f98c65ecc79b910949", "impliedFormat": 99}, {"version": "f8ee6c9ecf3a39cb551db7d6f0aea157cd272ac477c561331efd734a13b34134", "impliedFormat": 99}, {"version": "f72af7f1a38a5b8ae564be5eb68a8c25e5cf9cf4c567ddfa471a481425369c79", "impliedFormat": 99}, {"version": "aef37af42cec810a643f24ba90f2f7d55c3e05ec5e31adca4c3318e578822aa6", "impliedFormat": 99}, {"version": "ce35f35a8d59172dbf5cd945c253512114d6020e7dd30d399d372e473eff2515", "impliedFormat": 99}, {"version": "e9e8a6bbb3819df983667e1bbf9c993e954c009f575c1f5d2063d55c1af47d1a", "impliedFormat": 99}, {"version": "fc1eda40a6dc0e283ac8d75cec0082f6cc49c517ae608d2413e872ef2f5c2e84", "impliedFormat": 99}, {"version": "bf0b1e6c1bb4930244203a593c6db7aed456e635c31aba73ee2102c55998861f", "impliedFormat": 99}, {"version": "44993fcc19de9502ac3f58734809acbe0b7af3f5cca12761dc33d9a77cf02d1b", "impliedFormat": 99}, {"version": "d172b164580892e56129985557aaf73b4e45279e4e0774e1df53282e6fd89427", "impliedFormat": 99}, {"version": "1e1e240fa12ec7975ee7c9803e2e3751399820b4435f476ecfe22656809916f9", "impliedFormat": 99}, {"version": "68f1a4ec2937052ae0dd18407eb8d1b579708970ced79c6e7cfe4a93d0a00385", "impliedFormat": 99}, {"version": "efe0fabfc89403ce6a4a8b1fe3a7633f1161b7e10d9824299560f2d15e4e606e", "impliedFormat": 99}, {"version": "64c4a5d1bb65e93416fb1ca1d08210dcce25d6d8d1208039a58e4379a647bd76", "impliedFormat": 99}, {"version": "e84f2065c605965fd1d44de2cddf0509dce060b4d9e79c01a884a0899fe877db", "impliedFormat": 99}, {"version": "b0df9d1b07f9ffc72ac128e5a05da99af0e3a8a19a08d8defc26678c0e30c25c", "impliedFormat": 99}, {"version": "16725a633f5f5c1cd82e2baf4b0ae521da7f6055339f837bf2695bc3fd44373f", "impliedFormat": 99}, {"version": "664104ab990ca5d100a69e159f9f8874551d94a187db834309af14fee2d64f4e", "impliedFormat": 99}, {"version": "542e50c2dca6d24f5cb9cb2b7a5c07d450850af21ef253838bb2bbfb175a3e8c", "impliedFormat": 99}, {"version": "6ee3000708f3add1fe74964fd6ea6b1f5abf82151481babb96f7905a763ad5d8", "impliedFormat": 99}, {"version": "93640558bd78d5f98d7bf455d07e79f700efbe2f9826958d4b2acdcafbb5ba89", "impliedFormat": 99}, {"version": "fd8b58b771380655281dca6ed40019cd8ecd639ef6ec74baa91662ca0e0ae458", "impliedFormat": 99}, {"version": "6a73dc1806928e57c21fc51d00f40e4e92f17dc6b31ddfa95365a837651587c0", "impliedFormat": 99}, {"version": "ce35f35a8d59172dbf5cd945c253512114d6020e7dd30d399d372e473eff2515", "impliedFormat": 99}, {"version": "97912ca64fedc028914d9f1585e30d98a1e1e46a426a06f2190024067b8a534f", "impliedFormat": 99}, {"version": "a9b65aa46a4613eef2bef431366d8f5f166e8226c6fae3688c67ca102c3d6a79", "impliedFormat": 99}, {"version": "5fbfad634244c213e44e6b3e8e7936ccfb74bf163750dfbd1464140d8230497e", "impliedFormat": 99}, {"version": "0caecd57de90295669dd561bf9f0e4c4478434e14e0741c2b0fbed44e38563eb", "impliedFormat": 99}, {"version": "bb125cb4f8a3155a5dec027913e615c6b7f1000f0c600de19798ac4f0c8a6c5b", "impliedFormat": 99}, {"version": "78c0f55d5519d39233daf5562c5704a0322dd7abcc1e72afb015cac550be32d3", "impliedFormat": 99}, {"version": "95f1e94151a3a45c139a9efb748888d1af359521f6c96e7e644e070913fafc31", "impliedFormat": 99}, {"version": "f72af7f1a38a5b8ae564be5eb68a8c25e5cf9cf4c567ddfa471a481425369c79", "impliedFormat": 99}, {"version": "205d330174cc427f3002517bae08e2cf8b8e134cfe086cc80fe18a07efeca799", "impliedFormat": 99}, {"version": "93d7cf0d29aa72f51299e10d738149a77bb92d42473d3145428cdfedcaf8efa3", "impliedFormat": 99}, {"version": "03535e283a156874e32846037dc86e32c53995db4e077d392a8b17c6f26e4f8d", "impliedFormat": 99}, {"version": "d8f104b12bb1e0ee5690c50f3d6100f71c24145687190a5f2d5ba7b52538d57e", "impliedFormat": 99}, {"version": "aff2d01dbf009d2dc7c5aa71d32930d4783463a08527775e834e2e37bbed5b4a", "impliedFormat": 99}, {"version": "c63356e770e4fa3fd4d6cff5e804e557fafaef2bad6f5b81291d15b1ff21da8e", "impliedFormat": 99}, {"version": "47457637fa208f3d77e4b03a8f117a418a8ead3486995dbe0d9f915e967c9070", "impliedFormat": 99}, {"version": "87621a249f7a938e9d270b70e560b78b55552eafd08ddf71d2fbd80913699488", "impliedFormat": 99}, {"version": "8c40fdc32e3fab434b704c3bd731a12d479a061fdc72f42f665f4b0c287ad7e4", "impliedFormat": 99}, {"version": "400402da2b06f5acd7940db2ee5507784fdab53354062fcddfe4934f3ac04340", "impliedFormat": 99}, {"version": "3e80aeb2dad64ce73bb62a404e1db152fd73bd5849b1777d444939d0c1cfc287", "impliedFormat": 99}, {"version": "61f825380b5ff41a275f6d0cedd145a073524cc24b4963f82c4348574325768c", "impliedFormat": 99}, {"version": "d457f5d460966fee473f543e400f8e0784ca9875ce6aecd48b7ff0f6351a04d1", "impliedFormat": 99}, {"version": "b41d3caa8c0839223be817bfedea85bfcf1e682182d51414fd11d9ccaf83792f", "impliedFormat": 99}, {"version": "2b5637680ce53987f0335180e79a9dd639ccfa8f20d46332195dcf11c02e9bb7", "impliedFormat": 99}, {"version": "08bee5ad21bf8bf6d1e66f9bcbcf1c790c1873ae5d63068c02567c357ae619fc", "impliedFormat": 99}, {"version": "2e76803b80712451178add529e574c5b6acfa0ef4ff169dc5f8a4dfabb43704a", "impliedFormat": 99}, {"version": "931c8729cf2295582ad36e56947aa4253a554135800a5ae3c719e2937061319f", "impliedFormat": 99}, {"version": "949ccc4add0506d70be23ded8fe17702ce7ecad3f6b9b2948d12be7b7621c008", "impliedFormat": 99}, {"version": "8b5aa4aceca84ffb115eaa92eb511db532a380715fbe40e0f2691399f59779c4", "impliedFormat": 99}, {"version": "fa161dc810c98f507b7c8fe8d1cc978ef6cecfd05a91a0897b272ff3d424f53e", "impliedFormat": 99}, {"version": "04498bab7aa04819b6f85e0a833cac9a90d2c225449e62a500e0d969a980a0f5", "impliedFormat": 99}, {"version": "6378847b2becc1fd081eaae8ada8632a1e82a6fb68223b4b4b6db1f6b3783709", "impliedFormat": 99}, {"version": "953be5c29962c02b750c81742c6c8e3ec88f0dca93b490ae0c25d06ec09a336b", "impliedFormat": 99}, {"version": "93c47ea71b8ac6043e85e16a7f5a12fdf28283e0c3e64818b24ef77339dde953", "impliedFormat": 99}, {"version": "d0ebe2f759e4811f5157b9a1e1920458dbc5d4566fce7af6c6a777abcc31d7d0", "impliedFormat": 99}, {"version": "0a5c9fcea7d8dfde5b22c26763cf7c8822a99ba7774b87d4faa63fe165f371d3", "impliedFormat": 99}, {"version": "79e012a9efce1afb73f1d04c643326f3a90ecad76274b8b099711300f475c561", "impliedFormat": 99}, {"version": "cd80c1f39858c9aaf24cb6cf109d90b16470b4c4af5b712b350e6e18b08c1d7e", "impliedFormat": 99}, {"version": "d31e7c5b91a9310f9ace7e2c19e72ba501236af707639fe184d592b6f3aa612d", "impliedFormat": 99}, {"version": "ef0a3e581b336ec4522badc01575daa324a63e76b7317ceda2ef887a5168e2e2", "impliedFormat": 99}, {"version": "5a3458dfcbd3d376e91a57ff64ae747c34f8ca1b503b1be1a84f490b56da1638", "impliedFormat": 99}, {"version": "684fed66904651fd676b78ec044da251651f4dfaedb163df74b2280013d5cd5f", "impliedFormat": 99}, {"version": "78156ec80b86cc8f8651968051ed8f9eb4b2f02559500365ee12c689c2febd9e", "impliedFormat": 99}, {"version": "0383ff8743bc48551085aa9b40fa96327e857764fc0b8e4657b06db1b0068f79", "impliedFormat": 99}, {"version": "da84ac2614990bb98cc8921995af5c6e99cdea1eae3d92692ef6d4a152e9df68", "impliedFormat": 99}, {"version": "df9ca548acc13813971b2a578514bfb3383fffc0f3d88cc2b49150accf4cf090", "impliedFormat": 99}, {"version": "e463bccc0c9e8e19113e8f5684fa1e0d357fd66cbc7a495a3c4854442268ab0b", "impliedFormat": 99}, {"version": "01104176c1be6e4db2f152e17202e2752e01dd7dce8bf1fbfcbc85a54acd25f0", "impliedFormat": 99}, {"version": "2e415d3626693f39e40f19ad427f6ad173dc4bde2a7c4ef6a655f30d052b61b0", "impliedFormat": 99}, {"version": "496b4dd6da860c392c036aab07f706f623902707e0af1cef271eb9a6a827aa44", "impliedFormat": 99}, {"version": "c98069496e78eba403f51c1a7d582ae0e0f338e2d63b6417e561c9f56cbe88c6", "impliedFormat": 99}, {"version": "89e6832e87186cf2f1924ccbbdf510db4ed9d45271b332a1cb1ed659eaa0c874", "impliedFormat": 99}, {"version": "4b0e0173e248db6eab5b9402044f2f1a2d086e99d9d8af6c4a7f46f52cb6d787", "impliedFormat": 99}, {"version": "8d56ae9f7cac9011b44edb4905ad58cb57d12199ca56fd23a16c5714b15d368b", "impliedFormat": 99}, {"version": "a39d68209be7cdeb86ea872366f7c9f3578e657dde3eb1489012833c87028ff3", "impliedFormat": 99}, {"version": "8fc83926d2b5737ff691660774a9ab5829b5fb77d9a382eb97bb2786b8b2a661", "impliedFormat": 99}, {"version": "c5e59270f3237a2bf344ac83ab3095f30c0ad8f3f07e41f266e662ce544520c5", "impliedFormat": 99}, {"version": "63d8897302acaf122123a675c9e4875a1fc7d82bbc62a54949d595119b1ad049", "impliedFormat": 99}, {"version": "1bfb743c928bfe9fbf9ce88bdfaf8235edb1d3ea0b5ab446603d71c4ac87d802", "impliedFormat": 99}, {"version": "b6e92e897f1bd0dab01bb0f64bd70956f574c7752f7bbdc7f107460a074b707d", "impliedFormat": 99}, {"version": "6841d50aae775f444751e244f756085d8fcf34f94ff6647aafe8041b63fc89fe", "impliedFormat": 99}, {"version": "a3c33f57bb6ce04191478ea23a17293d382cddb7aee7b56bb5aed3ca49c7fa60", "impliedFormat": 99}, {"version": "c9bfc8572556f746686beb2ac476f999356253c4b3fcba189327b25b30c47801", "impliedFormat": 99}, {"version": "2d0bedabb6ca97235d746f5e1dd974c4975e8833985f6efb82a995afa06fea38", "impliedFormat": 99}, {"version": "6af214e64dbf7c599257f7f0851cb57b267c6eef97dbca04b1f2d204ac571fdb", "impliedFormat": 99}, {"version": "58617876087d1660ff295d2d76c325e50a42e5fd9bb7dfd9d02963ef80c8fced", "impliedFormat": 99}, {"version": "ac84c9b0786abb646dfce8480f6ebf83370a47a45d8bd7e2bc705f1069bc71b5", "impliedFormat": 99}, {"version": "d0fa8bcd9d99495de67ccbc3124de850e514f3eea0dc0c40f927ea8511bf8e8b", "impliedFormat": 99}, {"version": "504d56c1b6bbbe20409104ad2388b9f70d0e5f7091846e39674987c0b05af7fc", "impliedFormat": 99}, {"version": "98c33da6fd946601b36415c760e677c1faed100c361fee8c45565d8d6a00aca1", "impliedFormat": 99}, {"version": "afabd37daf4bc1b2604caedd796ec9deb277d7f3f1927ecea80cc9eeda678518", "impliedFormat": 99}, {"version": "1cd9c44575b349148a044fb300d2dade101e663dc7556b7c0b9aa4494dc88de7", "impliedFormat": 99}, {"version": "c59eee5e50312900effee1403fa07d9386e95dfaf20411a854729acdf6787629", "impliedFormat": 99}, {"version": "8c8b35b1251978c2156c04db23ce6b842f48db71d39b42dd3c537dfa099e5ef9", "impliedFormat": 99}, {"version": "0001579790ad5940cb4f59fbdf96b540a867b3d2c36624426aaa4fbcea1a4a1f", "impliedFormat": 99}, {"version": "9b571fa31a14b8e1e8e7412743e6000be66b7d350358938c1e42bcd18701c31f", "impliedFormat": 99}, {"version": "9a14a6f51a079956ce0a7ee0826c7898825dea24be60e10802e18b46f142efc3", "impliedFormat": 99}, {"version": "f2f1772f08149a999525bb78ffa3d504a851162d8dfbc7e9b8039baf42eb20bd", "impliedFormat": 99}, {"version": "f0410c617e9f6d332d7b860a1c3a679f7fa3e00e89699dfbc6b4f563b12b350c", "impliedFormat": 99}, {"version": "ace1cb8ad5d6a8cec49a1d4c26757bea48fb6612e0f6ca99581253b5893eaae2", "impliedFormat": 99}, {"version": "8cb9b25afdb7e0662b488c04b05ab598c5e52fd8a605628788080a163b583923", "impliedFormat": 99}, {"version": "b6b726231178cb2695b8a83519d4fa50a03e800fa9b2dd75193a56bf6cb58a08", "impliedFormat": 99}, {"version": "70a29119482d358ab4f28d28ee2dcd05d6cbf8e678068855d016e10a9256ec12", "impliedFormat": 1}, {"version": "869ac759ae8f304536d609082732cb025a08dcc38237fe619caf3fcdd41dde6f", "impliedFormat": 1}, {"version": "0ea900fe6565f9133e06bce92e3e9a4b5a69234e83d40b7df2e1752b8d2b5002", "impliedFormat": 1}, {"version": "e5408f95ca9ac5997c0fea772d68b1bf390e16c2a8cad62858553409f2b12412", "impliedFormat": 1}, {"version": "3c1332a48695617fc5c8a1aead8f09758c2e73018bd139882283fb5a5b8536a6", "impliedFormat": 1}, {"version": "9260b03453970e98ce9b1ad851275acd9c7d213c26c7d86bae096e8e9db4e62b", "impliedFormat": 1}, {"version": "083838d2f5fea0c28f02ce67087101f43bd6e8697c51fd48029261653095080c", "impliedFormat": 1}, {"version": "969132719f0f5822e669f6da7bd58ea0eb47f7899c1db854f8f06379f753b365", "impliedFormat": 1}, {"version": "94ca5d43ff6f9dc8b1812b0770b761392e6eac1948d99d2da443dc63c32b2ec1", "impliedFormat": 1}, {"version": "2cbc88cf54c50e74ee5642c12217e6fd5415e1b35232d5666d53418bae210b3b", "impliedFormat": 1}, {"version": "ccb226557417c606f8b1bba85d178f4bcea3f8ae67b0e86292709a634a1d389d", "impliedFormat": 1}, {"version": "5ea98f44cc9de1fe05d037afe4813f3dcd3a8c5de43bdd7db24624a364fad8e6", "impliedFormat": 1}, {"version": "5260a62a7d326565c7b42293ed427e4186b9d43d6f160f50e134a18385970d02", "impliedFormat": 1}, {"version": "0b3fc2d2d41ad187962c43cb38117d0aee0d3d515c8a6750aaea467da76b42aa", "impliedFormat": 1}, {"version": "ed219f328224100dad91505388453a8c24a97367d1bc13dcec82c72ab13012b7", "impliedFormat": 1}, {"version": "6847b17c96eb44634daa112849db0c9ade344fe23e6ced190b7eeb862beca9f4", "impliedFormat": 1}, {"version": "d479a5128f27f63b58d57a61e062bd68fa43b684271449a73a4d3e3666a599a7", "impliedFormat": 1}, {"version": "6f308b141358ac799edc3e83e887441852205dc1348310d30b62c69438b93ca0", "impliedFormat": 1}, {"version": "64f588374cff45a495d9da0722e88fa7c4a77b7024ea17750a7c947fb8f08e98", "impliedFormat": 99}, {"version": "5ca32089fa4a40b1369f085635aadc4bf853bc4ea4dd49eac0779bf9f62423a3", "impliedFormat": 99}, {"version": "5a46f69508e086a0f63d8fb15717422e9ea54d1813be3798c2220bbd9c8ef43c", "impliedFormat": 99}, {"version": "21e29420bf5da1147cf6ebcd8cd85afa21dc3cbf04aee331a042ae6f94c1fa63", "impliedFormat": 99}, {"version": "71e67299f77ff5da289ee428bb85157485f4a1d335c1b311288262ca04736b85", "impliedFormat": 99}, {"version": "5df08c4af12b3ec3b3e6afeadd08eaaadcdc2825f50335de914b505ee3252964", "impliedFormat": 99}, {"version": "9bab9e8d65ff83bceec753685598d1d522ca1735a2983eb8c881dc8389b6c008", "impliedFormat": 99}, {"version": "0356b906e53157425c8beb4e5673c71fa80d88e1cd32759d4bd57e59698ef88f", "impliedFormat": 99}, {"version": "e72c8e9bc1e2c9a55f6755f85150c3f63d63c3e13fa047656404402b22ae249e", "impliedFormat": 99}, {"version": "edca1f05d978d3c2feae191a82e34710dd8fedb83a24c2fab15373be5be8a378", "impliedFormat": 99}, {"version": "36ac04ebfefc210ab3c0148cbfc451f3434e9ca7048b19827a98247875923176", "impliedFormat": 99}, {"version": "4e152e1b7f2d588e6279ed5ee1815770a12e32913f06a9191f0f3cd60b01aaac", "impliedFormat": 99}, {"version": "d44ad42a40c4e84bcccc9a5db198f86afa6196d42e152cedbe09d513bff01fb5", "impliedFormat": 99}, {"version": "4f20bc9c75b4515c25c3de1cc6c5391972991a25136b796f8c6601a809e80796", "impliedFormat": 99}, {"version": "c9652370233cf3285567f8d84c6c1f59c6b5aa85104b2f2f3ade43ff01f058d2", "impliedFormat": 99}, {"version": "2670ba717e7b90210f244401d5fe6f729cf879cb2938b6536c9c118371ef24a2", "impliedFormat": 99}, {"version": "2e86a352fce1cf1df7be54b242d65c5efa3d66a445a60b2a0f7c33a60ed76eeb", "impliedFormat": 99}, {"version": "9b3abc22bb11e450c1c77674d11719e4eeebf980315470587cfd461d1d407606", "impliedFormat": 99}, {"version": "02e6668da999217b040e0d8d6e41daa96d7f59eda7bd9dc9156378584116b296", "impliedFormat": 99}, {"version": "7c52a6d05a6e68269e63bc63fad6e869368a141ad23a20e2350c831dc499c5f2", "impliedFormat": 1}, {"version": "556261268d31864a619459b9bfece0058e468456ff0ce569fbea916e6b543910", "impliedFormat": 99}, {"version": "827508bd5aee3a424eb2e91965c5ef78e2ec95585b4074399179b70d8f66524c", "impliedFormat": 99}, {"version": "97bc3fd65336434e6330e0a9141807cbde8ba4045989809632f70ba93f70f6d3", "impliedFormat": 99}, {"version": "d5bcc410b5ab12693f89a3c477f8dba724d881d87498adfa8ed292869b393c7e", "impliedFormat": 99}, {"version": "eedc9017d949f60aecbefa1c093f6d70bdb1dea65f5c50ceaf1e1fb30be978f4", "impliedFormat": 99}, {"version": "9f313a2d30d03a9954269fa7c7f5cca86ffe2ae6c1ea14741c3e2794aa805806", "impliedFormat": 99}, {"version": "2c4945c48f529153672e10dc7b67f414ac7e7678bfcd5d6b79842ae28a330002", "impliedFormat": 99}, {"version": "24ec3cb8a40752890fde2a1d694c43bbb0fe1eb0d1e61793373564be5d4c6585", "impliedFormat": 99}, {"version": "ef83f22620073b4b9e666191044faad4f2b3a5b4bb87e8487b8200bcc75102df", "impliedFormat": 99}, {"version": "99cec35e19fac2229b5c6ba317476fd2694f15a0e9a9d38f146c5f5edfe3ada3", "impliedFormat": 99}, {"version": "57678f3f59c73a29d71ade5be0f1ec6c5f737aef206ad61905ca5cde0c7d7445", "impliedFormat": 99}, {"version": "98ab624c4bb847ffac693acecf770154c9763eeb7228e28b873aa2d2ec9eacc4", "impliedFormat": 99}, {"version": "6d26c9ddd47ab86552f4d06e7bf051661237856cc0e5cf75d634853bbd562166", "impliedFormat": 99}, {"version": "136769a51b1415d74b8c03b74e9bf38e629177447065b897694072606bb26f92", "impliedFormat": 99}, {"version": "0a202409812f7dd20d61ded10a6984b79882fe264c76364dc53dca951a28c737", "impliedFormat": 99}, {"version": "06d5971c8b4a3bc00bf57f4332d3bfd92636dd4abda4fa0357c7c1dd496b1407", "impliedFormat": 99}, {"version": "ee67a800e8ec7418a1aac731c3e54759ece60a5aaa4c61a3daaaffea3360dd76", "impliedFormat": 99}, {"version": "719f559f65d32823f1db11af17b4ee08fbb19d5acd4b6feb7b6610ccc83af179", "impliedFormat": 99}, {"version": "432d66aa77c1e6059106ae63b5609793c1aeadc644282bf39d552afc83ee2ac6", "impliedFormat": 99}, {"version": "4c36226ba094c4b73a1ac45ca38815698eb2089101fc707de511bbe51dc0e6e5", "impliedFormat": 99}, {"version": "458a584e7898e910be8bb52341daf8466ed1d363a967f240bc082e549cfcbb69", "impliedFormat": 99}, {"version": "218daa4b2d1f8f6d3c4f022acce45b10b65d04086a1ab74ea7a135814521627d", "impliedFormat": 99}, {"version": "7f7b3faa89da29e2f52f73f7f2dd37b40c7d1e6dd8b820be1f9603bbd37080a0", "impliedFormat": 99}, {"version": "30d4591edcd78009f16185869f1a832b6ff00b42927d16892ede106f7b03081a", "impliedFormat": 99}, {"version": "6c80a54d4b2be32868d3dee7c69cbba3243d7150da9e0f3820a86f988047c9da", "impliedFormat": 99}, {"version": "8a50a838343a8ee7318f5a4a33defa84d325cb035ff67d4cef3f04cc3dbd7c72", "impliedFormat": 99}, {"version": "93f0399b89384f652cb73f597865e287b69db239dbb52c044a6844cb44a45b1b", "impliedFormat": 99}, {"version": "8ce4ebea4cd4077327faecb7a29805b4e6080bc6b9bac23df6adc601b0a12a18", "impliedFormat": 99}, {"version": "9553bb2ddc97cadf255d6056236f335fb3d0b34cd3ff34ef7dc170d0004d8f05", "impliedFormat": 99}, {"version": "522651983601a3d0a24eb8104086714d8e9a958810503275e45cd6ff263cf416", "impliedFormat": 99}, {"version": "a8f9fed7dba6d9a5c6ed93b7c8e02c892c184c8153639a6ab3ce30ffe30c43c2", "impliedFormat": 99}, {"version": "ddec04cd05ab7614a2d51c3fbafa772b47cec4d7d6be80c1de8d37e4366692d1", "impliedFormat": 99}, {"version": "a28d089808860737ef08c33c36db5e3db57ec5c5fd41acdbeb0f0d1d8f7a1519", "impliedFormat": 99}, {"version": "c921f5db48373afab4577ce6dbd5dcff50c41a0f34aaf4529808affc733f75a2", "impliedFormat": 99}, {"version": "51b1dce48fa5bde70b49e5586d0bf7ba3371e172df994fd6401bba8b436fb852", "impliedFormat": 99}, {"version": "09a2cc054e9070ff418f718c410e0065a56447a91e4770d619b58142b7ca7800", "impliedFormat": 99}, {"version": "f54905bfbb9932598ef1baa355769ea8e96e3783e4736b0d31723a520eba38fd", "impliedFormat": 99}, {"version": "aec5756720255bd7045409db869db09031ce31003dc654175f552d59b196313f", "impliedFormat": 99}, {"version": "86892d5bcae518db21850a892aa682878f77bc6ff1fe096f5f706c91e547cde3", "impliedFormat": 99}, {"version": "6852847a05178fce73d3c8b6388e0b5cb23bac202845c426387762b9fcf8970e", "impliedFormat": 99}, {"version": "d22c80b0d938d2a571dbe1707606222fb97bd1d4bbb46fe42e326bdee6545ca3", "impliedFormat": 99}, {"version": "4053a0866f10634083ba91f2166420b1c29a2509b64803bd192f50baeb221265", "impliedFormat": 99}, {"version": "74941adf0115a098f810cc363996a95da17e6847267bc29c9d519bf8b0838b98", "impliedFormat": 99}, {"version": "8b5762f3138b2894db972d51cb539f1ff2bf6b231129667cb89962d4711f9c70", "impliedFormat": 99}, {"version": "ffa366f1f2b7ccf00d170f120836a57cc74e8548e3e72b41bd0cee00dab9dd2a", "impliedFormat": 99}, {"version": "b445ac5a35ce7b38f5d806a88ee4d6b3d1a3a5638243c5a246727af90a9925f9", "impliedFormat": 99}, {"version": "aa94cdb0dbaac5ab520157f991bdcdc953c2fbb0436cb4ef6252bba926429a34", "impliedFormat": 99}, {"version": "063fcb0a3805a0ccccc409d58eb166d7642bed8f35ea56af67e520d3fede1101", "impliedFormat": 99}, {"version": "664ea2d1a61cbe738cf3a4cbe619f775868a97d06398cfe2867173356786988a", "impliedFormat": 99}, {"version": "408f9b4fac8c35efc9da748f2b221efbd565a26d3b45c7b7e3899bd6be5c257a", "impliedFormat": 99}, {"version": "24fa0edbfe31c7c0e96f168d9e7338f9fa0e1015550300e3c47079cedc18528d", "impliedFormat": 99}, {"version": "060bc6464f23a8cfe35ff7b91a3ca4ad918b4f760a96e666453ea093b412a336", "impliedFormat": 99}, {"version": "057a6bc4d8d4ebc4817ad261915f898cf589b62194058913ed9eb4c25f14544f", "impliedFormat": 99}, {"version": "a458726e9fbf25d67d7eb9dbba3909f2654a475f162a97227e592b79b1e6cf68", "impliedFormat": 99}, {"version": "90eb37365f7f73460de47970a44dbf4760990badf21b3223e8ce0207ed874903", "impliedFormat": 99}, {"version": "3127a03a881f78c9145d7db821295531e8c577a8a0738847e70af2b6ad9778f3", "impliedFormat": 99}, {"version": "cefe8670acf41bb5cc2726613785261a6b912c729b0423ed5daadd48a268e7d8", "impliedFormat": 99}, {"version": "1a35bd51a28387166ff9069b79c5b1b45d917efc33381368083a645c78aa5006", "impliedFormat": 99}, {"version": "17e18b0edde7e814a13e0208d2db3f5a6fbe189671b57caef288e39f1f1b9602", "impliedFormat": 99}, {"version": "57afd9ed037a00dd2715e6128c9f305f287c9b29d9c7f556e4daa074d35a90e5", "impliedFormat": 99}, {"version": "221c6bb2c1152e37f7254d5a167f11ffd57f12c734e970ea15cdc59a97f2038e", "impliedFormat": 99}, {"version": "4220b6bb9febf019e09d875d52fe611225de4c5574412a4c1a62c324e4a82401", "impliedFormat": 99}, {"version": "5b6c6c22a039478fa3bc034d6d91d10c0e4d20af1829d986b78a85232cbe0d2f", "impliedFormat": 99}, {"version": "ac67258368872db1e2d5a8fd53fa649fe31c5abe6f62786fd4bc6e6ad51ccb9d", "impliedFormat": 99}, {"version": "a2568a7262a7c222ffdbe3b9296fe725a3aa6037d3792815af50923bb669b7fe", "impliedFormat": 99}, {"version": "1397759591619d547cbcaea8d94cca1ed29e9f6f13beffaffe9f9307e5955861", "impliedFormat": 99}, {"version": "77381f3914dde6135f903652e311c5bb8053dae28607f519a3716ead90429f85", "impliedFormat": 99}, {"version": "761bfb2da76dd72beaa61c09770aa2d4e90fd2a8c8e38f79203cde259d4ed4c6", "impliedFormat": 99}, {"version": "788ec71568d441083686e3c32d5238de15aab63b59481f9b91174d8b4fb71100", "impliedFormat": 99}, {"version": "d77ee71e3052258d3b9afcc8e921ca84f96d460bab31ac752e6237454c5d5cc3", "impliedFormat": 99}, {"version": "6d9b1602e3d14e16b782dec30666f2e42d287d6a5345fb7ae52111f9a1e1f92d", "impliedFormat": 99}, {"version": "e537ea67b8894b0ebb941bce267e16f9eb0719ab8ff37f0653d12f200339f2ea", "impliedFormat": 99}, {"version": "07c9867e04c1628c47fde22389e075d615795c6b7c66ea90af6c281810699d0a", "impliedFormat": 99}, {"version": "f5349612ec61213715349174adb060d1361fa1713a3d8d23dd1630dacd942b11", "impliedFormat": 99}, {"version": "ed7fc0cc7db9eee422f099e3a14c7a624afa3fcfab25d6b39da9315cfb262b6a", "impliedFormat": 99}, {"version": "23abf55ba0b7a59b9bfd17491675b818fc178c581686840a7aef27e45205383c", "impliedFormat": 99}, {"version": "06d3015b06f1f22899905d74207c52e54c051f0466975156de9067ceb884ee47", "impliedFormat": 99}, {"version": "21714b0d8f7fdd7be1e233d4eb2daa87d2f4ee3e41a363362276fefcc2bd45aa", "impliedFormat": 99}, {"version": "3ecd423076cd6107967e1b9187f38919490d790b258df54e8a6572a93ded5f96", "impliedFormat": 99}, {"version": "015edc4dd049b299c563701125cd50d16d9605e9927824f8371a428993c25def", "impliedFormat": 99}, {"version": "f84ebeaa3d5b14a9fb6b8349330e371f706f48317b1524e3968ca13c8eab2ff6", "impliedFormat": 99}, {"version": "242258092f0ed6960f328b9d7a455c6559c7253c6b57b08883a2fb859c4cfdbb", "impliedFormat": 99}, {"version": "d3002aa3f7fcaf5921ebf891a2556ff5a95885d20f0f169b12f0428e4bf80bb1", "impliedFormat": 99}, {"version": "848ac64950a137510b1f47d87cb0f1fe15c7eb06c8e1c2823ae63f413430653c", "impliedFormat": 99}, {"version": "cbd768cb4e86fa0057ca6db0359749dde395eacf2eb9dafc86b903ff1477d213", "impliedFormat": 99}, {"version": "158ac44ea9ca9ecb8fd4036e5eb874be58eee082be92b73ef6f4dc9be3654715", "impliedFormat": 99}, {"version": "31f800e9c3607ff0e370bd5a2b73501567dfcf03b7c7c9c9e8927c10a0467efd", "impliedFormat": 99}, {"version": "75624353ffcf91bb2b7911b44075d19a7b9283670f2a78938c17e82e50d1c0f3", "impliedFormat": 99}, {"version": "c43841a8e135fc3a96ae46e5403a46a3ed686ba983f4f0ef142b1f776269147c", "impliedFormat": 99}, {"version": "f54bb4e54d36037ae537835edc7d64caff0e33b34fac0a2c3e035a418258ab62", "impliedFormat": 99}, {"version": "725e63c5518a0ca69dc44c12dc4cde29218e4bfd8088368ec67836f394cfc7a4", "impliedFormat": 99}, {"version": "a0231312762c8f9446ccb79c88227acdd9d2ee4f8cb3a459eda57029562470e5", "impliedFormat": 99}, {"version": "a6c16d7e6060828143259e5ce1ad0228e3a34e2ff2cf35d2300adc78b6fcb130", "impliedFormat": 99}, {"version": "de9ff289e55588add27a015cc023023660d6b8a21da1a64baa237d0f448b2e96", "impliedFormat": 99}, {"version": "43b90372f7b73615b1eca5549101e50835b885b44e862525f12ca22a55456a8b", "impliedFormat": 99}, {"version": "2f7d6f80dd8dd07edff2652926a4b8eeaedafb51775bea7c889afbc795d40b4f", "impliedFormat": 99}, {"version": "1a84b7fc795e6812ce4d63d7066dfd5292bfd2ccf52364b1fed7f599efa896d2", "impliedFormat": 99}, {"version": "9526eb9c30eb925dce99c5debe53d8478267f498fda60faf00b89cd129fcd7dd", "impliedFormat": 99}, {"version": "0528549bceed39a3d94c2bbefde7eab0778460dae5eef4ff71f04fcb8c8ec6f0", "impliedFormat": 99}, {"version": "17d424fb44cd45655049d153d11a71cb236155abb50d605e1d91c3736799004b", "impliedFormat": 99}, {"version": "96ebc724425e9aae600472cd4af10a11b0573a82cecd6c53581bcd235c869b37", "impliedFormat": 99}, {"version": "03ceff4db920e1831332a5a40c2eaf8056f221b9e3e672bc294ebc89537c9ff8", "impliedFormat": 99}, {"version": "ad030e8f3bae5badcd0e18837a3b637bf411c06ba3aa38c9b89bc6e016c67a35", "impliedFormat": 99}, {"version": "e7f31cf8377bd6a1779922371bd84d2427a6df910b3333a93f0c5168299cdece", "impliedFormat": 99}, {"version": "377862d812238033feb16a3174f3eca5449b5786727572fc546cb6f1e973adef", "impliedFormat": 99}, {"version": "e362bee8c7c56dad6c0f52b2d83316ed53c6aca843ccc4c1a88b7e55382e0b52", "impliedFormat": 99}, {"version": "2784077307c50f1342422e95f1a67f5cb9870ea04ad1a80ed4d99e9cec829980", "impliedFormat": 99}, {"version": "eb7e19c5a59896a08776f58b63212ebf6b4c52c24cb6f0574c8ad2e462fc1277", "impliedFormat": 99}, {"version": "c5676e6ff4ed5b0069a3dea05479e3a5abd938dedd4f5ca813f744728066fae8", "impliedFormat": 99}, {"version": "3b30055d700e379329817ad8469e061cfffb79dd0b6e66cdc3cabc5fe03da7d3", "impliedFormat": 99}, {"version": "7944d3987fda085b3b5a9325ec52f998d0172d4138fcdcbbff60e34b562656cc", "impliedFormat": 99}, {"version": "b944764dcffb404b05669dede7b7008e62b21a8f7c0cc1c021294490a99e555f", "impliedFormat": 99}, {"version": "e887a7a29bd7525556302dd1dae062cbc66ceced3565609b59920fe166910086", "impliedFormat": 99}, {"version": "503a8ac885749cc70864c0dfff99302888a41964e4a9fcaf83ab8d01eef3e458", "impliedFormat": 99}, {"version": "015b9884efeea4f3ffbf092e1c1d6eb69ade71d7d79833468e9c18e36545e142", "impliedFormat": 99}, {"version": "8637312eb67001e93cee29113dfcab695b3e12332a5f4d2fba22471d01978b3d", "impliedFormat": 99}, {"version": "8dfeb90bd8f28f690c724ee3c00d2d32ad633884e159fcfb5ce4e82ee5589c5c", "impliedFormat": 99}, {"version": "f21c7e7ba380996bc52cfbd4e23f037edc90b073fc4b34395c4f8167752da7f2", "impliedFormat": 99}, {"version": "f5df5c1a71732a42fdf23542b344d7069a4e0a68adbec151982b93571442b068", "impliedFormat": 99}, {"version": "b532dd989593d814d9bfcb3131b4331de4b35ade064427001676d1fff001ddd9", "impliedFormat": 99}, {"version": "49ebb1610637e76da9500d2def8f15c96c77b1bdc3560091d5d07ceb86c6be70", "impliedFormat": 99}, {"version": "3dad5f9d2442b6a1ee26187724f0a1ebdf9f89b5dff0fb3b8ba1eea11db6d7ba", "impliedFormat": 99}, {"version": "5fca4b593907fc70848e8590d14dba0cf0410e6c061e39c177835e700ad089bf", "impliedFormat": 99}, {"version": "aa76dec64917d5cb480593cd443b229f9ac8c3a983b88962bbc5afd89d0963ef", "impliedFormat": 99}, {"version": "4876014affafb8fe03898c335c396ec29ff29ec8ae3b50ad5ea5ff98c9323c8d", "impliedFormat": 99}, {"version": "255cfcfd791b6f0dfd44f17f8bf6d4dfd733b4a8fec6c15efed8013d794016c2", "impliedFormat": 99}, {"version": "420139e540c3461ff3a03158ba1a1d52e956aaf083c1a4b04069a8482e8978be", "impliedFormat": 99}, {"version": "d15d43b6b19a969858befe90f60009952298120dcaab7110cff78a388a50f7a0", "impliedFormat": 99}, {"version": "0cade822c5888722f9398f9e29781cfccb603d8844cb0273fd4ac8aa9a184193", "impliedFormat": 99}, {"version": "37b5ab7dcd9f3954013a12e1e873953d8be801cc3f97b4e5d9c4dc895d8fc4ac", "impliedFormat": 99}, {"version": "1277bf682a6d071861d20d2df102d950dedc15e49a96f211b1a4d2c87c83a912", "impliedFormat": 99}, {"version": "8cfe0fafb887fb38150159834ac34b3e91d883b250ba4e1154ce88ed057d9fe2", "impliedFormat": 99}, {"version": "ec69be923cb78bb128ea6fbf86555974d0f172a1f65b866d9bbbbc8e4dab82e5", "impliedFormat": 99}, {"version": "da5d2ad94cbe6ead090c5dabeb266eb81a958354e487442dfe8313beb467f99c", "impliedFormat": 99}, {"version": "1656706a594b924adfc45a7e9088c63caafb5c2ba689fce0d757d1ee5f016b17", "impliedFormat": 99}, {"version": "d274837eed0e7d29bfd55aaeb65147107ff57060c70cc977ec83868830fffe51", "impliedFormat": 99}, {"version": "a050ee6f9c5833d18643f86c0618ffe791cc15e7dd758f21738e305749e9b002", "impliedFormat": 99}, {"version": "baa0b19d4b1f69101d22cf17b011d4544343df50572a2ff7a56fa51a1182c299", "impliedFormat": 99}, {"version": "15e6e5a7d194e6a1d4852f2582c0b0f174e805c445cbd758fc9d2279374d5ae5", "impliedFormat": 99}, {"version": "bcaf57053cdd116527f18f99ed70085db39bed9a251510fcd6903e99df6910d2", "impliedFormat": 99}, {"version": "522ff1756b55a8c06ccc949b09b4cafe6fe922fbb1e2d780dc04e992673f6375", "impliedFormat": 99}, {"version": "6c583ae286739f214987efbbc2bc3222870c03a83b8af01fbb4e951c78a19cd6", "impliedFormat": 99}, {"version": "04ea39e4b3e1d6e56bc1f0bd0c7b19aeb4d35b678937b3ad54c63d44b44900c9", "impliedFormat": 99}, {"version": "7a54a284c5fb690b97ce715f0e7d861c3b150765751cb6bffd6c479c8d5b0313", "impliedFormat": 99}, {"version": "65ad93db7608fa525e362be30971ab55076ddae12db11d04a8e3ea4633ba7738", "impliedFormat": 99}, {"version": "d7fbd0ea7793a151d792f6ad7d7c9a9ab7dbc69d970d0d0e57b408cba59ab91c", "impliedFormat": 99}, {"version": "c59df2ff58c6adc907ed95ae1e0ddc2f6a123ca1189926dbafa3fae1fe8f40b5", "impliedFormat": 99}, {"version": "3e85dc80eee865fee0b9aed7bbe2707c38e2b36b0f9192f9202566a9be7c404e", "impliedFormat": 99}, {"version": "717c55229509a89e25c3c3a83a1de364e4db51be5002a738800f76f0ac168868", "impliedFormat": 99}, {"version": "c00bdc82363a765e8720a159a973486e03ec0c25da4d715e02afebd134bd622e", "impliedFormat": 99}, {"version": "e7165093ba33bad2ca7ee2865de7a0e7ca3b0480101c0cb75be7b024167d9e59", "impliedFormat": 99}, {"version": "ec4ec119f797f71ee6d8110930dad93c689a1683484171621a2702b873d8af1f", "impliedFormat": 99}, {"version": "1390e4de40d868b8e1d2619f6d0e95d0524b7ccdbf9a90c660e0b7230bd5ed19", "impliedFormat": 99}, {"version": "707a37c179d6ff79844ffe41d72350c775de3fe1a1e2ce2ff458cda9595cc75e", "impliedFormat": 99}, {"version": "09c6639e5622dc1693276f4c7684b0f0f4992d5c4e5c0769dd576e95c50635f7", "impliedFormat": 99}, {"version": "0af521e519e48440bd69f5683fd26542d478c8110c1bde2815a732ea790d5448", "impliedFormat": 99}, {"version": "af40e667287d9d2e79aec9af683744075a87c85424f518a70230af7aa8825844", "impliedFormat": 99}, {"version": "49062a955da1d4880135873f5c08988c920429c3785349ed1b4e112b9269d8f7", "impliedFormat": 99}, {"version": "334bc494ebf7f62684a30a916455dc63c6895784a74b07b835d28d0297785496", "impliedFormat": 99}, {"version": "de20f1cce0ab86efc45d9d7bdc100999fec7f369613d57cd8d44cdaec8e12958", "impliedFormat": 99}, {"version": "907467198cc07e6eac62f7eb2bcc7afc31e3ee433ae60000eca62213de971e6d", "impliedFormat": 99}, {"version": "4263e62ba6e779cd26752ab3fcfb42249d009efcf110bf7a69412c1f33582e22", "impliedFormat": 99}, {"version": "0afb4e75b4e9dfb1e331b026346fa429c72b3f76c2838ce448b5281b8d89eb9f", "impliedFormat": 99}, {"version": "a723cf11acbb7f1d9b620b90a5cdc50f60f9ac8c2ec7bb6f69751729093180b6", "impliedFormat": 99}, {"version": "019bfea6e0ea6051fe1d51f3d0671fccd704731d54ab218d9a8a42afcde54a41", "impliedFormat": 99}, {"version": "63646b3d3e6071e59c2ae0a3012529910593f6f55b0285c028798b700df1eaad", "impliedFormat": 99}, {"version": "3f854a9e492f56ef132efbc1bdc155896b97618a2c15eb06248bd88478303be2", "impliedFormat": 99}, {"version": "984d0fd8112e3cdde9bc9cf0875f69676cd5a150caabb228cf067741e1241add", "impliedFormat": 99}, {"version": "8235beb430cdab1e2c5244364de7f28ac109b3fac5e3b6def3bc9aa0fb7d1360", "impliedFormat": 99}, {"version": "6b95bc34efdbe1082609ab0a1522f30f4b79a906e479af1295d4aba7fa887f58", "impliedFormat": 99}, {"version": "c81e7a416c0e77487b511c0f345797d6323214968009b52dc8c2aa5c9faf7210", "impliedFormat": 99}, {"version": "f1f7004e9aadb6803b238c03a27971c5e1effdaf1c5d6dd9b3d688767f5563b2", "impliedFormat": 99}, {"version": "0d8ab497f53d6142282bacf32f1538fc607e267e058074286528126fd1c2db6c", "impliedFormat": 99}, {"version": "5b81a34a60401dac6213a45e2bbde3e57060ff06f847cb005337816ff2015189", "impliedFormat": 99}, {"version": "4b64c32b6dfd99fff8c7805de34e90dd20891dcbbb8e8fc406a3222f5c5bf346", "impliedFormat": 99}, {"version": "8ae43e29b6a1b72cec9bd415afd180de9a9d83423c7d7c8f4d61e090f85ad572", "impliedFormat": 99}, {"version": "f8449256f5c820606e9da9e5dcffd574d48981b8b6520c234b15f8a6bc3dfa70", "impliedFormat": 99}, {"version": "a61e72002ae43b8230b720eac472b287c2d6e492adaaeb7546570e1ede58c3ca", "impliedFormat": 99}, {"version": "3de403593b664a953f7b10950653129a6b70e97fbdbcc79ad8292cebd6602274", "impliedFormat": 99}, {"version": "35c011c44b69e88a5798bb61158c26e35ce74df571c095c029b29d182924c2f8", "impliedFormat": 99}, {"version": "14cb4ab32e33b9a279f3b62ef3ae69938583fcdb276b219d74d149e9106b7aeb", "impliedFormat": 99}, {"version": "c9bf49c427e33b552a03b20084624635957dc8468eca2a3d461f0582a011c5b8", "impliedFormat": 99}, {"version": "f4d2c3633596eb54d2bb659bc1c60da3d4157c74c6b6e19f8d27965da2b46bf4", "impliedFormat": 99}, {"version": "4a6091ca49cf40b7933e287a233de2c4666c4ac22c80aab2a0bf4a52b467c743", "impliedFormat": 99}, {"version": "53b2c7304bea0d35da3f158365ecd0794a49cbd8882ff2f7122f99a737854993", "impliedFormat": 99}, {"version": "d51c6abeb24e22093f26441b97eff90378ec9bd13979d0d59f5034a2296ef884", "impliedFormat": 99}, {"version": "6f40ad7380099493513c35be209c0b10a531c4e3bf3acf27d5400d030c59971a", "impliedFormat": 99}, {"version": "d2f0d9d92558f5e5406a561675e6437524bee447f554a8ba6f4dbdd627d0b2e5", "impliedFormat": 99}, {"version": "6a0189edf84211867754d862eebdc7b6f075d156b9301a9bebffd89f51ffb66c", "impliedFormat": 99}, {"version": "ef74f47c63b7a4d7a022c1f569f3ca9c14e3277e0385b037587665d69b96be7d", "impliedFormat": 99}, {"version": "4198bc4505f06500bd9b7db780972b9a301cc946896287e0c9da7d140849ea46", "impliedFormat": 99}, {"version": "6bbd5c8d318ee98ff37777e15fbaf08f1328fe71d72c931f082cb942e0a2cd17", "impliedFormat": 99}, {"version": "b4b440d99a10cbfd6272aac5bfd9aa9622b9c1f9c43f7d5cf79cb43825614958", "impliedFormat": 99}, {"version": "741587fb86739542002fd67fed070c07e34dbfd9bbfde95ca955144b861d00f3", "impliedFormat": 99}, {"version": "91691429b483822699b6c2ecdba19b9fc1ba352693db28fae12092b727400010", "impliedFormat": 99}, {"version": "6989d42d669be40f6591a8fdb8e705df5fec8968a38206f5a0047f47c230d1b2", "impliedFormat": 99}, {"version": "20b1db9c33a81af48e43140a540d51c87b6b20f608489fbbf7486c8f56ef0c87", "impliedFormat": 99}, {"version": "a534aae35e31df8c5dfae7d984612adca9d5641b59b49ead295066dee45b4dfe", "impliedFormat": 99}, {"version": "4960805d11b85af4fcff7d549c97447b2294d67d4ee2bbf00695184d5eb6b21e", "impliedFormat": 99}, {"version": "d0b1cdaa14a443a383bfe147dc579b4a836b73f8dfe2b3289e58e871fcad0bf8", "impliedFormat": 99}, {"version": "2546d813c0fcb88951aeeb0c59d42fcc188ca463a6b64045cc091cbe01737664", "impliedFormat": 99}, {"version": "f03eeb6a19310c90fca912e9d3d618bfe78a590e2386695ac4fb05511e6b9a44", "impliedFormat": 99}, {"version": "fda15a21c72487186d6e08d90b6d2554eda631c7bfa71c8805bde1d409f04c4f", "impliedFormat": 99}, {"version": "aad34743471540dc34740144e1dccc42c9b4a1522a8f60ea6f8bece95f226aa5", "impliedFormat": 99}, {"version": "c4feb5adb299f304513b63720b3caadca698d20eb5f2ba53f540609576399ed4", "impliedFormat": 99}, {"version": "3f6ff7fa12f7ae9e51fb3335767a23feb2042397ff6dd78836ab8380ce06b760", "impliedFormat": 99}, {"version": "e379f2cc178fbdfe06bd7575ed0c3019f06307503753d2e3833fa08cccdf765b", "impliedFormat": 99}, {"version": "05e7d52d0f13fc255dae1568da631c3b31ae36097bf4fa7fafa5d4fc0a902d2f", "impliedFormat": 99}, {"version": "b911ec34b809d0cc9bd3392c04f5fc4b7d29fc43635330ec94ddcb64aad6c32f", "impliedFormat": 99}, {"version": "7411280457182312e059b3e78910089b75f7694645c9caa75e0b2e3fb1e6e9c3", "impliedFormat": 99}, {"version": "035cdb01dc859990cc531611dd6c7bb0144f5c02a911b06e7dfbf3232ee0bc73", "impliedFormat": 99}, {"version": "15f23c7f87961ef45889ccb37db664270db9c7ceb127a4d3938521ed095504d2", "impliedFormat": 99}, {"version": "cce8976bec1dfccb5e48ed58df797a393e3c894397b40986884a173e3ef8fb51", "impliedFormat": 99}, {"version": "d1dfa8127d21751115a0a6ae3e0e0e41f70eabf45e23787ba2d327a14669e518", "impliedFormat": 99}, {"version": "ef87c5b95fbe2151e96c89e6c80ad7dcfa895a7001ea9c0cc258eca3eb84ae49", "impliedFormat": 99}, {"version": "2433129fe6d3d67b8268ba54abd4ab1c7c2f7a32444d4c6a68a9a10be06cc617", "impliedFormat": 99}, {"version": "e969d9b9fd9ca2e023ef701519ccd75e207dd52b92f9af22e15c04fea8e719c4", "impliedFormat": 99}, {"version": "18bdb597e29cc27e765330c5ab04ef4de75a9f019fd8c457f88ed777fef90774", "impliedFormat": 99}, {"version": "dd429b03ce8ba91ab6f204d6c2c7ca00fb3cff07b956da1ac8c60360da28d866", "impliedFormat": 99}, {"version": "b7a63ff548e03c363de65f81f7c31bf98f77b73f13054ece8ee2bc1c1ed9cf6b", "impliedFormat": 99}, {"version": "72a7c47fbcfd19b0814dd7555950d2726f1530daec8f0c98de3107cb6654eee6", "impliedFormat": 99}, {"version": "5f49779e856a15a93dbc55628c6dd22787c4729a6ecd4a3ef0226ce3efa54d6a", "impliedFormat": 99}, {"version": "bb836f3e3bb9cff93ea6cd392b5fcb88aae3d664d7c09171e6ffacc2f0a44759", "impliedFormat": 99}, {"version": "612f919817f17d0a4ab4dc0bb83f1af7b6fd3a810ab8265f3ba247619c90118a", "impliedFormat": 99}, {"version": "02d5344b11cf703ffd698f1874f5298d855ae6a91c3a2d42c3d95b70c2f4e6f7", "impliedFormat": 99}, {"version": "f6a02ec242fe847abb54511123ee93c58ff13d7b660bfb8a01eaf5edc39e8856", "impliedFormat": 99}, {"version": "4ed57726726e281f991b7419a8df5536aa8c1189bac3a0386ff590c8f16b7bc0", "impliedFormat": 99}, {"version": "8ead572121be169161fbafe5293a189110c391b15670753f1be62d6298a316da", "impliedFormat": 99}, {"version": "3801017d48638edbf32c445143b804711d2bc1a2ef51f0dceb25fe8a5b591bd5", "impliedFormat": 99}, {"version": "2d5537810389a683449de9b0896ca4b130b93a339d8d72836649f08cebd17f1d", "impliedFormat": 99}, {"version": "773f4ca58611a16eae2143575c1a01d738de48378dd2d11fc400be42ef2daca3", "impliedFormat": 99}, {"version": "558d19d1b6743e92b564bfbf3edf3501ed8bdb2d090181b4fe5003b884694c38", "impliedFormat": 99}, {"version": "9f74f3a8cb86c7035df458ac1964b046e71d75e156ca30e46b7237ccb5c88352", "impliedFormat": 99}, {"version": "bb4a8d5ccc79c02fd91468a00a6a60094b5faf91c69e510fbc4b84ce1f1a44e9", "impliedFormat": 99}, {"version": "a68d52626a14a314e2f910dc7e279bc087f066e60a78b259c3ab78a4cc1b2e4a", "impliedFormat": 99}, {"version": "c796c30eea1275679550236b6f00139fad4be671f5df058fc908156949d91e32", "impliedFormat": 99}, {"version": "405533464641522eab7fbdc2c249729514750d679d5905a84ad94b790787df9f", "impliedFormat": 99}, {"version": "ee2f8c4790ef349e7777b3faaf599823e82e3e59a4bfc2c67c3e1775d3bee50c", "impliedFormat": 99}, {"version": "8effb19bf88f12addeb45df0c5d05e0f6464612d3d6b34f1da8ca8c2c1c5cc12", "impliedFormat": 99}, {"version": "1e013d9eb6ae0803a2aca856d30da9cfc48c6448500544d8600cd1ef8549d311", "impliedFormat": 99}, {"version": "bec1c0e444418bd6b168ffb15b76b9441c761bb2d243c089fa6ea378b2cc72ef", "impliedFormat": 99}, {"version": "c5a21f137c70fdc46c5d643218989ae7d71199f3d6a30af86441dea65a458d5e", "impliedFormat": 99}, {"version": "5c7d1b8744a3c63cb23db59258fcee28ef638307c6862f51572805162a851b51", "impliedFormat": 99}, {"version": "448a88c8e7eda3d8999b7022cfe4dbd1cf586e71e21e999bdbbcdd436ac58b8d", "impliedFormat": 99}, {"version": "3d0a68c3aeea5142f8eeef68dffad223de6aff452d4ff16d7c41bbe327cd9f05", "impliedFormat": 99}, {"version": "ceec50190a9d3d13a8500a8e1d1b6f8f5a3f6be45dc8e9f983530d84dbd69cd7", "impliedFormat": 99}, {"version": "42b9d795a3152c6bb0f641da28297b91d5424cdbe936952ad18c20f501bed1f0", "impliedFormat": 99}, {"version": "37488fdc6ffd2d40cb049ddab8ba198c8e887dfe77510c6c83efb6de34e2fe68", "impliedFormat": 99}, {"version": "03f6241d183131e3118bc6196e3012eccec7df5a002b995be6ed3ad3bb7c8fd9", "impliedFormat": 99}, {"version": "661b89ea587a659596859486a0123a631c34b5057993284d60ef9b87c015797f", "impliedFormat": 99}, {"version": "0e6f5d456e1b73ad322c4b0bdcf10b0f9a8a0b75414d5b9e00d9f561a43874df", "impliedFormat": 99}, {"version": "56a8fb4c1e654942254ca0e64f667a75eeff9c3d4964ef7e759d03821ef09c94", "impliedFormat": 99}, {"version": "e72931e0fd3c01a2153527880a56b53a2fbbe198421809dc2a7c3a93ea74997f", "impliedFormat": 99}, {"version": "b70eb8f22c1217715e2c34d1a83a75d5fa024c32b1aef4b7c4db3f98645cb395", "impliedFormat": 99}, {"version": "bdf3308ab1c4bea0b7ac8432e5651fd55fbf1591496f0b5dfae649f8b0cbd145", "impliedFormat": 99}, {"version": "3a5b6c07dd61016f03d7d4b9b8714fc10e0ecfb2f358783449a6385b930409fd", "impliedFormat": 99}, {"version": "0b70dc15cd46f0b2f0d705744aa3dc4798b87f5113589ca5e1a7053af8edc756", "impliedFormat": 99}, {"version": "7687d8298fbd5d0859b84ec89fbd43fa591970639447cc7b0156670b2a4740f8", "impliedFormat": 99}, {"version": "ae1fc7ed3c72167972acd4f771883d14dd13d635c3b585606218ea4f9f5662c9", "impliedFormat": 99}, {"version": "69204d6d8f37d8ef16ef681b185c5aafc81d81afd5432a25912560f9909ed2bb", "impliedFormat": 99}, {"version": "3608e6f20899db55d817ab7a76390aea19b8e3bf7cb4becb5f3b70b833db038f", "impliedFormat": 99}, {"version": "434af61f55bf25916aba2d8abcec57ceeef35571daff914fe7b54aba771312c1", "impliedFormat": 99}, {"version": "3f31fbb79cd50033ef517ce3296f511ba8654758609015026227740f4892e187", "impliedFormat": 99}, {"version": "b6cbb9a7507ddfb4658eb5fc04835b24abdb18f9b1dcfc821ea8cb220c6b4a24", "impliedFormat": 99}, {"version": "590a91fe582b89a9bad5b5b4d1a6d9747c5287f6e1b23a2a57d1aa60c1a23180", "impliedFormat": 99}, {"version": "5aa8cb7c1bc385a9938b872f6b857ffd91a17cebe05c86a44f12666a37cdf1ce", "impliedFormat": 99}, {"version": "8867ef533f3a1b2d7e77051ee1c764c1942861544873ffd8773d52005a7b30e1", "impliedFormat": 99}, {"version": "157a1f916813abf3e1faadae34279ee65110d7dc8146711240196ce0e46cbcec", "impliedFormat": 99}, {"version": "7d0101529b77bd85692b2a831308a7534a478c60b95a1798c07e14d3a14e4b21", "impliedFormat": 99}, {"version": "8176d254d2942413a87cdf2cd5aa51932e5b91e33fcea3e0fdb29582005095ce", "impliedFormat": 99}, {"version": "19ea1b64d140b3fb5d1b699b09f1aaa60ebf32014f6dee279b96d92ca662d871", "impliedFormat": 99}, {"version": "b2d2ab3ab26f446cad62cc23ded652641a44deb9d19280550c74cc81c7cd4263", "impliedFormat": 99}, {"version": "1b7f1fee5d0df0a2a9e5c4e0f685561d75fed9820679f0eb1f87757a050b7bf6", "impliedFormat": 99}, {"version": "9afee2d40467087a6aed46b5fef0548c2a1351d533f2aafc68cb47694a81f7c2", "impliedFormat": 99}, {"version": "372c39fd10f96d006497fc2bf9d56d0a602119244ed46d087a2bd5bb037821d9", "impliedFormat": 99}, {"version": "82874ef5e1e686a1edebf547e58083dc1f2ca920100eb4f771d4b1b9ba0851b7", "impliedFormat": 99}, {"version": "d9e8f082189fbcd24d1c13275aaffebaf48c9222d20654d61ad7082f6f2df101", "impliedFormat": 99}, {"version": "8f2350543fe05a8d34952c3dae8f9781594751f5ef130384446a729e3dac7bff", "impliedFormat": 99}, {"version": "fc71808cf3e82c4b815b17870970038be40a83c23ea77a47c88bebd7a8a0d431", "impliedFormat": 99}, {"version": "87622b9b115ff00fdcb1ad2e5c0f6064249dd577cd94140d2429aed76218195d", "impliedFormat": 99}, {"version": "987a12239021ad858813841f22475f2a225d3333a2dfd9beb32222c9e2dc2505", "impliedFormat": 99}, {"version": "ed3f6a7fbdb2e7d6bc2636b3f56c08ed34d2ba80ad3c4d30f03a8b12298ba100", "impliedFormat": 99}, {"version": "097d4c89e60fa539682315762384d83801b9c8bc0f24f57a63d62319b6cb88f6", "impliedFormat": 99}, {"version": "ae868f126890affa478b4628684db9c084b00eaea3ac884ece0184e8f9b4041c", "impliedFormat": 99}, {"version": "0aa2fc9a3936aaed64b486dc8efcbd6c62e0afad81ffd72be408cb97867c0b16", "impliedFormat": 99}, {"version": "ee630d71a65d5026c4f4cb01b95eb5277bc9950c36897a3fe5d01409c312759c", "impliedFormat": 99}, {"version": "1caad517833757199ab3830587bca968433d3e1e485c518989e10a3b77f85b24", "impliedFormat": 99}, {"version": "9087d62992fb955a421851106b0e8c815f3e24120b95c56e8373d384e273e0e5", "impliedFormat": 99}, {"version": "f3c8a9af5feab30aaa5c170548fb0748dc2e7f7ce30aa0050555419bee0c05df", "impliedFormat": 99}, {"version": "ebdb84450ad6efa9a70dbb78f4c0f9a16888bd798eefc37f6cd04d2572206242", "impliedFormat": 99}, {"version": "f93d43b0832bc9f5e6a3ec0358bfee8dc2f44f748278f3e6a073220844e78c78", "impliedFormat": 99}, {"version": "a15b1957c98e891ab28b838335bb1deb557343bb4124a9975df71d3e523a8a46", "impliedFormat": 99}, {"version": "30d463e7ce174f7a529d3a832711f424c984cf517c08f59dbcd2ccd5b16bb6ea", "impliedFormat": 99}, {"version": "6767ab11a8cda8c0ac2ac7e2252bf7be2299410752049237a48d93c62a4a7195", "impliedFormat": 99}, {"version": "556ec31b542b318f82f9fbcbcea81d9c139ab820d4e32df8327b81843dc32234", "impliedFormat": 99}, {"version": "256cde5dd5a4f0ed7516ef587efd4bef006317e8daffc232974fac0efe47ecee", "impliedFormat": 99}, {"version": "53c4229dc8cd2aa22a2c58537514818d429b6972555241f821cd7e1701c42d38", "impliedFormat": 99}, {"version": "dbfcc3a90669180c15e0817815c5a9ac090b9473998ec0bedbfc3dc98fdafe12", "impliedFormat": 99}, {"version": "6745a82126e61c30cb5a8db54d35886159c53ac5a28f5a61d31fee282598f7c2", "impliedFormat": 99}, {"version": "be768a2f53e62d96a980aa56e02861472f7e974862730dd12fa26cb4bc50e348", "impliedFormat": 99}, {"version": "1ba993dfeec6dca5b138bc0370f561e5a220a367b7fc015a935e015ecc865aa4", "impliedFormat": 99}, {"version": "1bc5d66f065f14c9c6290f6fe09492e60d30901737b68a1e344f2d61ed001e96", "impliedFormat": 99}, {"version": "f3a27610d825a99ec583a666eacfb2f5cced7b452d0c3338815b0caa4639ca7e", "impliedFormat": 99}, {"version": "fe896af05f06c4c6257fdc8e8cad8a278c90d4b38ff6b70efc5b5e3ecc880bb4", "impliedFormat": 99}, {"version": "362db1b55e2006226b53ac79a8ddd5a12976bdd4531badad0ddff27b49817de2", "impliedFormat": 99}, {"version": "c3ff132ac57ce2706280f9e145befc0e7ee6060caebb32ff3022e9c154575876", "impliedFormat": 99}, {"version": "8c1e7fe0b90aeba2f3eab5fe6e5fd66e70ddb6cd998a1eda1c5cfdd6336ba94c", "impliedFormat": 99}, {"version": "a0f0701ce0a5be197aa18a41feea179f1e21a2991918ca26320753fd3cbc17d0", "impliedFormat": 99}, {"version": "89af4f75c1f204d678637102d01382e0b8b167e0b213a42a6fab2a64826e815d", "impliedFormat": 99}, {"version": "55eb256718c8258c829c4220a707904a8c4b3838599deace11c7bf72c19a1c12", "impliedFormat": 99}, {"version": "50d2f4d075114bd15852e0ae28244f897e8fb7109fdb4bb980cd0d3071ffa87e", "impliedFormat": 99}, {"version": "fb29fb3a2e3247167f4e699f19b47cbbe02e3137794c48d08ef6140c13a82a13", "impliedFormat": 99}, {"version": "b8b338b2581fe913b51078571e66b93f60e27089753bfcf0124cd0727684571c", "impliedFormat": 99}, {"version": "00287f47a7a9ab63f5e218d1db19923519e6761a3ae2ba9222d2c38a21a4bb35", "impliedFormat": 99}, {"version": "17f1776b27b2c29bebba486721f5d9319dd9b651b6e3be83de3fa216085e948e", "impliedFormat": 99}, {"version": "97fe89bab2cbd68a825b749e69b091cc01cdcbce11ea81dd9292b41a0067fb2c", "impliedFormat": 99}, {"version": "7468715152819058c1a2a27ea8688a7ae51f9800f1273e0815a60b53a0c023ac", "impliedFormat": 99}, {"version": "f253619c22ea40bf7cbe77923e570714f74ba32e33fd3af620a623867d94561f", "impliedFormat": 99}, {"version": "a9615353b037dab7ed7a5ba67807a7daa8c15cd433f627170360135ae30f7913", "impliedFormat": 99}, {"version": "9ddf47eb87c7613d5a5bbb577fe6ce87dd34f2c7681dede0ab9fa1d6bcaa7242", "impliedFormat": 99}, {"version": "57b00b8088284b7178fda7be8f5987d5edcdddfa10bd2f777c9910bbb7ac7e97", "impliedFormat": 99}, {"version": "eeca86e723c4dd548eaf507190e849b925fdc0788734afe84a4e5ad29ea518b6", "impliedFormat": 99}, {"version": "cf03afdf519792b0f8bcc22c984a5521c5d192c3f46b1caee9d645dc02cc076c", "impliedFormat": 99}, {"version": "8ef260aeed7f688a8c40f0a3480e8e4ff4c1406b0afc44544a8d0087c9f80cd2", "impliedFormat": 99}, {"version": "1074bad4ea7a4cd8088f39ebf5169e355510089d28ee7b775ba1ee5ddbd67a2b", "impliedFormat": 99}, {"version": "500265f07d0faf96f8b04ee1c9e0a77a8e5e1ae07b075adf58105c05db2687ac", "impliedFormat": 99}, {"version": "5eafb802b8483ae0fda85920af0802e633178c701f631ad85db80156054a3840", "impliedFormat": 99}, {"version": "d4326b0dc272b46b1ce13fce5b29331a705b1aaaf79c67dcd883fea74c713b81", "impliedFormat": 99}, {"version": "41edc9dcb80ada08b64177bd4405650842e2e17f86f2ba905e5a7395b660c1f6", "impliedFormat": 99}, {"version": "282c37fb44ceeb5bcfcf070f383314a1bc33b1c1f089f682f53e79b0bd90ce7b", "impliedFormat": 99}, {"version": "d702cd1aaf59322d1532b37530fc934e2bed5a875d3239dc1eecd275f8b76734", "impliedFormat": 99}, {"version": "57d5f16d751884e0a2e97ef772d1a24f256dd1b82b35397041d91baa85e4bd93", "impliedFormat": 99}, {"version": "d5851073cd5047ff38938d853a37c2d709d68a74017bd4df1010187f44541fa2", "impliedFormat": 99}, {"version": "2133317393eff9aa9778320a5c251349f5d0a3597715fa33eb08b6aa9c9deea6", "impliedFormat": 99}, {"version": "979fa80f9aa7e1f015e0a019a28baed03f69924db612889d1899b62b4439f8b7", "impliedFormat": 99}, {"version": "67cfa42620d86ad53914cfec05a9d8f90e43fb28fef9323275d25f6dde1d7790", "impliedFormat": 99}, {"version": "ec5c726ce278b542cff27f8c2a507166eefcb9ae2130ba3785b1c7e168a8f2a0", "impliedFormat": 99}, {"version": "08b4120029f17693ae31a695121c2a37fa1b7f98769aeaf4582ec7a7b25bb352", "impliedFormat": 99}, {"version": "cc5354e745ad65d3a07f67586f85565d332db8f83ab6119616d5dcd5e57bc3fe", "impliedFormat": 99}, {"version": "0be25ceb7bdfe3fa2597861b1c579897370ab1c936494ddb68fe55c85a07be73", "impliedFormat": 99}, {"version": "7a1f228faa5fa5b29b96c1ad04293e310a20c22ec1b83b5adbd1ee306625ddb1", "impliedFormat": 99}, {"version": "22d5c827159162dd95e53a3a67e0d84b61f08d549589ce83dc650ba2446e4055", "impliedFormat": 99}, {"version": "57ab97e8e4bfe6a726c44fa4982c63713e21ebaf407c314afd4e48c235ffb96c", "impliedFormat": 99}, {"version": "54ee6720ce787300bf050b24224405696295d9e2f3f42da366a0b62758835451", "impliedFormat": 99}, {"version": "af25c46e77f36f675d5bff643ca3b984304a46e7cfdf10f4531c0ad003299946", "impliedFormat": 99}, {"version": "fcd0755cfd48a03797014183580db6d6caa4f6b2c06b5eae2501e45754457deb", "impliedFormat": 99}, {"version": "49f2593f18dd90981d30b5d2712bfdf56318c3456f3776a83b23b120b8d0c065", "impliedFormat": 99}, {"version": "e6fbb74c785dade2e68168cfd141a4accab9c9ac5f3be344b8d116ae533cb7ff", "impliedFormat": 99}, {"version": "83eb2cbb1913c3adb9cbf391eacac9bb6ea2627737e4a3c0350d78bc8e1c040a", "impliedFormat": 99}, {"version": "7d206c70ec9860ce9d65dede8bcf731fe3828b34a566afe01000f0e8e0324b94", "impliedFormat": 99}, {"version": "697929cc709ce1a14bfa22637796c90de5a7deac1afc32d703aed10cd148230b", "impliedFormat": 99}, {"version": "a96c285e78d88334d074cc966ceadc5ed67608dfac9c6626a0f800288b692ccc", "impliedFormat": 99}, {"version": "c2bff621d611a1cc7e0cbf6f8bb2e5fd99930b159d80bfc721bd6e2f3ac1af50", "impliedFormat": 99}, {"version": "56e9483c87ffd60f3811152a21d9704384c6539b13fef717ddbf99c5d944c330", "impliedFormat": 99}, {"version": "5c06912ea08265c5b0b46e34ccb3c2082cd608bce26e80d9d810af2cc47fc990", "impliedFormat": 99}, {"version": "32f816bc6d64a56503bb2398846ba92f6e058d93a57ca8dba27790b8214fc88c", "impliedFormat": 99}, {"version": "99c9b803342e29e16248f6d03fccbc88f202c57852c4ef2f8f37407965cfbb6a", "impliedFormat": 99}, {"version": "9057244241137ab9d0f8e7b2419d26d6b5794c063ff2a390047ab733e17a84f6", "impliedFormat": 99}, {"version": "68a5d0c31d7f136af350c10d778043fabe5c94407495d9417fdf8e543ac277de", "impliedFormat": 99}, {"version": "afe62de8880caa0ca0cf59e8bb37d93f6d4d19d7ee887ec9b88cc5b79c2e2cad", "impliedFormat": 99}, {"version": "0c46d7c267ba59b302512de340f4c92b97764eafd086c5b13477fedfa953385d", "impliedFormat": 99}, {"version": "0f2e941fbb7fa25b52f407745686b2e905ec03225af1de5285dc8113cf9f38cc", "impliedFormat": 99}, {"version": "a12f3295a92f365c2919a9b128984c35486282b7de8f3ff81fc360b8f137aaa5", "impliedFormat": 99}, {"version": "80b3f9c2b731626233662c38a5c4ca60a1ae28775a031d59b105672ef1a3f934", "impliedFormat": 99}, {"version": "c326bb72f933aa18f366a29a27dfd4193749c4c077b0464bb31054134a84aa8b", "impliedFormat": 99}, {"version": "0222992caad46191f90e9a5987e0c92ca95c5bb631f8f953e4c92b700411321e", "impliedFormat": 99}, {"version": "fbb281974839d3fcc1fc0eb70b71f68688d9d2e3c719f7956f02ada2d03b0e2a", "impliedFormat": 99}, {"version": "f9c21a69d044828e19f2b9e202b4fb1a1de1927fdd7e7ff0c40d4f63ebcc9b42", "impliedFormat": 99}, {"version": "a23673b34f0d0b6d6925f8db11c21d27c98bcac58faef3e0e20805057737ed33", "signature": "b5fd370f54166696f6cb9ffaa135123c2d56905eb3fe32eb53ba51489bf93473"}, {"version": "f697e4b5b03e13d98a4cd6fe8f51bd604022498463f8c4fca532213a439d58ea", "signature": "078c2e3055b0d4ae397f184061f5a3eed7b187b01fd06e7edb55fce1a7a5ce79"}, {"version": "3300e952b9da8ba9f9519fb895a3f5955dd3c7c4d68a884975ee21cc34696daa", "signature": "239db1d26be2fbb3418ec0b72662dbf79ef52992076e82f782cc0f5105c486df"}, {"version": "bfd3e10679e684630d00e3dcfccdb7e7db827a3f3cd850757761afd1ac46204e", "signature": "8f61cb47220bdd785e8da216eabe432b25d7a2e227689a169ad80b4a6a886f48"}, {"version": "fe93c474ab38ac02e30e3af073412b4f92b740152cf3a751fdaee8cbea982341", "impliedFormat": 1}, {"version": "aa4feed67c9af19fa98fe02a12f424def3cdc41146fb87b8d8dab077ad9ceb3c", "impliedFormat": 1}, {"version": "1e00b8bf9e3766c958218cd6144ffe08418286f89ff44ba5a2cc830c03dd22c7", "impliedFormat": 1}, {"version": "7153c5dcbb126b34f10026c210296c3abf0fb76e93522c46a6f5ee461578b698", "signature": "563143ac40c6e87498e196b4ebfcc9b2d1ef83f8befcdfeffa0bdf9315196232"}, "869ce09892a78746f32233bb8756ce9165b2dba06b75a8d87875f3f07a27f74d", "77d009a99299a61593d1632c6b9458d7f9117c265cbee945f07dc179fbf460c8", {"version": "443e791ec6f33a90db59c6eee3ae3965d771110106b4d1d4db6f673749f56b19", "signature": "6dedb3f04188587c3eb820ee35026a22ebe58b5bdf2835362783bb24dd19124e"}, "7b7cd790864f0d53cb83ba8da634ce5bb0279d29d970597d71b6e99da62524f1", {"version": "88fcf2ed7d4b622a52c3dca318c570cf30db5a0b48280a0c545346324d5aaca8", "signature": "86bb4859356ac66c7e73c41b966dbf1ae7758e9bdc7c2d010c4da840e08d3852"}, {"version": "59d22b7000458c05067c25b420a2b8ab7a0780a1bfc57f57b0385e4e2f60cb52", "signature": "ee0f23c83c4dac9f116cb654b2e5ed2ddcbc2e21a281f7bd389bc296c9faeb67"}, {"version": "ec2ca578b5a219b63f2e8d1d4690dadffb4a9578ac0ba84daede798c2375a55d", "signature": "b9f4816c6a6076dbfaf8abd504828ded6929ce96752d38e1b068d86bfa025620"}, {"version": "a8891d2b3f9d66927ef6f14b90fefc0f4a395da336eb6b6f770f6d7f0c1e619a", "signature": "5349b71c3ea832211d89961650ec8ec7c63e31ab78a5a43fba7157c7df7748e8"}, "f04d0838ee80e780fb45f8f2da44b1d2944f0ceee511bebb3a879ed9d51b5a72", "91aff288a7d5ce60a36865f4fd3fe3593befe72699c41d971853d8e66416574a", {"version": "0c13edb8be44a43b639d68b50668a585efe452efd8e7a09c61c6a14e548566e6", "signature": "a4a326e5c4e4521df44bffc75ef6a8d5c6e9d5a8e8f37dd2e36b538991ed3d5a"}, {"version": "b98851d9d1a2e7f81dde35f44b044ce944ae6f04337fb56af4791392a5c15f8a", "signature": "3993a8cac5b348f3276f973710abe1311d8c2b717a7979a39f1e2450c4ec5518"}, "86da37dcae68155c85368c75aa1440ef2782f71d9272a58e0d0ea0afc4eea948", {"version": "b491fa3345ff8291057a93d2577e1406a0143995302fef52a4ec625767c4507d", "signature": "53087d821e77f663819b3720bc635e9b1ff9069dee00a5fde78f4abd0861497e"}, "38fa304464a9291c969c798daa3e3053cebeafb7c75b0519d14739b3860c81ee", {"version": "91b4ce96f6ad631a0a6920eb0ab928159ff01a439ae0e266ecdc9ea83126a195", "impliedFormat": 1}, {"version": "88efe27bebddb62da9655a9f093e0c27719647e96747f16650489dc9671075d6", "impliedFormat": 1}, {"version": "e348f128032c4807ad9359a1fff29fcbc5f551c81be807bfa86db5a45649b7ba", "impliedFormat": 1}, {"version": "8ee6b07974528da39b7835556e12dd3198c0a13e4a9de321217cd2044f3de22e", "impliedFormat": 1}, {"version": "5e1d8a07714f909beaaaf4d0ffe507345a99f2db967493dd8ebbfbb4f18e83ca", "impliedFormat": 1}, {"version": "5f12132800d430adbe59b49c2c0354d85a71ada7d756e34250a655baa8ad4ae5", "impliedFormat": 1}, {"version": "1996d1cd7d585a8359a35878f67abdd73cc35b1f675c9c6b147b202fdd8dfc3f", "impliedFormat": 1}, {"version": "5a50dbfc042633fdb558e53b30b0a005e0b78e142a1fe1147a8d6618ca69ec99", "impliedFormat": 1}, {"version": "7d1fd5b1f5f9a463fbd2088e81b1a97571a942448e5dc292021f7c89b1b1135c", "impliedFormat": 1}, {"version": "6fb55bb881f4a7167649e6925df076f64a1db2f50632df4674e4621a9445c954", "impliedFormat": 1}, {"version": "4374cefdde5c6e9bad52b0436e887b8325b8f407c12035194ad02c28f1553a3a", "impliedFormat": 1}, {"version": "9b70cad270593f676aecfe4d1611dc766464f0b8138527b0ebbf1ff773578d69", "impliedFormat": 1}, {"version": "b4f85bfb7e831703ac81737361842f1ae4d924b42c5d1af2bff93cca521de4d1", "impliedFormat": 1}, {"version": "5fea76008a2d537ca09d569ffae4e08b991b4a5ff90e9f4783bc983584454ede", "impliedFormat": 1}, {"version": "21575cdeaca6a2c2a0beb8c2ecbc981d9deb95f879f82dc7d6e325fe8737b5ba", "impliedFormat": 1}, {"version": "40ec58f0fadd0b3981b3d383e1c12fa0680115ae9f018387fc2cfc0bbcf23204", "impliedFormat": 1}, {"version": "849b9e7283b7309a4556c9b90bb8e2dfc27751f157798065bbc513dcddb09a8c", "impliedFormat": 1}, {"version": "10e109212c7be8a9f66e988e5d6c2a8900c9d14bf6beadf5fa70d32ada3425cf", "impliedFormat": 1}, {"version": "2b821aeb31e690092f8eae671dd961a9d0fd598ff4883ce0a600c90e9e8fa716", "impliedFormat": 1}, {"version": "26602933b613e4df3868a6c82e14fffa2393a08531cb333ed27b151923462981", "impliedFormat": 1}, {"version": "f57a588d8f6b3ce5c8b494f2dc759a8885eaee18e80a4952df47de45403fedbe", "impliedFormat": 1}, {"version": "34735727b3fe7a0ed0651a0f88d06449163d1989a2b2de7f047473adc7c1c383", "impliedFormat": 1}, {"version": "a5b13abc88ab3186e713c445e59e2f6eee20c6167943517bc2f56985d89b8c55", "impliedFormat": 1}, {"version": "3844b45a774bafe226260cf0772376dce72121ebb801d03902c70a7f11da832b", "impliedFormat": 1}, {"version": "7ae65fe95b18205e241e6695cb2c61c0828d660aca7d08f68781b439a800e6b8", "impliedFormat": 1}, {"version": "4e28cc749981da4c24922104abd9a8f94261d0e25281df675e7c0c032f6f79aa", "impliedFormat": 1}, {"version": "369b7270eeeb37982203b2cb18c7302947b89bf5818c1d3d2e95a0418f02b74e", "impliedFormat": 1}, {"version": "94f95d223e2783b0aef4d15d7f6990a6a550fe17d099c501395f690337f7105e", "impliedFormat": 1}, {"version": "039bd8d1e0d151570b66e75ee152877fb0e2f42eca43718632ac195e6884be34", "impliedFormat": 1}, {"version": "89fb1e22c3c98cbb86dc3e5949012bdae217f2b5d768a2cc74e1c4b413c25ad2", "impliedFormat": 1}, {"version": "d3cfde44f8089768ebb08098c96d01ca260b88bccf238d55eee93f1c620ff5a5", "impliedFormat": 1}, {"version": "293eadad9dead44c6fd1db6de552663c33f215c55a1bfa2802a1bceed88ff0ec", "impliedFormat": 1}, {"version": "54f6ec6ea75acea6eb23635617252d249145edbc7bcd9d53f2d70280d2aef953", "impliedFormat": 1}, {"version": "c25ce98cca43a3bfa885862044be0d59557be4ecd06989b2001a83dcf69620fd", "impliedFormat": 1}, {"version": "8e71e53b02c152a38af6aec45e288cc65bede077b92b9b43b3cb54a37978bb33", "impliedFormat": 1}, {"version": "754a9396b14ca3a4241591afb4edc644b293ccc8a3397f49be4dfd520c08acb3", "impliedFormat": 1}, {"version": "f672c876c1a04a223cf2023b3d91e8a52bb1544c576b81bf64a8fec82be9969c", "impliedFormat": 1}, {"version": "e4b03ddcf8563b1c0aee782a185286ed85a255ce8a30df8453aade2188bbc904", "impliedFormat": 1}, {"version": "de2316e90fc6d379d83002f04ad9698bc1e5285b4d52779778f454dd12ce9f44", "impliedFormat": 1}, {"version": "25b3f581e12ede11e5739f57a86e8668fbc0124f6649506def306cad2c59d262", "impliedFormat": 1}, {"version": "2da997a01a6aa5c5c09de5d28f0f4407b597c5e1aecfd32f1815809c532650a2", "impliedFormat": 1}, {"version": "5d26d2e47e2352def36f89a3e8bf8581da22b7f857e07ef3114cd52cf4813445", "impliedFormat": 1}, {"version": "3db2efd285e7328d8014b54a7fce3f4861ebcdc655df40517092ed0050983617", "impliedFormat": 1}, {"version": "d5d39a24c759df40480a4bfc0daffd364489702fdbcbdfc1711cde34f8739995", "impliedFormat": 1}, {"version": "309ebd217636d68cf8784cbc3272c16fb94fb8e969e18b6fe88c35200340aef1", "impliedFormat": 1}, {"version": "6e4fde24e4d82d79eaff2daa7f5dffa79ba53de2a6b8aef76c178a5a370764bb", "impliedFormat": 1}, {"version": "ef9b6279acc69002a779d0172916ef22e8be5de2d2469ff2f4bb019a21e89de2", "impliedFormat": 1}, {"version": "12b8d97a20b0fb267b69c4a6be0dfad7c88851d2dcab6150aa4218f40efa45f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0e86102dbab93227b2702cba0ba06cb638961394577dc28cd5b856f0184c3156", "impliedFormat": 1}, {"version": "6c859096094c744d2dd7b733189293a5b2af535e15f7794e69a3b4288b70dcfc", "impliedFormat": 1}, {"version": "915d51e1bcd9b06ab8c922360b3f74ffe70c2ab6264f759f2b3e5f4130df0149", "impliedFormat": 1}, {"version": "716a022c6d311c8367d830d2839fe017699564de2d0f5446b4a6f3f022a5c0c6", "impliedFormat": 1}, {"version": "c939cb12cb000b4ec9c3eca3fe7dee1fe373ccb801237631d9252bad10206d61", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "3b25e966fd93475d8ca2834194ea78321d741a21ca9d1f606b25ec99c1bbc29a", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "3b25e966fd93475d8ca2834194ea78321d741a21ca9d1f606b25ec99c1bbc29a", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "impliedFormat": 1}, {"version": "92d777bf731e4062397081e864fbc384054934ab64af7723dfbf1df21824db31", "impliedFormat": 1}, {"version": "ee415a173162328db8ab33496db05790b7d6b4a48272ff4a6c35cf9540ac3a60", "impliedFormat": 1}, {"version": "80e653fbbec818eecfe95d182dc65a1d107b343d970159a71922ac4491caa0af", "impliedFormat": 1}, {"version": "f978b1b63ad690ff2a8f16d6f784acaa0ba0f4bcfc64211d79a2704de34f5913", "impliedFormat": 1}, {"version": "00c7c66bbd6675c5bc24b58bac2f9cbdeb9f619b295813cabf780c08034cfaba", "impliedFormat": 1}, {"version": "9078205849121a5d37a642949d687565498da922508eacb0e5a0c3de427f0ae5", "impliedFormat": 1}, {"version": "0ce71e5ee7c489209494c14028e351ccb1ffe455187d98a889f8e07ae2458ef7", "impliedFormat": 1}, {"version": "f5c8f2ef9603893e25ed86c7112cd2cc60d53e5387b9146c904bce3e707c55de", "impliedFormat": 1}, {"version": "8e6427dd1a4321b0857499739c641b98657ea6dc7cc9a02c9b2c25a845c3c8e6", "impliedFormat": 1}, {"version": "58da08d1fe876c79c47dcf88be37c5c3fab55d97b34c8c09a666599a2191208d", "impliedFormat": 1}, "d928196c731d0532828f048957968de24f4286b89e36e956a4cc731b02acdea6", {"version": "caa8fe1141721b3bab79364471425952184458a1602013c345a5fb6ad6bd1cc8", "signature": "ecad5e898b7f7ef600130df12731dda2128bc6b6f00685d29a8f35da44f5771c"}, "9f128da33c4d160ca5fdea4fd456d9199d952f6854191e55882fcc1211b2f0b2", "ac38e22d819e10020d83ea27c5c21db50ab5abc3537da44c8fe51236733ad323", "0cd694f823b9757963675dba872809e62e05dc582bf225f318975600342e7a0a", "e2794fccfbb6251047da7d8821b56f3c0ef5dfade15de5c8c7b6c6d17c58584d", {"version": "c652e3653150b8ee84ffc9034860d9183e6b4c34be28e3ba41b34b1417941982", "impliedFormat": 99}, {"version": "eee0f5492d6584224fb6465b635e6fcde743565c130aba0d830dfd447d5e42b8", "impliedFormat": 1}, "37034508bd671c33317aae9253aba288809f1511a2a844f0508365bdc0d3dde8", "6db04637232a056d65c8fea81ed17094a6ea0baf9af1dec2fb02c75d72fb7f48", {"version": "d3f6b10548f8ae52b2a05ecafee7335780cdbcf76a8eb9f26f8b375d640959fb", "signature": "05ba2c63b9331513e358a76b61665abae5cf8e49dc4a07599bc902dc6d05e68a"}, {"version": "56e1b5470f0524844c5bf9d0c44e08cc96c2ad4794b861fd10b3b935d9da0278", "signature": "ec0a24806da80b20859c0f3a3d22707f66a0d2cbcdbf8535dfb78d42500db36c"}, {"version": "ba02594f2f5d344dc75863c28da8cb39dea13bc702ac8cbfda9a0dbf04f35c0d", "signature": "3100f4fc1020a0d413c0dce9256791fff6de616a02019aff4223b728c7e2fa7b"}, {"version": "58505e8a77a12fa02667b48e2e181b74cf96575892c32e8c1af5ea279395fe75", "signature": "72aeb5e361f5a160dbaaa6fcd434c1c38faf25cea519d7d3dd0e1bdfa3da50fb"}, {"version": "6452b9a39f8ad465425edf068b8c471f37450546eb49b3d8567fc7b708b79416", "signature": "1ce5cbd9d4240ec5a293d4773677543d511872c500288ef3acdfae1435622447"}, "242fd632258df0bf3bca57018aa666f33fed48535a28d1528ddbd3d7e4e5450a", "135a7c56d37637ec48dfa6bb379a1e71384a9ac2076e310bb42248e8dd4d5954", {"version": "91dfc3a17c98991b2fb4ab22ea098c1678796dc6e626e00cb56ff9793b349316", "signature": "89267e908f7d4d0eb218d813ca85ae6ba98e96cc64a58d2818f817ae250b8310"}, "c20a4e686a7e27d5444ef36c1f86c305910192062e8b29c94e4dd977951fc209", "b17eb07a76115d486ff3e6ea732e8f8368c4c8b399ee52a523179dcf058a375e", {"version": "d30e67059f5c545c5f8f0cc328a36d2e03b8c4a091b4301bc1d6afb2b1491a3a", "impliedFormat": 1}, {"version": "210469f9985f125b0725d46b0c97a3cf367904463c138030f4c86217510316e9", "impliedFormat": 1}, {"version": "f77db1be13db8dcf8c7a268c1f235c1ba1e4707e829175cdc237421b3c346f9d", "impliedFormat": 99}, {"version": "05cb95655ecadfd1230547b84367d1c4d40a4bb6309545b8dc4d1638a3de1a50", "impliedFormat": 99}, {"version": "e980a9cf8404a649ff3a2f0628d76e4469613c02b98bd3b922756d7d962476c9", "impliedFormat": 99}, {"version": "f8adbcb59256526bc69d94fb5c826e812ebd1321b30ab35baa9997d74d45dd73", "impliedFormat": 99}, {"version": "050a4570de5ad6e47cc9ac9fd9db7a26e57dbe1daadadbc19b20567941f8bf1a", "impliedFormat": 99}, {"version": "5ed040255a4d5181f8ecb4ba90b8b38e0a6f1becf0ed860ca75b6e52c46db0bc", "impliedFormat": 99}, {"version": "e22a49cd604cab3b62b1968a363d7b182edcb23d46793ed12cf5cfc6b1597f39", "impliedFormat": 99}, {"version": "ff1b4730f5d49d37b73ee2db3443145daa0bfc7ff9c865134d871b08955e389b", "impliedFormat": 99}, {"version": "8e64b72fa289b7f133b8cdb7d837f73e30ca7eb76ad88e1020d97c405c94fd7e", "impliedFormat": 99}, {"version": "1f907507e41cc3df66b4521b80134bb8f7afada8d31c10f7100c93c90ab0f84e", "impliedFormat": 99}, {"version": "6bb14070b70b4c9a897a4f5088af984e6e316b420d00d82fb962bad577896723", "impliedFormat": 99}, {"version": "46e17953f7ffbf43d4328fcb5983e0ade2932fb56e84181e6929fcdcfa7c7aa6", "impliedFormat": 99}, {"version": "113aef5576cd65f310927b17ae5f6ac8745c542a660bace5f019034d536fbd04", "impliedFormat": 99}, {"version": "ddf0fdbb010c94978c1151441171f0aac236a23b6786e9f6332f745527d905e9", "impliedFormat": 99}, {"version": "a2c1678ec68c42795e2ac068a7d026b61680357d2a881c9df211dd0f83d077fd", "impliedFormat": 99}, {"version": "2fe207d2e8662abb709772fff1f3ec3116a4787b5caa4e862daa5dab2753edd7", "impliedFormat": 99}, {"version": "a7af5f01007f450dc8cf2cdbbb11f4d4bf8bf3faa869d21267db5de74ebf665a", "impliedFormat": 99}, {"version": "709cb4986cbe2b58ac3bbbad45dbfa24cda1b62c794c73b96e9ff1236dd0d5d1", "impliedFormat": 99}, {"version": "afdc9b1fd1937d9b649bca2b377d1144cc9c48158403c17cfd21b6e1e8b25099", "impliedFormat": 99}, {"version": "1d47324801b498d62f31ea179f58e1f3eaa1e607914504a7c92fb5465affb851", "impliedFormat": 99}, {"version": "95fdf978302838125ac79d9d5e9485d8fa1ddd909664bf5cc3b45ec31f794fda", "impliedFormat": 99}, {"version": "d92bf7d6d30c85e53b961236ceeb099e73a1a874849d038a348b51383087872f", "impliedFormat": 99}, {"version": "e56e4a57ca5aa762d67fd3d16471c47592469944315fa5e92b3b09c83eabae91", "impliedFormat": 99}, {"version": "f3d31927b7a3d0f2f119a05a102af2bdd1fc4f759fe43d508a64a80b3b341f6b", "impliedFormat": 99}, {"version": "678700fba88589e28648a923e4b98ab60f3f7df4742412419e29f95966da4475", "impliedFormat": 99}, {"version": "5a71b307074ef3d2794c4104248b7a3cad5f486df204da65862a7d24f698fc95", "impliedFormat": 99}, {"version": "9a4496ad6d48bc801a122c11e94ee1e3f0710bda38b125573f67f5cb0add1733", "impliedFormat": 99}, {"version": "afa5e16f2ad07d847701e3bde9e7ab36f87e0e3a5c0cb7998644791a1fa3c5b1", "impliedFormat": 99}, {"version": "98cd9124b5d8438db4b4dbd247b2c68ac22b6366a43e6dc4945ae32972f157fc", "impliedFormat": 99}, {"version": "dc21879e45f3a023b5fe459c3da5f2f3cf995f21a1ac533049d8950ce394c045", "impliedFormat": 99}, {"version": "622d6ce66ac838d5d7e968daf4ae760cf49797e3fbfaa2b21d01e0fb5d625bc9", "impliedFormat": 99}, {"version": "ecfa30418b2200ba6496b5f59b4c09a95cce9ea37c1daaf5a5db9bb306ee038f", "impliedFormat": 99}, {"version": "3b56e30b1cbe1bfa7710e88e5e0b8fa6eddc7c2e67615f73bdf8637af68403e6", "impliedFormat": 99}, {"version": "92a8de4f8f6595bf1eb24a19aebff7371c66ae8751f2e045edd9e25ca435e4a2", "impliedFormat": 99}, {"version": "01810afb0ed31afdea0846cee91e85a474727d0966e5bb57c2a4a732854deab1", "impliedFormat": 99}, {"version": "c874e98cd875727ea62fdcd978ac9e067ce07cf7493aa4b8b193fdc3b7318eea", "impliedFormat": 99}, {"version": "455e843c1f8e0df452f101c9ec0b63ab8e749f296c947249f8bbc29bff58c83c", "impliedFormat": 99}, {"version": "eadc4c556b494cc52676e084eadf0b60fb2cc6e2408d1411eeae5cb74068ca86", "impliedFormat": 99}, {"version": "3b8689266e8fb628ca2068ff610ed0b842ff4e407c3a914358ef1895dabfcfcd", "impliedFormat": 99}, {"version": "fc741907f6d8158b2c4722932d745b11dd41f9355a5b325c8cd3cdfbd966d76d", "impliedFormat": 99}, "ddb29a7337f7db32ce4b4e46e8759a7049b1a1587c8fe26492eb4df74e6dea42", "a6626ef0db1d0efce14d287d7e36c5d975a27bfbcf5dfc1609e67b56d18bab29", "6207fc4ac6e824713739461ce8e295ca2fea23ee6758f39b885e38a4b404b718", "f47405df875c02c5bdfd094e5ad2bdc43da05de695ce2d53aaf3cecd5f1dfe1e", {"version": "5034136fb33c03e4f0e8ca11026a74b659c07319b09073322c73be630a71c744", "signature": "c5289f4a8495d0b463cbd8801716e19dc37ce8529d626755a14892b377ca7d7e"}, {"version": "a02c64e6c1ff0da3700ff6f1824e335bbc271a4742d4da4a52014ae710da56b3", "signature": "dc1c6e99da2427a48b4661cf18deb8304056dde650938ebbd823d68d4f6e6d05"}, {"version": "fb9e309b8f569bf93ba026ff5eae847d3fb9c61aa55811f4999aff19972215db", "signature": "ae9503409f82686bf8f7abed5cafad861d34ffb63572206197fdd5bbe6b86ee2"}, "7b6f2f998842d9a6293830015b9b796f57a4a77113d5cc223ff4be8f27d56013", "2e13a2e09bf4feec329948a0547d633850787d1963c1bf14932bced84a1caafa", "54ab5763dcf81f1998eacd746d22b26a9d1dfb0887d16acad715a68c922b075e", "fbf09f1ff7ad76013150b5716ec263951e8af05e82b0f5f33dc843a61bcb59ed", {"version": "6801e146d5bd192e998d4ccb1b99fa09b45bb136dec2622832833ae1e3ba3404", "signature": "91d78152900b3c201bd2ac98486b38887ecd2777b57eeeecccc0e26c3d71b562"}, {"version": "cb78b45f485fb686d8de18922c9169465982bffbb165b09fd03e88bd5cb16f10", "signature": "b05b871bd13173d03b8a6ccfd9d1d187d6f612bf672f565eff21e0da7055aa3d"}, {"version": "7e4eda1d3837bae07d78f105bdc72e648555bcffb6c33e9912392b221b45fa39", "signature": "fd945f401985b0277f9a0af9aff645703518686cb9fc1cf994e3f1b7953e444d"}, {"version": "2266999682ca8eb1e9c214c4f641498eb5e0aba9fc49333423829a3f83629b48", "signature": "cbda62e9bcd76e4e09d28ecfee27a543233411f0837a3ef1ececad3d778fb020"}, "3feff5db3133c3828d0465bd12e6bb99ddd3c733f360da30810cfc4d48e69028", {"version": "4f5965c09578fd6353d149c932ffef9e77eb899825a431cc596cebdd16adc2f2", "signature": "0b537e0253106abdd0b24c22cbee30cc6b4e7977d5dbcf542af235fadeb7dbae"}, {"version": "c6503e2a43dd580ece2f344c044d112c18cfe24a237c4f328b4514e50121c809", "signature": "8ad0319f7c482c981e080125fed3a930b9a46b2c1eff132546464aa2032a0fb9"}, "0adbe2890ae5f928dd959f193940d941f0aaf3efd1e098585663fecb8cda63ce", {"version": "c8da1186922c28d79cb6c477690cc758a59b7470db9a4c6b9cc6a5ae2a604266", "signature": "3bb640e828690a5169b4b32eabf0e40780c2e38063b7fba56a0aa6f4fd71e120"}, "15e668083a06b067208f86cacbbdf8d8fd08c8f633e1d6d6a3a336521f9c8e6f", {"version": "631ac999ce30acdba30215f1132d0af4b35c4960e5661041547f30cf7c67167a", "signature": "4233ffc277e742b699c8532fdc07823f1b4f852a0cd9eee260275ad7e81d3b5b"}, {"version": "d2df84742a2d23f2f2ee8d9a05f039d2904cd20f773b0624f664b99b3dabf3d6", "signature": "8dfb57f8d5b3e34550b2122876019c5ac523c0fa0119e23ae248b8ff5938897b"}, {"version": "101098a4acd9c42ec37f8b23fccb5130ef0598de23220fb59adf1d1e4414d5e3", "signature": "c41f4b1bda18183eab865a5c7d45d37f1b93150bd6ac90ae0e5ac942d4773501"}, "e2e6dfe7721c742bb732e759fa09f0cd28c4aa4d74685adb29f91fc8b92ca6d5", {"version": "f4fe958d7c251f4d1e06b004a58e9eda32947ad35eec079505a9350193783770", "signature": "7791fa5db5e035e86197ec51e35f9d1af1f57c64976c43c8b40618ab543dd0f9"}, {"version": "eed3e9b44eefafeeadd059a0b8c708501f172b026d651e9bdf88e34e01b31632", "signature": "f9801e2a65bc7831724e99f6da3fd112738ece819eddff707c7f1bf2ffdb5cce"}, {"version": "6f33e56c54573a2d8ff814e9e6389f1896768dcd8e32ef7c195a0d2ad0ceb9d7", "signature": "70a8158ff1663fc1709672b7816988566f23b063509b1083a9f08145674d7f04"}, {"version": "2612ec16c9c63d28cdc2fe87247aeef222d0926a7d80d9568030452618a6e125", "signature": "ef3faee3e51a19408cdc1edb281d0a9ec7f79a9232331902cde9b8aefc383d30"}, {"version": "c5a94e448c9b54893cdc000fc2fe71393565251791fa5e028274c091bfb448df", "signature": "0e74f04fc7e5d3c389456b94e40ceb90f1ad432c0b74d64d5596663600cdcc44"}, "5f8512650aff5c931e37d2c434baf065ac64c9b35d5ca5e0a24bc88de4c6fecb", "448a3f5def78d7654ed48e217408d21a3b947e901996f910c00fe384cac624de", "52bbe47a3d860596dab651901d8dc3bc9bc56344e587e1a9129cd38d10439715", {"version": "fb8146b6acf32944b11406bd9a89f56611c485112d10306e66503fb9bb9d64b3", "signature": "8d44323f4cb700bbd1cae371d264dabda8b9b463e1ea0181684d6f062a4cc22b"}, "3d342f8a2240029e2f317a3bc8c3aaa45c2624b547d9196bd5f42292e7d6e7d0", {"version": "8641c13b2165fc9bb7019149714355fc60b0c7838c129a03e1a4129a18ee3c1e", "signature": "999d70f9c804405d14ca94ca2c4027b0c044ae4ee98217a22059421f2f4af8d3"}, {"version": "b78589c7e9ac032e5642fffdec4a5c8f703e1ebe5ba39650c000feff33c148cd", "signature": "02c216dec39692c1e874301d536e1d4137366440a344c9e6397710cfa3e40b5e"}, {"version": "d2e0bedb75cadd12895591cadbb723b0815237bb710f1b9da89fed042f7356c8", "signature": "24944d3bef34aebb2a75a979bb25d99698c0ff06a4dda43f9cf9cf9e08e3278e"}, {"version": "55adb5a4bcea4805e658274c0594c194ed87ac4db601e8c277895b6dbdced593", "signature": "0b40458e57edcbcd6660f4c7a122f0e104f609e34e9e6ad95c644d9603aed81f"}, "6f8bf14eaf436704f8b5f01d608ff44dcbe3bb1b5909496aae5dad2f2975b37a", "4f5965c09578fd6353d149c932ffef9e77eb899825a431cc596cebdd16adc2f2", "6569cdabf6aea25c256f6892988e4ea293918f57639a4ef69293bd229f230cdc", {"version": "8c98fc6d9e69c19a58c179dbac24624b3320f7b9cde4c4907a8681313bb315ec", "signature": "426a31e21d1e47353eb8c94b43860535b452a1ae5821d2c4974d7a455d20c4f0"}, {"version": "d8d96f2ee043a692dae23729482cc3d4d5037f23a222410d7f56186460a81cfa", "signature": "61f47972c645e9ef2bb1bfdbfecdb396e5689014aaf940597fa3211ce1db502f"}, "95e620f3e66f27b20232f3860b170e97b709559d73db5f0c003578184666fdf1", "2f9be8077c8e634d3a758aef871a28b029f7e23b63e0409d0f2edbb177d7322b", {"version": "3c308356c6a7d3fb2cafd0978248fcfa44b4cf0c00bb2673664c29549d22d374", "signature": "defa957d98ee50a4865748ed7d77607f1436c87c3d31d03b3d6c9b76ee8bb244"}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "impliedFormat": 1}, "47d4a955185abae52afbb2bda35dfa6eea175662d25c94410cb214d68f93a80c", {"version": "eae0f0bd272650a83a592c6000b7733520eb5aa42efcc8ab62d47dc1acb5ee78", "impliedFormat": 99}, {"version": "0f321818befa1f90aa797afdc64c6cf1652c133eca86d5dd6c99548a8bdaf51e", "impliedFormat": 99}, {"version": "481c19996de65c72ebf9d7e8f9952298072d4c30db6475cd4231df8e2f2d09b1", "impliedFormat": 99}, {"version": "406be199d4f2b0c74810de31b45fecb333d0c04f6275d6e9578067cced0f3b8c", "impliedFormat": 99}, {"version": "2401f5d61e82a35b49f8e89fe5e826682d82273714d86454b5d8ff74838efa7a", "impliedFormat": 99}, {"version": "87ba3ab05e8e23618cd376562d0680ddd0c00a29569ddddb053b9862ef73e159", "impliedFormat": 99}, {"version": "2b4276dde46aa2faf0dd86119999c76b81e6488cd6b0d0fcf9fb985769cd11c0", "impliedFormat": 99}, {"version": "88247402edb737af32da5c7f69ff80e66e831262065b7f0feb32ea8293260d22", "impliedFormat": 99}, {"version": "5ecea63968444d55f7c3cf677cbec9525db9229953b34f06be0386a24b0fffd2", "impliedFormat": 99}, {"version": "b50ee4bde16b52ecb08e2407dca49a5649b38e046e353485335aa024f6efb8ef", "impliedFormat": 99}, {"version": "a3d603c46b55d51493799241b8a456169d36301cc926ff72c75f5480e7eb25bf", "impliedFormat": 99}, {"version": "324869b470cb6aa2bc54e8fb057b90d972f90d24c7059c027869b2587efe01aa", "impliedFormat": 99}, {"version": "eedf3960076a5b33a84cd28476e035983b7c71a9a8728f904d8e17e824259a8e", "impliedFormat": 99}, {"version": "d7058b71aae678b2a276ecbeb7a9f0fdf4d57ccf0831f572686ba43be26b8ef7", "impliedFormat": 99}, {"version": "ed57d309b3d74719526912a9952a1ff72ca38fe0243c51701a49976c771cbb6c", "impliedFormat": 99}, {"version": "9e0b04a9586f6f7bcf2cd160a21630643957553fc49197e8e10d8cca2d163610", "impliedFormat": 99}, {"version": "2df4f080ac546741f1963d7b8a9cc74f739fbdedf8912c0bad34edeb99b64db6", "impliedFormat": 99}, {"version": "4b62ccc8a561ee6f6124dec319721c064456d5888a66a31a5f2691d33aa93a5f", "impliedFormat": 99}, {"version": "430fa8183f4a42a776af25dac202a5e254598ff5b46aa3016165570ea174b09e", "impliedFormat": 99}, {"version": "7cd3e62c5a8cc665104736a6b6d8b360d97ebc9926e2ed98ac23dca8232e210b", "impliedFormat": 99}, {"version": "ff434ea45f1fc18278b1fc25d3269ec58ce110e602ebafba629980543c3d6999", "impliedFormat": 99}, {"version": "d39e6644c8b9854b16e6810f6fc96c2bf044e2fd200da65a17e557c1bac51bc4", "impliedFormat": 99}, {"version": "cd6f4c96cb17765ebc8f0cc96637235385876f1141fa749fc145f29e0932fc2b", "impliedFormat": 99}, {"version": "45ea8224ec8fc3787615fc548677d6bf6d7cec4251f864a6c09fc86dbdb2cd5d", "impliedFormat": 99}, {"version": "3347361f2bf9befc42c807101f43f4d7ea4960294fb8d92a5dbf761d0ca38d71", "impliedFormat": 99}, {"version": "0bbc9eb3b65e320a97c4a1cc8ee5069b86048c4b3dd12ac974c7a1a6d8b6fb36", "impliedFormat": 99}, {"version": "68dc445224378e9b650c322f5753b371cccbeca078e5293cbc54374051d62734", "impliedFormat": 99}, {"version": "93340b1999275b433662eedd4b1195b22f2df3a8eb7e9d1321e5a06c5576417c", "impliedFormat": 99}, {"version": "cbcdb55ee4aafef7154e004b8bf3131550d92e1c2e905b037b87c427a9aa2a0f", "impliedFormat": 99}, {"version": "37fcf5a0823c2344a947d4c0e50cc63316156f1e6bc0f0c6749e099642d286b1", "impliedFormat": 99}, {"version": "2d2f9018356acf6234cd08669a94b67de89f4df559c65bf52c8c7e3d54eea16b", "impliedFormat": 99}, {"version": "1b50e65f1fbcf48850f91b0bc6ff8c61e6fa2e2e64dd2134a087c40fcfa84e28", "impliedFormat": 99}, {"version": "3736846e55c2a2291b0e4b8b0cb875d329b0b190367323f55a5ab58ee9c8406c", "impliedFormat": 99}, {"version": "f86c6ba182a8b3e2042a61b7e4740413ddca1b68ed72d95758355d53dac232d4", "impliedFormat": 99}, {"version": "33aab7e0f4bf0f7c016e98fb8ea1a05b367fedb2785025c7fa628d91f93818cc", "impliedFormat": 99}, {"version": "20cb0921e0f2580cb2878b4379eedab15a7013197a1126a3df34ea7838999039", "impliedFormat": 99}, "791e064fad56d73dc5c6b33473e90fec271ae19b7765d245f876d8586c22d3e2", "942adafab111c439b00821f81607fc7d1b3b193dec2a2724a6ebe23f9c19b19d", {"version": "d51ad0a8cef192d20824441d512e35f1396cd2150bb9a7c4a0194c935cc64ab7", "signature": "5848dd425d303654adf9020712070a6bfb4efaf1304ece19be50d2206c852bd2"}, {"version": "cf67e3ab470da6609f0ad9d6cf944bf85f8f0437ca8abacd2b91539df4d7a4f2", "impliedFormat": 1}, "1223adbe8a47efadb4dd6ed12c4f04bd718b1ae8ef09eb7e5ff405c48cb1c582", "311792e21f18f0c7dcda7e0fd9f8ef320e93d42f0cb91cb137cb24699856756c", {"version": "5c105465ca7bd23bcb636b82596d08b078dc39fb905676a5e031a4589ab6271a", "signature": "4c478ff89eb7fe3136740e707d95a63e8d09b3543500d359ed439d3a0c8f345a"}, {"version": "ad4dd955765808395367b7500eff97163ca3c4708327d32389e7720b5fb5f5fa", "signature": "2e9b0997f276a58290a47b97b765371b28fc76128c80a9864ade662f1470026f"}, {"version": "e5de38d5ee5c990f712d4ff3f06407cb5cf771683d1900fa001948178e73158a", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "b6a47d8bf987d6fa45135262503e6e804e46e1084cf0824dce832591ca18b50b", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "b4d1af143e9a79beda5e2ba3bcc62de51d4da25694db25178a7568664700b5ad", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "3663595501d1e59d444a101adb52ff3a32c41ba3b81391aebc9a695ee48cd15c", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "fe251f335bda3546aaed9ae74916bf2690ec766b4c76f3a84ea755eb7af2a3cb", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "1581b26f083c21556ab565e48741f012eaa30e083b81df9a894bfd905be72523", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "1e6c4ca40a6fd49685292f184cb81ac736e1304f469fd4ea1db6ee69acd45b4b", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "87412a7561090da7f1397e5dd96c5c6c27bf56f6dbd5ea3c635e3b9c1c40542b", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "e93dc745cf2f5bf6d75fca47fe88fa3a304a510191a7e8866621c549a273cd15", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "063a88eeb26fc3167287128bd94cd5e0997bb426b48ce2526f25ab2d09c335cd", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, "501e5a947e00150c11e3c9e2cc6bd6dfd87c58a49ab36811e2ac684981715df5", "0764d671957b9ddd6798b2a22f17802edf65b426b83d1bc72c0320636d643531", {"version": "f931d00d37d70a43cec0d1d93f9ccc4da31cbd40b457de2d1abbad4da812f1bd", "signature": "2cc743b624d6891f9275f11f76fedfe235af04641c806e7dc65e55740db4dd29"}, {"version": "96d14f21b7652903852eef49379d04dbda28c16ed36468f8c9fa08f7c14c9538", "impliedFormat": 1}, {"version": "916be7d770b0ae0406be9486ac12eb9825f21514961dd050594c4b250617d5a8", "impliedFormat": 1}, {"version": "4cf58cd73f135e59d2268b4b792623bd8cc7ea887d96498f2a64d550beb930bb", "impliedFormat": 1}, {"version": "960a68ced7820108787135bdae5265d2cc4b511b7dcfd5b8f213432a8483daf1", "impliedFormat": 1}, {"version": "2e7ebdc7d8af978c263890bbde991e88d6aa31cc29d46735c9c5f45f0a41243b", "impliedFormat": 1}, {"version": "b57fd1c0a680d220e714b76d83eff51a08670f56efcc5d68abc82f5a2684f0c0", "impliedFormat": 1}, {"version": "8cf121e98669f724256d06bebafec912b92bb042a06d4944f7fb27a56c545109", "impliedFormat": 1}, {"version": "1084565c68b2aed5d6d5cea394799bd688afdf4dc99f4e3615957857c15bb231", "impliedFormat": 1}, {"version": "15fe687c59d62741b4494d5e623d497d55eb38966ecf5bea7f36e48fc3fbe15e", "impliedFormat": 1}, {"version": "2c3b8be03577c98530ef9cb1a76e2c812636a871f367e9edf4c5f3ce702b77f8", "affectsGlobalScope": true, "impliedFormat": 1}], "root": [[405, 407], 409, 410, [440, 454], [460, 468], [470, 476], [478, 485], [811, 1004], [1591, 1594], [1598, 1613], [1715, 1720], [1723, 1734], [1777, 1823], 2149, [2186, 2188], [2190, 2206]], "options": {"allowJs": true, "esModuleInterop": true, "jsx": 1, "module": 99, "skipLibCheck": true, "strict": true, "target": 1}, "referencedMap": [[2196, 1], [2197, 2], [2198, 3], [2194, 4], [2199, 5], [2200, 6], [2195, 7], [2201, 8], [2202, 9], [2203, 10], [2204, 11], [2205, 12], [2206, 13], [406, 14], [407, 15], [1606, 16], [1605, 17], [1609, 18], [1610, 18], [1611, 19], [410, 20], [441, 21], [1608, 22], [1612, 23], [1613, 24], [1719, 25], [1720, 26], [1717, 25], [1718, 27], [442, 20], [1715, 28], [1716, 29], [443, 20], [445, 30], [446, 31], [1730, 32], [444, 20], [1723, 33], [1724, 34], [1725, 15], [1726, 15], [1727, 15], [1728, 15], [1729, 15], [1600, 35], [1731, 24], [447, 20], [1734, 36], [1732, 29], [448, 20], [1778, 37], [1779, 38], [1777, 39], [1780, 40], [1781, 29], [1782, 29], [1785, 41], [1783, 29], [1784, 42], [449, 20], [1786, 43], [451, 44], [452, 45], [1788, 46], [1787, 22], [450, 47], [1789, 48], [1790, 49], [1602, 50], [1599, 51], [827, 52], [1791, 24], [828, 53], [825, 54], [824, 55], [823, 56], [475, 57], [476, 58], [468, 59], [460, 60], [474, 61], [833, 62], [831, 63], [829, 64], [832, 65], [1793, 66], [814, 67], [812, 68], [483, 69], [811, 70], [481, 71], [480, 71], [485, 71], [484, 72], [479, 73], [482, 69], [813, 15], [822, 74], [815, 69], [819, 75], [821, 76], [816, 77], [818, 78], [478, 15], [472, 15], [473, 79], [453, 20], [454, 80], [1792, 81], [869, 82], [868, 83], [867, 84], [866, 85], [839, 86], [838, 86], [836, 52], [835, 87], [837, 88], [873, 89], [871, 90], [870, 91], [872, 92], [850, 29], [851, 93], [847, 94], [849, 95], [845, 96], [844, 97], [848, 98], [1794, 97], [842, 99], [846, 94], [859, 15], [864, 100], [863, 29], [861, 101], [862, 102], [860, 103], [874, 104], [840, 15], [841, 15], [843, 105], [826, 20], [834, 106], [1796, 15], [1795, 24], [1797, 107], [1798, 15], [878, 108], [876, 109], [1799, 110], [877, 111], [881, 112], [880, 113], [879, 110], [1800, 114], [865, 20], [875, 115], [1802, 29], [1803, 29], [1801, 24], [1804, 15], [1805, 29], [884, 116], [883, 117], [890, 118], [891, 119], [889, 109], [1807, 120], [1808, 121], [885, 122], [886, 20], [895, 123], [894, 124], [892, 121], [1810, 125], [1811, 126], [1809, 127], [1812, 128], [1813, 129], [1814, 130], [887, 131], [888, 132], [1806, 24], [926, 133], [925, 134], [903, 135], [904, 136], [899, 137], [898, 138], [902, 139], [924, 140], [923, 141], [930, 142], [928, 143], [927, 144], [929, 145], [896, 20], [897, 146], [1815, 147], [916, 67], [914, 148], [910, 149], [911, 150], [913, 151], [908, 152], [907, 152], [912, 152], [906, 153], [909, 149], [915, 15], [922, 154], [921, 149], [919, 155], [920, 156], [917, 157], [918, 158], [905, 15], [900, 15], [901, 159], [963, 160], [962, 161], [961, 162], [960, 163], [938, 164], [939, 165], [934, 166], [933, 167], [937, 168], [967, 169], [965, 170], [964, 171], [966, 172], [1816, 173], [951, 67], [949, 174], [945, 175], [948, 176], [943, 177], [942, 177], [947, 177], [946, 178], [941, 179], [944, 175], [950, 15], [959, 180], [952, 175], [955, 181], [956, 182], [953, 183], [958, 184], [957, 183], [954, 185], [940, 15], [935, 186], [936, 187], [931, 20], [932, 188], [996, 189], [995, 190], [994, 191], [993, 192], [974, 193], [975, 194], [970, 195], [969, 196], [973, 197], [1000, 198], [998, 199], [997, 200], [999, 201], [1817, 202], [986, 67], [985, 203], [981, 204], [984, 205], [979, 206], [978, 206], [983, 206], [982, 207], [977, 208], [980, 204], [893, 15], [992, 209], [987, 204], [990, 210], [991, 211], [988, 212], [989, 213], [976, 15], [971, 15], [972, 214], [882, 20], [968, 215], [817, 20], [830, 20], [1819, 29], [1818, 24], [1821, 216], [1822, 217], [1001, 47], [1823, 29], [2186, 218], [2188, 219], [2187, 218], [2190, 220], [2149, 221], [2191, 222], [1002, 47], [820, 47], [1603, 29], [462, 223], [466, 224], [463, 225], [467, 226], [465, 15], [461, 15], [464, 227], [2192, 228], [1592, 229], [1591, 230], [1820, 29], [470, 109], [2193, 231], [471, 29], [1604, 29], [1733, 232], [1601, 233], [853, 234], [854, 235], [858, 236], [856, 15], [857, 237], [852, 15], [855, 234], [1607, 238], [1004, 239], [1003, 240], [440, 241], [1598, 242], [405, 243], [1013, 244], [1012, 15], [1015, 245], [1014, 246], [1025, 247], [1018, 248], [1026, 249], [1023, 247], [1027, 250], [1021, 247], [1022, 251], [1024, 252], [1020, 253], [1019, 254], [1028, 255], [1016, 256], [1017, 257], [1007, 15], [1008, 258], [1031, 259], [1029, 29], [1030, 260], [1033, 261], [1032, 262], [1010, 263], [1009, 264], [1011, 265], [2157, 266], [2159, 267], [2160, 268], [2161, 269], [2156, 15], [2158, 15], [2152, 270], [2153, 270], [2154, 271], [2164, 272], [2165, 270], [2166, 270], [2167, 273], [2168, 270], [2169, 270], [2170, 270], [2171, 270], [2172, 270], [2163, 270], [2173, 274], [2174, 272], [2175, 275], [2176, 275], [2177, 270], [2178, 276], [2179, 270], [2180, 277], [2181, 270], [2182, 270], [2184, 270], [2155, 15], [2185, 278], [2183, 29], [2162, 279], [2150, 29], [2151, 280], [1824, 29], [1825, 29], [1826, 29], [1827, 29], [1829, 29], [1828, 29], [1830, 29], [1836, 29], [1831, 29], [1833, 29], [1832, 29], [1834, 29], [1835, 29], [1837, 29], [1838, 29], [1841, 29], [1839, 29], [1840, 29], [1842, 29], [1843, 29], [1844, 29], [1845, 29], [1847, 29], [1846, 29], [1848, 29], [1849, 29], [1852, 29], [1850, 29], [1851, 29], [1853, 29], [1854, 29], [1855, 29], [1856, 29], [1879, 29], [1880, 29], [1881, 29], [1882, 29], [1857, 29], [1858, 29], [1859, 29], [1860, 29], [1861, 29], [1862, 29], [1863, 29], [1864, 29], [1865, 29], [1866, 29], [1867, 29], [1868, 29], [1874, 29], [1869, 29], [1871, 29], [1870, 29], [1872, 29], [1873, 29], [1875, 29], [1876, 29], [1877, 29], [1878, 29], [1883, 29], [1884, 29], [1885, 29], [1886, 29], [1887, 29], [1888, 29], [1889, 29], [1890, 29], [1891, 29], [1892, 29], [1893, 29], [1894, 29], [1895, 29], [1896, 29], [1897, 29], [1898, 29], [1899, 29], [1902, 29], [1900, 29], [1901, 29], [1903, 29], [1905, 29], [1904, 29], [1909, 29], [1907, 29], [1908, 29], [1906, 29], [1910, 29], [1911, 29], [1912, 29], [1913, 29], [1914, 29], [1915, 29], [1916, 29], [1917, 29], [1918, 29], [1919, 29], [1920, 29], [1921, 29], [1923, 29], [1922, 29], [1924, 29], [1926, 29], [1925, 29], [1927, 29], [1929, 29], [1928, 29], [1930, 29], [1931, 29], [1932, 29], [1933, 29], [1934, 29], [1935, 29], [1936, 29], [1937, 29], [1938, 29], [1939, 29], [1940, 29], [1941, 29], [1942, 29], [1943, 29], [1944, 29], [1945, 29], [1947, 29], [1946, 29], [1948, 29], [1949, 29], [1950, 29], [1951, 29], [1952, 29], [1954, 29], [1953, 29], [1955, 29], [1956, 29], [1957, 29], [1958, 29], [1959, 29], [1960, 29], [1961, 29], [1963, 29], [1962, 29], [1964, 29], [1965, 29], [1966, 29], [1967, 29], [1968, 29], [1969, 29], [1970, 29], [1971, 29], [1972, 29], [1973, 29], [1974, 29], [1975, 29], [1976, 29], [1977, 29], [1978, 29], [1979, 29], [1980, 29], [1981, 29], [1982, 29], [1983, 29], [1984, 29], [1985, 29], [1990, 29], [1986, 29], [1987, 29], [1988, 29], [1989, 29], [1991, 29], [1992, 29], [1993, 29], [1995, 29], [1994, 29], [1996, 29], [1997, 29], [1998, 29], [1999, 29], [2001, 29], [2000, 29], [2002, 29], [2003, 29], [2004, 29], [2005, 29], [2006, 29], [2007, 29], [2008, 29], [2012, 29], [2009, 29], [2010, 29], [2011, 29], [2013, 29], [2014, 29], [2015, 29], [2017, 29], [2016, 29], [2018, 29], [2019, 29], [2020, 29], [2021, 29], [2022, 29], [2023, 29], [2024, 29], [2025, 29], [2026, 29], [2027, 29], [2028, 29], [2029, 29], [2031, 29], [2030, 29], [2032, 29], [2033, 29], [2035, 29], [2034, 29], [2148, 281], [2036, 29], [2037, 29], [2038, 29], [2039, 29], [2040, 29], [2041, 29], [2043, 29], [2042, 29], [2044, 29], [2045, 29], [2046, 29], [2047, 29], [2050, 29], [2048, 29], [2049, 29], [2052, 29], [2051, 29], [2053, 29], [2054, 29], [2055, 29], [2057, 29], [2056, 29], [2058, 29], [2059, 29], [2060, 29], [2061, 29], [2062, 29], [2063, 29], [2064, 29], [2065, 29], [2066, 29], [2067, 29], [2069, 29], [2068, 29], [2070, 29], [2071, 29], [2072, 29], [2074, 29], [2073, 29], [2075, 29], [2076, 29], [2078, 29], [2077, 29], [2079, 29], [2081, 29], [2080, 29], [2082, 29], [2083, 29], [2084, 29], [2085, 29], [2086, 29], [2087, 29], [2088, 29], [2089, 29], [2090, 29], [2091, 29], [2092, 29], [2093, 29], [2094, 29], [2095, 29], [2096, 29], [2097, 29], [2098, 29], [2100, 29], [2099, 29], [2101, 29], [2102, 29], [2103, 29], [2104, 29], [2105, 29], [2107, 29], [2106, 29], [2108, 29], [2109, 29], [2110, 29], [2111, 29], [2112, 29], [2113, 29], [2114, 29], [2115, 29], [2116, 29], [2117, 29], [2118, 29], [2119, 29], [2120, 29], [2121, 29], [2122, 29], [2123, 29], [2124, 29], [2125, 29], [2126, 29], [2127, 29], [2128, 29], [2129, 29], [2130, 29], [2131, 29], [2134, 29], [2132, 29], [2133, 29], [2135, 29], [2136, 29], [2138, 29], [2137, 29], [2139, 29], [2140, 29], [2141, 29], [2142, 29], [2143, 29], [2145, 29], [2144, 29], [2146, 29], [2147, 29], [486, 29], [487, 29], [488, 29], [489, 29], [491, 29], [490, 29], [492, 29], [498, 29], [493, 29], [495, 29], [494, 29], [496, 29], [497, 29], [499, 29], [500, 29], [503, 29], [501, 29], [502, 29], [504, 29], [505, 29], [506, 29], [507, 29], [509, 29], [508, 29], [510, 29], [511, 29], [514, 29], [512, 29], [513, 29], [515, 29], [516, 29], [517, 29], [518, 29], [541, 29], [542, 29], [543, 29], [544, 29], [519, 29], [520, 29], [521, 29], [522, 29], [523, 29], [524, 29], [525, 29], [526, 29], [527, 29], [528, 29], [529, 29], [530, 29], [536, 29], [531, 29], [533, 29], [532, 29], [534, 29], [535, 29], [537, 29], [538, 29], [539, 29], [540, 29], [545, 29], [546, 29], [547, 29], [548, 29], [549, 29], [550, 29], [551, 29], [552, 29], [553, 29], [554, 29], [555, 29], [556, 29], [557, 29], [558, 29], [559, 29], [560, 29], [561, 29], [564, 29], [562, 29], [563, 29], [565, 29], [567, 29], [566, 29], [571, 29], [569, 29], [570, 29], [568, 29], [572, 29], [573, 29], [574, 29], [575, 29], [576, 29], [577, 29], [578, 29], [579, 29], [580, 29], [581, 29], [582, 29], [583, 29], [585, 29], [584, 29], [586, 29], [588, 29], [587, 29], [589, 29], [591, 29], [590, 29], [592, 29], [593, 29], [594, 29], [595, 29], [596, 29], [597, 29], [598, 29], [599, 29], [600, 29], [601, 29], [602, 29], [603, 29], [604, 29], [605, 29], [606, 29], [607, 29], [609, 29], [608, 29], [610, 29], [611, 29], [612, 29], [613, 29], [614, 29], [616, 29], [615, 29], [617, 29], [618, 29], [619, 29], [620, 29], [621, 29], [622, 29], [623, 29], [625, 29], [624, 29], [626, 29], [627, 29], [628, 29], [629, 29], [630, 29], [631, 29], [632, 29], [633, 29], [634, 29], [635, 29], [636, 29], [637, 29], [638, 29], [639, 29], [640, 29], [641, 29], [642, 29], [643, 29], [644, 29], [645, 29], [646, 29], [647, 29], [652, 29], [648, 29], [649, 29], [650, 29], [651, 29], [653, 29], [654, 29], [655, 29], [657, 29], [656, 29], [658, 29], [659, 29], [660, 29], [661, 29], [663, 29], [662, 29], [664, 29], [665, 29], [666, 29], [667, 29], [668, 29], [669, 29], [670, 29], [674, 29], [671, 29], [672, 29], [673, 29], [675, 29], [676, 29], [677, 29], [679, 29], [678, 29], [680, 29], [681, 29], [682, 29], [683, 29], [684, 29], [685, 29], [686, 29], [687, 29], [688, 29], [689, 29], [690, 29], [691, 29], [693, 29], [692, 29], [694, 29], [695, 29], [697, 29], [696, 29], [810, 282], [698, 29], [699, 29], [700, 29], [701, 29], [702, 29], [703, 29], [705, 29], [704, 29], [706, 29], [707, 29], [708, 29], [709, 29], [712, 29], [710, 29], [711, 29], [714, 29], [713, 29], [715, 29], [716, 29], [717, 29], [719, 29], [718, 29], [720, 29], [721, 29], [722, 29], [723, 29], [724, 29], [725, 29], [726, 29], [727, 29], [728, 29], [729, 29], [731, 29], [730, 29], [732, 29], [733, 29], [734, 29], [736, 29], [735, 29], [737, 29], [738, 29], [740, 29], [739, 29], [741, 29], [743, 29], [742, 29], [744, 29], [745, 29], [746, 29], [747, 29], [748, 29], [749, 29], [750, 29], [751, 29], [752, 29], [753, 29], [754, 29], [755, 29], [756, 29], [757, 29], [758, 29], [759, 29], [760, 29], [762, 29], [761, 29], [763, 29], [764, 29], [765, 29], [766, 29], [767, 29], [769, 29], [768, 29], [770, 29], [771, 29], [772, 29], [773, 29], [774, 29], [775, 29], [776, 29], [777, 29], [778, 29], [779, 29], [780, 29], [781, 29], [782, 29], [783, 29], [784, 29], [785, 29], [786, 29], [787, 29], [788, 29], [789, 29], [790, 29], [791, 29], [792, 29], [793, 29], [796, 29], [794, 29], [795, 29], [797, 29], [798, 29], [800, 29], [799, 29], [801, 29], [802, 29], [803, 29], [804, 29], [805, 29], [807, 29], [806, 29], [808, 29], [809, 29], [1714, 283], [1713, 284], [1317, 285], [1316, 15], [1318, 286], [1311, 287], [1310, 15], [1312, 288], [1314, 289], [1313, 15], [1315, 290], [1320, 291], [1319, 15], [1321, 292], [1171, 293], [1168, 15], [1172, 294], [1177, 295], [1176, 15], [1178, 296], [1180, 297], [1179, 15], [1181, 298], [1214, 299], [1213, 15], [1215, 300], [1217, 301], [1216, 15], [1218, 302], [1220, 303], [1219, 15], [1221, 304], [1227, 305], [1226, 15], [1228, 306], [1230, 307], [1229, 15], [1231, 308], [1236, 309], [1235, 15], [1237, 310], [1233, 311], [1232, 15], [1234, 312], [1239, 313], [1238, 15], [1240, 314], [1247, 315], [1246, 15], [1248, 316], [1160, 317], [1159, 15], [1161, 318], [1158, 319], [1157, 15], [1242, 320], [1244, 29], [1241, 15], [1243, 321], [1245, 322], [1265, 323], [1264, 15], [1266, 324], [1250, 325], [1249, 15], [1251, 326], [1253, 327], [1252, 15], [1254, 328], [1256, 329], [1255, 15], [1257, 330], [1259, 331], [1258, 15], [1260, 332], [1262, 333], [1261, 15], [1263, 334], [1270, 335], [1269, 15], [1271, 336], [1183, 337], [1182, 15], [1184, 338], [1273, 339], [1272, 15], [1274, 340], [1464, 29], [1465, 341], [1276, 342], [1275, 15], [1277, 343], [1279, 344], [1278, 345], [1280, 346], [1281, 347], [1282, 348], [1297, 349], [1296, 15], [1298, 350], [1284, 351], [1283, 15], [1285, 352], [1287, 353], [1286, 15], [1288, 354], [1290, 355], [1289, 15], [1291, 356], [1300, 357], [1299, 15], [1301, 358], [1303, 359], [1302, 15], [1304, 360], [1308, 361], [1307, 15], [1309, 362], [1323, 363], [1322, 15], [1324, 364], [1224, 365], [1225, 366], [1329, 367], [1328, 15], [1330, 368], [1335, 369], [1334, 15], [1336, 370], [1338, 371], [1337, 372], [1332, 373], [1331, 15], [1333, 374], [1340, 375], [1339, 15], [1341, 376], [1343, 377], [1342, 15], [1344, 378], [1346, 379], [1345, 15], [1347, 380], [1351, 381], [1352, 15], [1353, 382], [1349, 383], [1348, 15], [1350, 384], [1355, 385], [1354, 15], [1356, 386], [1163, 387], [1162, 15], [1164, 388], [1358, 389], [1357, 15], [1359, 390], [1364, 391], [1363, 15], [1365, 392], [1361, 393], [1360, 15], [1362, 394], [1374, 395], [1373, 396], [1372, 15], [1368, 397], [1367, 398], [1366, 15], [1327, 399], [1326, 400], [1325, 15], [1371, 401], [1370, 402], [1369, 15], [1156, 403], [1268, 404], [1267, 15], [1377, 405], [1376, 406], [1375, 15], [1380, 407], [1379, 408], [1378, 15], [1401, 409], [1400, 410], [1399, 15], [1389, 411], [1388, 412], [1387, 15], [1383, 413], [1382, 414], [1381, 15], [1392, 415], [1391, 416], [1390, 15], [1386, 417], [1385, 418], [1384, 15], [1395, 419], [1394, 420], [1393, 15], [1398, 421], [1397, 422], [1396, 15], [1404, 423], [1403, 424], [1402, 15], [1415, 425], [1414, 426], [1413, 15], [1407, 427], [1406, 428], [1405, 15], [1409, 429], [1408, 430], [1418, 431], [1417, 432], [1416, 15], [1295, 433], [1294, 434], [1293, 15], [1292, 15], [1422, 435], [1421, 436], [1420, 15], [1419, 437], [1426, 438], [1425, 439], [1424, 15], [1152, 440], [1430, 441], [1429, 442], [1428, 15], [1433, 443], [1432, 444], [1431, 15], [1167, 445], [1166, 446], [1165, 15], [1412, 447], [1411, 448], [1410, 15], [1207, 449], [1210, 450], [1208, 451], [1209, 15], [1205, 452], [1204, 453], [1203, 29], [1441, 454], [1440, 455], [1439, 15], [1438, 456], [1434, 457], [1437, 458], [1435, 29], [1436, 459], [1444, 460], [1443, 461], [1442, 15], [1447, 462], [1446, 463], [1445, 15], [1451, 464], [1450, 465], [1449, 15], [1448, 466], [1454, 467], [1453, 468], [1452, 15], [1306, 469], [1305, 365], [1460, 470], [1459, 471], [1458, 15], [1457, 472], [1456, 15], [1455, 29], [1468, 473], [1467, 474], [1466, 15], [1463, 475], [1462, 476], [1461, 15], [1472, 477], [1471, 478], [1470, 15], [1478, 479], [1477, 480], [1476, 15], [1481, 481], [1480, 482], [1479, 15], [1484, 483], [1482, 484], [1483, 345], [1507, 485], [1505, 486], [1504, 15], [1506, 29], [1487, 487], [1486, 488], [1485, 15], [1490, 489], [1489, 490], [1488, 15], [1493, 491], [1492, 492], [1491, 15], [1496, 493], [1495, 494], [1494, 15], [1499, 495], [1498, 496], [1497, 15], [1503, 497], [1501, 498], [1500, 15], [1502, 29], [1571, 499], [1567, 500], [1572, 501], [1146, 502], [1147, 15], [1573, 15], [1570, 503], [1568, 504], [1569, 505], [1150, 15], [1148, 506], [1582, 507], [1589, 15], [1587, 15], [1006, 15], [1590, 508], [1583, 15], [1565, 509], [1564, 510], [1574, 511], [1579, 15], [1149, 15], [1588, 15], [1578, 15], [1580, 512], [1581, 513], [1586, 514], [1576, 515], [1577, 516], [1566, 517], [1584, 15], [1585, 15], [1151, 15], [1155, 518], [1154, 519], [1153, 15], [1509, 520], [1508, 521], [1512, 522], [1511, 523], [1510, 15], [1548, 524], [1547, 525], [1546, 15], [1536, 526], [1535, 527], [1534, 15], [1515, 528], [1514, 529], [1513, 15], [1518, 530], [1517, 531], [1516, 15], [1521, 532], [1520, 533], [1519, 15], [1545, 534], [1544, 535], [1543, 15], [1524, 536], [1523, 537], [1522, 15], [1533, 538], [1532, 539], [1528, 15], [1527, 540], [1525, 541], [1526, 15], [1539, 542], [1538, 543], [1537, 15], [1542, 544], [1541, 545], [1540, 15], [1554, 546], [1553, 547], [1552, 15], [1551, 548], [1550, 549], [1549, 15], [1557, 550], [1556, 551], [1555, 15], [1560, 552], [1559, 553], [1558, 15], [1563, 554], [1562, 555], [1561, 15], [1531, 556], [1530, 557], [1529, 15], [1475, 558], [1474, 559], [1473, 15], [1469, 560], [1223, 561], [1175, 562], [1174, 563], [1173, 15], [1212, 564], [1211, 565], [1423, 566], [1427, 29], [1206, 565], [1170, 567], [1080, 15], [1085, 568], [1082, 569], [1081, 570], [1084, 571], [1083, 570], [1036, 572], [1037, 573], [1038, 574], [1035, 575], [1034, 29], [1041, 576], [1042, 577], [1090, 578], [1091, 15], [1092, 579], [1058, 580], [1059, 581], [1108, 15], [1109, 582], [1060, 576], [1061, 583], [1130, 584], [1127, 15], [1128, 585], [1129, 586], [1131, 587], [1093, 588], [1094, 589], [1043, 590], [1575, 591], [1095, 592], [1096, 593], [1053, 594], [1045, 15], [1056, 595], [1057, 596], [1044, 15], [1054, 591], [1055, 597], [1066, 576], [1067, 598], [1117, 599], [1120, 600], [1123, 15], [1124, 15], [1121, 15], [1122, 601], [1115, 15], [1118, 15], [1119, 15], [1116, 602], [1062, 576], [1063, 603], [1064, 576], [1065, 604], [1078, 15], [1079, 605], [1086, 606], [1087, 607], [1134, 608], [1133, 609], [1135, 15], [1137, 610], [1132, 611], [1138, 612], [1136, 591], [1145, 613], [1114, 614], [1113, 29], [1112, 594], [1069, 615], [1068, 576], [1071, 616], [1070, 576], [1126, 617], [1125, 15], [1073, 618], [1072, 576], [1075, 619], [1074, 576], [1089, 620], [1088, 576], [1141, 621], [1143, 622], [1140, 623], [1142, 15], [1139, 611], [1040, 624], [1039, 594], [1098, 625], [1097, 626], [1047, 627], [1051, 576], [1050, 628], [1052, 629], [1048, 630], [1046, 630], [1049, 631], [1111, 632], [1110, 633], [1077, 634], [1076, 576], [1107, 635], [1106, 15], [1103, 636], [1102, 637], [1100, 15], [1101, 638], [1099, 15], [1105, 639], [1104, 15], [1144, 15], [1005, 29], [1169, 29], [358, 15], [1202, 640], [1198, 641], [1185, 15], [1201, 642], [1194, 643], [1192, 644], [1191, 644], [1190, 643], [1187, 644], [1188, 643], [1196, 645], [1189, 644], [1186, 643], [1193, 644], [1199, 646], [1200, 647], [1195, 648], [1197, 644], [1739, 649], [1745, 650], [1747, 651], [1740, 652], [1748, 653], [1746, 654], [1749, 15], [1741, 655], [1742, 653], [1750, 656], [1751, 649], [1754, 657], [1743, 658], [1752, 659], [1753, 660], [1744, 661], [417, 662], [413, 663], [420, 664], [415, 665], [416, 15], [418, 662], [414, 665], [411, 15], [419, 665], [412, 15], [433, 666], [439, 667], [430, 668], [438, 29], [431, 666], [432, 669], [423, 668], [421, 670], [437, 671], [434, 670], [436, 668], [435, 670], [429, 670], [428, 670], [422, 668], [424, 672], [426, 668], [427, 668], [425, 668], [1735, 15], [2207, 15], [1736, 673], [136, 674], [137, 674], [138, 675], [97, 676], [139, 677], [140, 678], [141, 679], [92, 15], [95, 680], [93, 15], [94, 15], [142, 681], [143, 682], [144, 683], [145, 684], [146, 685], [147, 686], [148, 686], [150, 15], [149, 687], [151, 688], [152, 689], [153, 690], [135, 691], [96, 15], [154, 692], [155, 693], [156, 694], [188, 695], [157, 696], [158, 697], [159, 698], [160, 699], [161, 700], [162, 701], [163, 702], [164, 703], [165, 704], [166, 705], [167, 705], [168, 706], [169, 15], [170, 707], [172, 708], [171, 709], [173, 710], [174, 711], [175, 712], [176, 713], [177, 714], [178, 715], [179, 716], [180, 717], [181, 718], [182, 719], [183, 720], [184, 721], [185, 722], [186, 723], [187, 724], [2208, 15], [84, 15], [2189, 725], [2209, 15], [193, 726], [194, 727], [192, 29], [2210, 15], [2211, 561], [2214, 728], [2212, 29], [1222, 29], [2213, 561], [190, 729], [191, 730], [82, 15], [85, 731], [281, 29], [2216, 732], [2215, 15], [408, 15], [83, 15], [1721, 630], [469, 29], [91, 733], [361, 734], [365, 735], [367, 736], [214, 737], [228, 738], [332, 739], [260, 15], [335, 740], [296, 741], [305, 742], [333, 743], [215, 744], [259, 15], [261, 745], [334, 746], [235, 747], [216, 748], [240, 747], [229, 747], [199, 747], [287, 749], [288, 750], [204, 15], [284, 751], [289, 669], [376, 752], [282, 669], [377, 753], [266, 15], [285, 754], [389, 755], [388, 756], [291, 669], [387, 15], [385, 15], [386, 757], [286, 29], [273, 758], [274, 759], [283, 760], [300, 761], [301, 762], [290, 763], [268, 764], [269, 765], [380, 766], [383, 767], [247, 768], [246, 769], [245, 770], [392, 29], [244, 771], [220, 15], [395, 15], [1596, 772], [1595, 15], [398, 15], [397, 29], [399, 773], [195, 15], [326, 15], [227, 774], [197, 775], [349, 15], [350, 15], [352, 15], [355, 776], [351, 15], [353, 777], [354, 777], [213, 15], [226, 15], [360, 778], [368, 779], [372, 780], [209, 781], [276, 782], [275, 15], [267, 764], [295, 783], [293, 784], [292, 15], [294, 15], [299, 785], [271, 786], [208, 787], [233, 788], [323, 789], [200, 790], [207, 791], [196, 739], [337, 792], [347, 793], [336, 15], [346, 794], [234, 15], [218, 795], [314, 796], [313, 15], [320, 797], [322, 798], [315, 799], [319, 800], [321, 797], [318, 799], [317, 797], [316, 799], [256, 801], [241, 801], [308, 802], [242, 802], [202, 803], [201, 15], [312, 804], [311, 805], [310, 806], [309, 807], [203, 808], [280, 809], [297, 810], [279, 811], [304, 812], [306, 813], [303, 811], [236, 808], [189, 15], [324, 814], [262, 815], [298, 15], [345, 816], [265, 817], [340, 818], [206, 15], [341, 819], [343, 820], [344, 821], [327, 15], [339, 790], [238, 822], [325, 823], [348, 824], [210, 15], [212, 15], [217, 825], [307, 826], [205, 827], [211, 15], [264, 828], [263, 829], [219, 830], [272, 831], [270, 832], [221, 833], [223, 834], [396, 15], [222, 835], [224, 836], [363, 15], [362, 15], [364, 15], [394, 15], [225, 837], [278, 29], [90, 15], [302, 838], [248, 15], [258, 839], [237, 15], [370, 29], [379, 840], [255, 29], [374, 669], [254, 841], [357, 842], [253, 840], [198, 15], [381, 843], [251, 29], [252, 29], [243, 15], [257, 15], [250, 844], [249, 845], [239, 846], [232, 763], [342, 15], [231, 847], [230, 15], [366, 15], [277, 29], [359, 848], [81, 15], [89, 849], [86, 29], [87, 15], [88, 15], [338, 850], [331, 851], [330, 15], [329, 852], [328, 15], [369, 853], [371, 854], [373, 855], [1597, 856], [375, 857], [378, 858], [404, 859], [382, 859], [403, 860], [384, 861], [390, 862], [391, 863], [393, 864], [400, 865], [402, 15], [401, 866], [356, 867], [1614, 15], [1629, 868], [1630, 868], [1643, 869], [1631, 870], [1632, 870], [1633, 871], [1627, 872], [1625, 873], [1616, 15], [1620, 874], [1624, 875], [1622, 876], [1628, 877], [1617, 878], [1618, 879], [1619, 880], [1621, 881], [1623, 882], [1626, 883], [1634, 870], [1635, 870], [1636, 870], [1637, 868], [1638, 870], [1639, 870], [1615, 870], [1640, 15], [1642, 884], [1641, 870], [1722, 885], [459, 886], [456, 29], [457, 29], [455, 15], [458, 887], [1738, 652], [1755, 888], [1756, 888], [1758, 889], [1759, 890], [1737, 649], [1760, 888], [1776, 891], [1757, 888], [1761, 652], [1762, 652], [1763, 888], [1764, 29], [1765, 888], [1766, 892], [1767, 888], [1768, 888], [1769, 652], [1770, 888], [1771, 888], [1772, 888], [1773, 888], [1774, 888], [1775, 652], [477, 29], [79, 15], [80, 15], [13, 15], [14, 15], [16, 15], [15, 15], [2, 15], [17, 15], [18, 15], [19, 15], [20, 15], [21, 15], [22, 15], [23, 15], [24, 15], [3, 15], [25, 15], [26, 15], [4, 15], [27, 15], [31, 15], [28, 15], [29, 15], [30, 15], [32, 15], [33, 15], [34, 15], [5, 15], [35, 15], [36, 15], [37, 15], [38, 15], [6, 15], [42, 15], [39, 15], [40, 15], [41, 15], [43, 15], [7, 15], [44, 15], [49, 15], [50, 15], [45, 15], [46, 15], [47, 15], [48, 15], [8, 15], [54, 15], [51, 15], [52, 15], [53, 15], [55, 15], [9, 15], [56, 15], [57, 15], [58, 15], [60, 15], [59, 15], [61, 15], [62, 15], [10, 15], [63, 15], [64, 15], [65, 15], [11, 15], [66, 15], [67, 15], [68, 15], [69, 15], [70, 15], [1, 15], [71, 15], [72, 15], [12, 15], [76, 15], [74, 15], [78, 15], [73, 15], [77, 15], [75, 15], [113, 893], [123, 894], [112, 893], [133, 895], [104, 896], [103, 897], [132, 866], [126, 898], [131, 899], [106, 900], [120, 901], [105, 902], [129, 903], [101, 904], [100, 866], [130, 905], [102, 906], [107, 907], [108, 15], [111, 907], [98, 15], [134, 908], [124, 909], [115, 910], [116, 911], [118, 912], [114, 913], [117, 914], [127, 866], [109, 915], [110, 916], [119, 917], [99, 918], [122, 909], [121, 907], [125, 15], [128, 919], [1657, 920], [1648, 921], [1655, 922], [1650, 15], [1651, 15], [1649, 923], [1652, 924], [1644, 15], [1645, 15], [1656, 925], [1647, 926], [1653, 15], [1654, 927], [1646, 928], [1709, 929], [1662, 930], [1664, 931], [1707, 15], [1663, 932], [1708, 933], [1712, 934], [1710, 15], [1665, 930], [1666, 15], [1706, 935], [1661, 936], [1658, 15], [1711, 937], [1659, 938], [1660, 15], [1667, 939], [1668, 939], [1669, 939], [1670, 939], [1671, 939], [1672, 939], [1673, 939], [1674, 939], [1675, 939], [1676, 939], [1678, 939], [1677, 939], [1679, 939], [1680, 939], [1681, 939], [1705, 940], [1682, 939], [1683, 939], [1684, 939], [1685, 939], [1686, 939], [1687, 939], [1688, 939], [1689, 939], [1690, 939], [1692, 939], [1691, 939], [1693, 939], [1694, 939], [1695, 939], [1696, 939], [1697, 939], [1698, 939], [1699, 939], [1700, 939], [1701, 939], [1702, 939], [1703, 939], [1704, 939], [1593, 15], [409, 15], [1594, 15]], "semanticDiagnosticsPerFile": [[409, [{"start": 1728, "length": 32, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type '\"Authorization\"' can't be used to index type 'HeadersInit'.", "category": 1, "code": 7053, "next": [{"messageText": "Property 'Authorization' does not exist on type 'HeadersInit'.", "category": 1, "code": 2339}]}}]], [440, [{"start": 1237, "length": 14, "messageText": "Cannot find name 'SecurityStatus'.", "category": 1, "code": 2304}, {"start": 19389, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'getSecurityStatus' is missing in type '{ user: User | null; token: string | null; refreshToken: string | null; loading: boolean; login: (user: User, token: string, refreshTokenValue: string) => void; logout: () => Promise<...>; refreshAccessToken: () => Promise<boolean>; fetchAndPersistWarehouseInfo: (userObjParam?: User, tokenParam?: string | null) => P...' but required in type 'AuthContextType'.", "relatedInformation": [{"start": 1204, "length": 17, "messageText": "'getSecurityStatus' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/@types/react/index.d.ts", "start": 21275, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & ProviderProps<AuthContextType | undefined>'", "category": 3, "code": 6500}], "canonicalHead": {"code": 2322, "messageText": "Type '{ user: User | null; token: string | null; refreshToken: string | null; loading: boolean; login: (user: User, token: string, refreshTokenValue: string) => void; logout: () => Promise<...>; refreshAccessToken: () => Promise<boolean>; fetchAndPersistWarehouseInfo: (userObjParam?: User, tokenParam?: string | null) => P...' is not assignable to type 'AuthContextType'."}}]], [441, [{"start": 996, "length": 7, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(options: DefinedInitialDataOptions<unknown, Error, unknown, (string | undefined)[]>, queryClient?: QueryClient | undefined): DefinedUseQueryResult<unknown, Error>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'boolean | \"\" | null' is not assignable to type 'Enabled<unknown, Error, unknown, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'Enabled<unknown, Error, unknown, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 3, '(options: UndefinedInitialDataOptions<DashboardStats, Error, DashboardStats, (string | undefined)[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'boolean | \"\" | null' is not assignable to type 'Enabled<DashboardStats, Error, DashboardStats, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'Enabled<DashboardStats, Error, DashboardStats, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 3 of 3, '(options: UseQueryOptions<DashboardStats, Error, DashboardStats, (string | undefined)[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'boolean | \"\" | null' is not assignable to type 'Enabled<DashboardStats, Error, DashboardStats, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'Enabled<DashboardStats, Error, DashboardStats, (string | undefined)[]> | undefined'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": [{"file": "./node_modules/@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "start": 36392, "length": 7, "messageText": "The expected type comes from property 'enabled' which is declared here on type 'DefinedInitialDataOptions<unknown, Error, unknown, (string | undefined)[]>'", "category": 3, "code": 6500}, {"file": "./node_modules/@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "start": 36392, "length": 7, "messageText": "The expected type comes from property 'enabled' which is declared here on type 'UndefinedInitialDataOptions<DashboardStats, Error, DashboardStats, (string | undefined)[]>'", "category": 3, "code": 6500}, {"file": "./node_modules/@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "start": 36392, "length": 7, "messageText": "The expected type comes from property 'enabled' which is declared here on type 'UseQueryOptions<DashboardStats, Error, DashboardStats, (string | undefined)[]>'", "category": 3, "code": 6500}]}, {"start": 1677, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'products' does not exist on type '{}'."}, {"start": 1848, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'warehouses' does not exist on type '{}'."}, {"start": 2139, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'securityEvents' does not exist on type '{}'."}]], [446, [{"start": 4606, "length": 103, "messageText": "Expected 1-3 arguments, but got 4.", "category": 1, "code": 2554}, {"start": 5516, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'meta' does not exist on type 'never'."}, {"start": 5633, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'meta' does not exist on type 'never'."}, {"start": 5756, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'meta' does not exist on type 'never'."}]], [451, [{"start": 642, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 1776, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 2925, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 4290, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 5627, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 10009, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 12266, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 13406, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 14558, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 15691, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 16863, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 19038, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}]], [452, [{"start": 636, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"file": "./app/logs/components/entitydisplayconfig.ts", "start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 2132, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"file": "./app/logs/components/entitydisplayconfig.ts", "start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}, {"start": 3646, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(log: Log) => string | undefined' is not assignable to type '(log: Log) => string'."}}]}, "relatedInformation": [{"file": "./app/logs/components/entitydisplayconfig.ts", "start": 209, "length": 14, "messageText": "The expected type comes from property 'getDescription' which is declared here on type 'EntityDisplayConfig'", "category": 3, "code": 6500}]}]], [472, [{"start": 1628, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}, {"start": 3870, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}]], [473, [{"start": 39, "length": 8, "messageText": "Module '\"../types\"' has no exported member 'Customer'.", "category": 1, "code": 2305}]], [818, [{"start": 295, "length": 30, "messageText": "Cannot find module '../../customers/customersApi' or its corresponding type declarations.", "category": 1, "code": 2307}]], [819, [{"start": 5767, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'customer' does not exist on type 'Sale'."}, {"start": 6471, "length": 23, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/purchase/orders/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 7031, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'creditLimit' does not exist in type 'CreateCustomerDto'."}, {"start": 7311, "length": 37, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/purchase/orders/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 7831, "length": 27, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/purchase/orders/pos/posApi\").Customer[]' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2678, "messageText": "Type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/purchase/orders/pos/posApi\").Customer' is not comparable to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer'."}}]}}]], [823, [{"start": 13376, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [831, [{"start": 276, "length": 30, "messageText": "Cannot find module '../../customers/customersApi' or its corresponding type declarations.", "category": 1, "code": 2307}]], [857, [{"start": 2264, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./components/suppliermodal/hooks/usesupplierdata.ts", "start": 469, "length": 13, "messageText": "The expected type comes from property 'warehouseUuid' which is declared here on type '{ page: number; limit: number; search?: string | undefined; warehouseUuid?: string | undefined; }'", "category": 3, "code": 6500}]}, {"start": 3621, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./components/suppliermodal/hooks/usesupplierdata.ts", "start": 469, "length": 13, "messageText": "The expected type comes from property 'warehouseUuid' which is declared here on type '{ page: number; limit: number; search?: string | undefined; warehouseUuid?: string | undefined; }'", "category": 3, "code": 6500}]}, {"start": 4273, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./components/suppliermodal/hooks/usesupplierdata.ts", "start": 469, "length": 13, "messageText": "The expected type comes from property 'warehouseUuid' which is declared here on type '{ page: number; limit: number; search?: string | undefined; warehouseUuid?: string | undefined; }'", "category": 3, "code": 6500}]}]], [860, [{"start": 7165, "length": 8, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ purchaseUuid: string; originalPurchaseUuid: string; mode: string; items: PurchaseItem[]; supplier: any; supplierUuid: any; paymentMethod: any; amountPaid: any; taxEnabled: any; ... 9 more ...; error: string | null; }' is not assignable to parameter of type 'SetStateAction<POSPurchaseState>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ purchaseUuid: string; originalPurchaseUuid: string; mode: string; items: PurchaseItem[]; supplier: any; supplierUuid: any; paymentMethod: any; amountPaid: any; taxEnabled: any; ... 9 more ...; error: string | null; }' is not assignable to type 'POSPurchaseState'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'mode' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"new\" | \"edit\" | \"load\"'.", "category": 1, "code": 2322, "canonicalHead": {"code": 2322, "messageText": "Type '{ purchaseUuid: string; originalPurchaseUuid: string; mode: string; items: PurchaseItem[]; supplier: any; supplierUuid: any; paymentMethod: any; amountPaid: any; taxEnabled: any; ... 9 more ...; error: string | null; }' is not assignable to type 'POSPurchaseState'."}}]}]}]}}]], [861, [{"start": 4300, "length": 21, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ warehouseUuid: string | null | undefined; name: string; fiscalId?: string; email?: string; phone?: string; address?: string; rc?: string; articleNumber?: string; }' is not assignable to parameter of type 'CreateSupplierDto'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'warehouseUuid' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}]}]}}]], [866, [{"start": 20356, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onClose: () => void; onSelect: (supplier: Supplier | null) => void | null; onNewSupplier: (supplierData: CreateSupplierDto) => Promise<void>; searchSuppliers: any; getDefaultSupplier: any; }' is not assignable to type 'IntrinsicAttributes & SupplierModalProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'onNewSupplier' does not exist on type 'IntrinsicAttributes & SupplierModalProps'.", "category": 1, "code": 2339}]}}]], [874, [{"start": 4087, "length": 4, "code": 2740, "category": 1, "messageText": "Type 'PaginatedResponseDto<Supplier>' is missing the following properties from type 'Supplier[]': length, pop, push, concat, and 35 more.", "relatedInformation": [{"start": 3978, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ data: Supplier[]; }'", "category": 3, "code": 6500}], "canonicalHead": {"code": 2322, "messageText": "Type 'PaginatedResponseDto<Supplier>' is not assignable to type 'Supplier[]'."}}]], [894, [{"start": 1954, "length": 31, "code": 2345, "category": 1, "messageText": "Argument of type '{ customerUuid: string; }' is not assignable to parameter of type 'string'."}]], [900, [{"start": 1634, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}, {"start": 3876, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}]], [901, [{"start": 39, "length": 8, "messageText": "Module '\"../types\"' has no exported member 'Customer'.", "category": 1, "code": 2305}]], [902, [{"start": 2646, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'balanceDue' does not exist on type 'Order'."}, {"start": 2770, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'balanceDue' does not exist on type 'Order'."}]], [918, [{"start": 14537, "length": 11, "messageText": "'OrderStatus' refers to a value, but is being used as a type here. Did you mean 'typeof OrderStatus'?", "category": 1, "code": 2749}]], [919, [{"start": 3180, "length": 13, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'paymentMethod' does not exist in type 'UpdateOrderDto'."}, {"start": 3892, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'amountPaid' does not exist on type 'UpdateOrderDto'."}, {"start": 5926, "length": 13, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'paymentMethod' does not exist in type 'CreateOrderDto'."}, {"start": 6541, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'customerUuidString' does not exist on type 'Order'."}, {"start": 6760, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'customerUuidString' does not exist on type 'Order'."}, {"start": 6819, "length": 18, "code": 2741, "category": 1, "messageText": "Property 'rc' is missing in type 'Customer' but required in type '{ uuid: string; name: string; fiscalId: string; rc: string; articleNumber?: string | undefined; }'.", "relatedInformation": [{"file": "./app/sales/orders/ordersapi.ts", "start": 2134, "length": 2, "messageText": "'rc' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type 'Customer' is not assignable to type '{ uuid: string; name: string; fiscalId: string; rc: string; articleNumber?: string | undefined; }'."}}, {"start": 7535, "length": 23, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/orders/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 8095, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'creditLimit' does not exist in type 'CreateCustomerDto'."}, {"start": 8375, "length": 37, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/orders/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 8895, "length": 27, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/orders/pos/posApi\").Customer[]' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2678, "messageText": "Type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/orders/pos/posApi\").Customer' is not comparable to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer'."}}]}}]], [922, [{"start": 500, "length": 27, "messageText": "'\"./useKeyboardNavigation\"' has no exported member named 'UseKeyboardNavigationReturn'. Did you mean 'useKeyboardNavigation'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./app/sales/orders/pos/hooks/usekeyboardnavigation.ts", "start": 375, "length": 21, "messageText": "'useKeyboardNavigation' is declared here.", "category": 3, "code": 2728}]}]], [923, [{"start": 13485, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 15044, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'customerUuidString' does not exist on type 'Order'."}]], [928, [{"start": 4584, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'customerUuidString' does not exist on type 'Order'."}]], [936, [{"start": 39, "length": 8, "messageText": "Module '\"../types\"' declares 'Customer' locally, but it is not exported.", "category": 1, "code": 2459, "relatedInformation": [{"file": "./app/sales/quotes/pos/types/index.ts", "start": 46, "length": 8, "messageText": "'Customer' is declared here.", "category": 3, "code": 2728}]}, {"start": 58, "length": 8, "messageText": "Module '\"../types\"' has no exported member 'SaleItem'.", "category": 1, "code": 2305}, {"start": 68, "length": 4, "messageText": "Module '\"../types\"' has no exported member 'Sale'.", "category": 1, "code": 2305}]], [938, [{"start": 4262, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'itemsSnapshot' does not exist on type 'Quote'."}, {"start": 4285, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'itemsSnapshot' does not exist on type 'Quote'."}, {"start": 5281, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'itemsSnapshot' does not exist on type 'Quote'."}]], [939, [{"start": 867, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'invoiceNumber' does not exist on type 'Quote'."}]], [946, [{"start": 275, "length": 14, "messageText": "Module '\"../types\"' has no exported member 'SalesCartProps'.", "category": 1, "code": 2305}, {"start": 5563, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6690, "length": 13, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 6733, "length": 21, "messageText": "Cannot find name 'onPaymentMethodChange'. Did you mean 'PaymentMethodChangeEvent'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'onPaymentMethodChange'."}, "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 785557, "length": 24, "messageText": "'PaymentMethodChangeEvent' is declared here.", "category": 3, "code": 2728}]}, {"start": 6806, "length": 10, "messageText": "Cannot find name 'amountPaid'.", "category": 1, "code": 2304}, {"start": 6850, "length": 18, "messageText": "Cannot find name 'onAmountPaidChange'.", "category": 1, "code": 2304}]], [954, [{"start": 14650, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'itemsSnapshot' does not exist on type 'Quote'."}, {"start": 14669, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [955, [{"start": 199, "length": 12, "messageText": "Module '\"./usePOSState\"' has no exported member 'POSSaleState'.", "category": 1, "code": 2305}, {"start": 1234, "length": 23, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/quotes/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 1794, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'creditLimit' does not exist in type 'CreateCustomerDto'."}, {"start": 2074, "length": 37, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/quotes/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 2594, "length": 27, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/quotes/pos/posApi\").Customer[]' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2678, "messageText": "Type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/quotes/pos/posApi\").Customer' is not comparable to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer'."}}]}}]], [959, [{"start": 531, "length": 12, "messageText": "Module '\"./usePOSState\"' has no exported member 'POSSaleState'.", "category": 1, "code": 2305}]], [960, [{"start": 13379, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 14939, "length": 18, "code": 2551, "category": 1, "messageText": "Property 'customerUuidString' does not exist on type 'Quote'. Did you mean 'customerUuid'?", "relatedInformation": [{"file": "./app/sales/quotes/quotesapi.ts", "start": 239, "length": 12, "messageText": "'customerUuid' is declared here.", "category": 3, "code": 2728}]}]], [971, [{"start": 1628, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}, {"start": 3870, "length": 8, "messageText": "Cannot find name 'Customer'.", "category": 1, "code": 2304}]], [972, [{"start": 39, "length": 8, "messageText": "Module '\"../types\"' has no exported member 'Customer'.", "category": 1, "code": 2305}]], [989, [{"start": 14604, "length": 13, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'PaymentMethods | undefined'.", "relatedInformation": [{"file": "./app/sales/sales/salesapi.ts", "start": 2452, "length": 13, "messageText": "The expected type comes from property 'paymentMethod' which is declared here on type 'UpdateSaleDto'", "category": 3, "code": 6500}]}]], [990, [{"start": 411, "length": 13, "messageText": "Cannot find module '../salesApi' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 6741, "length": 8, "code": 2551, "category": 1, "messageText": "Property 'customer' does not exist on type 'Sale'. Did you mean 'customerRc'?", "relatedInformation": [{"file": "./app/sales/sales/salesapi.ts", "start": 345, "length": 10, "messageText": "'customerRc' is declared here.", "category": 3, "code": 2728}]}, {"start": 7445, "length": 23, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/sales/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 8005, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'creditLimit' does not exist in type 'CreateCustomerDto'."}, {"start": 8285, "length": 37, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/sales/pos/posApi\").Customer' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739}]}}, {"start": 8805, "length": 27, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/sales/pos/posApi\").Customer[]' to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'Customer' is missing the following properties from type 'Customer': customerType, isDeleted, createdAt, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2678, "messageText": "Type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/app/sales/sales/pos/posApi\").Customer' is not comparable to type 'import(\"C:/Users/<USER>/Documents/workspace/projects/dido-distribution/frontend/components/CustomerModal/types/index\").Customer'."}}]}}]], [993, [{"start": 13376, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [998, [{"start": 6766, "length": 9, "code": 2345, "category": 1, "messageText": "Argument of type 'string' is not assignable to parameter of type 'CancelSaleDto'."}]], [1599, [{"start": 634, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'onError' does not exist in type 'OmitKeyof<QueryObserverOptions<unknown, Error, unknown, unknown, readonly unknown[], never>, \"suspense\" | \"queryKey\", \"strictly\">'."}]], [1606, [{"start": 2788, "length": 11, "messageText": "'healthError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6086, "length": 10, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ uuid: any; email: any; firstName: any; lastName: any; phone: null; isActive: boolean; roles: never[]; warehouseUuid: any; warehouseUuidString: any; vanUuid: any; createdAt: string; updatedAt: string; name: any; userType: any; warehouseName: null; }' is not assignable to parameter of type 'User'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'warehouseName' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}]}}, {"start": 6976, "length": 10, "messageText": "'fetchError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 7450, "length": 13, "messageText": "'fallbackError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 11354, "length": 3, "messageText": "'err' is of type 'unknown'.", "category": 1, "code": 18046}]], [1611, [{"start": 37, "length": 13, "messageText": "Module '\"../dashboardApi\"' has no exported member 'SecurityEvent'.", "category": 1, "code": 2305}]], [1612, [{"start": 2033, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'displayName' does not exist on type 'User'."}, {"start": 5325, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'securityEvents' does not exist on type '{}'."}, {"start": 7364, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'products' does not exist on type '{}'."}, {"start": 7397, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'warehouses' does not exist on type '{}'."}]], [1718, [{"start": 12140, "length": 11, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ warehouseUuid: string; userUuid: string; name: string; description?: string | undefined; price?: string | undefined; sku?: string | undefined; productCategoryUuid?: string | undefined; ... 5 more ...; institutionalPrice?: string | undefined; }' is not assignable to parameter of type 'Partial<Product>'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'price' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'number | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}]}}, {"start": 12197, "length": 11, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ warehouseUuid: string; userUuid: string; name: string; description?: string | undefined; price?: string | undefined; sku?: string | undefined; productCategoryUuid?: string | undefined; ... 5 more ...; institutionalPrice?: string | undefined; }' is not assignable to parameter of type 'Omit<Product, \"uuid\" | \"createdAt\" | \"updatedAt\" | \"isDeleted\">'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'price' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'number | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}]}}]], [1719, [{"start": 989, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"start": 1023, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}]], [1723, [{"start": 1870, "length": 18, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 12068, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'never'."}]], [1724, [{"start": 3010, "length": 18, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 7192, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'AdjustmentForm' is not assignable to parameter of type '{ storageUuid: string; adjustments: { productUuid: string; adjustment: number; reason: string; }[]; }'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'adjustments' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'AdjustmentRow[]' is not assignable to type '{ productUuid: string; adjustment: number; reason: string; }[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AdjustmentRow' is not assignable to type '{ productUuid: string; adjustment: number; reason: string; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'reason' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type 'AdjustmentRow' is not assignable to type '{ productUuid: string; adjustment: number; reason: string; }'."}}]}]}]}]}]}}]], [1732, [{"start": 1241, "length": 18, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'."}, {"start": 1536, "length": 19, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'."}, {"start": 2096, "length": 22, "messageText": "This comparison appears to be unintentional because the types 'number' and 'string' have no overlap.", "category": 1, "code": 2367}, {"start": 2259, "length": 23, "messageText": "This comparison appears to be unintentional because the types 'number' and 'string' have no overlap.", "category": 1, "code": 2367}]], [1780, [{"start": 4862, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'info' does not exist on type '{ (message: Message, opts?: Partial<Pick<Toast, \"style\" | \"className\" | \"id\" | \"icon\" | \"position\" | \"duration\" | \"ariaProps\" | \"iconTheme\" | \"removeDelay\">> | undefined): string; ... 6 more ...; promise<T>(promise: Promise<...> | (() => Promise<...>), msgs: { ...; }, opts?: DefaultToastOptions | undefined): Promise...'."}, {"start": 5034, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'info' does not exist on type '{ (message: Message, opts?: Partial<Pick<Toast, \"style\" | \"className\" | \"id\" | \"icon\" | \"position\" | \"duration\" | \"ariaProps\" | \"iconTheme\" | \"removeDelay\">> | undefined): string; ... 6 more ...; promise<T>(promise: Promise<...> | (() => Promise<...>), msgs: { ...; }, opts?: DefaultToastOptions | undefined): Promise...'."}, {"start": 5189, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'info' does not exist on type '{ (message: Message, opts?: Partial<Pick<Toast, \"style\" | \"className\" | \"id\" | \"icon\" | \"position\" | \"duration\" | \"ariaProps\" | \"iconTheme\" | \"removeDelay\">> | undefined): string; ... 6 more ...; promise<T>(promise: Promise<...> | (() => Promise<...>), msgs: { ...; }, opts?: DefaultToastOptions | undefined): Promise...'."}, {"start": 5539, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'info' does not exist on type '{ (message: Message, opts?: Partial<Pick<Toast, \"style\" | \"className\" | \"id\" | \"icon\" | \"position\" | \"duration\" | \"ariaProps\" | \"iconTheme\" | \"removeDelay\">> | undefined): string; ... 6 more ...; promise<T>(promise: Promise<...> | (() => Promise<...>), msgs: { ...; }, opts?: DefaultToastOptions | undefined): Promise...'."}, {"start": 12725, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(data: any) => Promise<void>' is not assignable to type '(data: ComputeRouteDto) => Promise<Route>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'Promise<void>' is not assignable to type 'Promise<Route>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'void' is not assignable to type 'Route'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '(data: any) => Promise<void>' is not assignable to type '(data: ComputeRouteDto) => Promise<Route>'."}}]}, "relatedInformation": [{"file": "./app/logistics/routes/components/computeroutemodal.tsx", "start": 375, "length": 14, "messageText": "The expected type comes from property 'onComputeRoute' which is declared here on type 'IntrinsicAttributes & ComputeRouteModalProps'", "category": 3, "code": 6500}]}]], [1786, [{"start": 126, "length": 10, "messageText": "Module '\"@/components/itemsTable/ItemsTable\"' has no exported member 'ItemsTable'. Did you mean to use 'import ItemsTable from \"@/components/itemsTable/ItemsTable\"' instead?", "category": 1, "code": 2614}, {"start": 1060, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'description' does not exist on type 'Warehouse'."}]], [1793, [{"start": 7085, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ sale: Sale; companyInfo: CompanyInfo; customerInfo: CustomerInfo; isOpen: true; onClose: () => void; }' is not assignable to type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'sale' does not exist on type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2339}]}}]], [1800, [{"start": 5629, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./app/purchasing/suppliers/components/suppliermodal.tsx", "start": 362, "length": 13, "messageText": "The expected type comes from property 'warehouseUuid' which is declared here on type 'IntrinsicAttributes & SupplierModalProps'", "category": 3, "code": 6500}]}]], [1809, [{"start": 6494, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./app/sales/customers/customermodal.tsx", "start": 644, "length": 13, "messageText": "The expected type comes from property 'warehouseUuid' which is declared here on type 'IntrinsicAttributes & CustomerModalProps'", "category": 3, "code": 6500}]}]], [1812, [{"start": 308, "length": 14, "messageText": "Module '\"../customerPaymentsApi\"' has no exported member 'formatCurrency'.", "category": 1, "code": 2305}]], [1813, [{"start": 737, "length": 14, "messageText": "Module '\"../customerPaymentsApi\"' has no exported member 'formatCurrency'.", "category": 1, "code": 2305}]], [1814, [{"start": 633, "length": 14, "messageText": "Module '\"../customerPaymentsApi\"' has no exported member 'formatCurrency'.", "category": 1, "code": 2305}]], [1815, [{"start": 7184, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ order: Order; companyInfo: CompanyInfo; customerInfo: CustomerInfo; isOpen: true; onClose: () => void; }' is not assignable to type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'order' does not exist on type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2339}]}}]], [1816, [{"start": 7184, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ quote: Quote; companyInfo: CompanyInfo; customerInfo: CustomerInfo; isOpen: true; onClose: () => void; }' is not assignable to type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'quote' does not exist on type 'IntrinsicAttributes & InvoicePrintProps'.", "category": 1, "code": 2339}]}}]], [1821, [{"start": 2323, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'AccountSettings | null' is not assignable to parameter of type 'SetStateAction<any[]>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'SetStateAction<any[]>'.", "category": 1, "code": 2322}]}}, {"start": 6815, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'AccountSettings | null' is not assignable to parameter of type 'SetStateAction<any[]>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'SetStateAction<any[]>'.", "category": 1, "code": 2322}]}}]], [1822, [{"start": 385, "length": 10, "messageText": "Module '\"./rolesApi\"' has no exported member 'createRole'.", "category": 1, "code": 2305}, {"start": 400, "length": 8, "messageText": "Module '\"./rolesApi\"' has no exported member 'editRole'.", "category": 1, "code": 2305}, {"start": 437, "length": 10, "messageText": "Module '\"./rolesApi\"' has no exported member 'Permission'.", "category": 1, "code": 2305}, {"start": 3665, "length": 26, "code": 2345, "category": 1, "messageText": "Argument of type '{ warehouseUuid: string; name: string; permissions: string[]; }' is not assignable to parameter of type 'void'."}]], [2193, [{"start": 9765, "length": 12, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ columns: ({ key: string; header: string; cellClassName: string; headerClassName: string; render?: undefined; } | { key: string; header: string; cellClassName: string; render: (value: string) => Element; headerClassName?: undefined; } | { ...; } | { ...; } | { ...; })[]; ... 4 more ...; containerClassName: string; }' is not assignable to type 'IntrinsicAttributes & ItemsTableProps<DemoItem>'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'skeletonRows' does not exist on type 'IntrinsicAttributes & ItemsTableProps<DemoItem>'.", "category": 1, "code": 2339}]}}]]], "affectedFilesPendingEmit": [2196, 2197, 2198, 2194, 2199, 2200, 2195, 2201, 2202, 2203, 2204, 2205, 2206, 406, 407, 1606, 1605, 1609, 1610, 1611, 410, 441, 1608, 1612, 1613, 1719, 1720, 1717, 1718, 442, 1715, 1716, 443, 445, 446, 1730, 444, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1600, 1731, 447, 1734, 1732, 448, 1778, 1779, 1777, 1780, 1781, 1782, 1785, 1783, 1784, 449, 1786, 451, 452, 1788, 1787, 450, 1789, 1790, 1602, 1599, 827, 1791, 828, 825, 824, 823, 475, 476, 468, 460, 474, 833, 831, 829, 832, 1793, 814, 812, 483, 811, 481, 480, 485, 484, 479, 482, 813, 822, 815, 819, 821, 816, 818, 478, 472, 473, 453, 454, 1792, 869, 868, 867, 866, 839, 838, 836, 835, 837, 873, 871, 870, 872, 850, 851, 847, 849, 845, 844, 848, 1794, 842, 846, 859, 864, 863, 861, 862, 860, 874, 840, 841, 843, 826, 834, 1796, 1795, 1797, 1798, 878, 876, 1799, 877, 881, 880, 879, 1800, 865, 875, 1802, 1803, 1801, 1804, 1805, 884, 883, 890, 891, 889, 1807, 1808, 885, 886, 895, 894, 892, 1810, 1811, 1809, 1812, 1813, 1814, 887, 888, 1806, 926, 925, 903, 904, 899, 898, 902, 924, 923, 930, 928, 927, 929, 896, 897, 1815, 916, 914, 910, 911, 913, 908, 907, 912, 906, 909, 915, 922, 921, 919, 920, 917, 918, 905, 900, 901, 963, 962, 961, 960, 938, 939, 934, 933, 937, 967, 965, 964, 966, 1816, 951, 949, 945, 948, 943, 942, 947, 946, 941, 944, 950, 959, 952, 955, 956, 953, 958, 957, 954, 940, 935, 936, 931, 932, 996, 995, 994, 993, 974, 975, 970, 969, 973, 1000, 998, 997, 999, 1817, 986, 985, 981, 984, 979, 978, 983, 982, 977, 980, 893, 992, 987, 990, 991, 988, 989, 976, 971, 972, 882, 968, 817, 830, 1819, 1818, 1821, 1822, 1001, 1823, 2186, 2188, 2187, 2190, 2149, 2191, 1002, 820, 1603, 462, 466, 463, 467, 465, 461, 464, 2192, 1592, 1591, 1820, 470, 2193, 471, 1604, 1733, 1601, 853, 854, 858, 856, 857, 852, 855, 1607, 1004, 1003, 440, 1598, 1593, 409, 1594], "version": "5.8.3"}