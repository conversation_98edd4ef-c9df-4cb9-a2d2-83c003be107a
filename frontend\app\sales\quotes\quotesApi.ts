// quotesApi.ts - API utilities for Quotes endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/quotes';

export interface Quote {
  uuid: string;
  quoteNumber: string;
  customerUuid: string;
  customerName?: string;
  customerFiscalId?: string;
  customerRc?: string;
  customerArticleNumber?: string;
  warehouseUuid: string;
  warehouseName?: string;
  subtotal: number;
  useTax: boolean;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired' | 'cancelled';
  quoteDate: string;
  expiryDate: string;
  notes?: string;
  convertedToOrderUuid?: string | null;
  createdBy: string;
  createdByName: string;
  updatedBy: string;
  updatedByName: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  quoteItems: Array<{
    uuid: string;
    quoteUuid: string;
    productUuid: string;
    name: string;
    quantity: number;
    unitPrice: number;
    lineTotal: number;
    createdAt: string;
    updatedAt: string;
  }>;
}

// Quote item interface for creating/updating quotes
export interface QuoteItem {
  productUuid: string;
  name: string;
  quantity: number;
  unitPrice: number;
  lineTotal?: number;
}

// Create quote DTO
export interface CreateQuoteDto {
  userUuid: string;
  warehouseUuid: string;
  customerUuid: string;
  items: QuoteItem[];
  useTax: boolean;
  taxRate: number;
  status?: string;
  quoteDate?: string;
  expiryDate?: string;
  notes?: string;
}

// Update quote DTO
export interface UpdateQuoteDto {
  userUuid: string;
  customerUuid?: string;
  items?: QuoteItem[];
  useTax?: boolean;
  taxRate?: number;
  status?: string;
  quoteDate?: string;
  expiryDate?: string;
  notes?: string;
}

// Add items to quote DTO
export interface AddItemsToQuoteDto {
  userUuid: string;
  items: QuoteItem[];
}

// Quote status enum matching backend
export enum QuoteStatus {
  DRAFT = "draft",
  SENT = "sent",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  EXPIRED = "expired",
  CANCELLED = "cancelled",
}

// Filter DTO matching backend API
export interface FilterQuoteDto {
  warehouseUuid?: string;
  customerUuid?: string;
  status?: QuoteStatus;
  quoteNumber?: string;
  createdFrom?: string;
  createdTo?: string;
  quoteDateFrom?: string;
  quoteDateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  useTax?: boolean;
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface QuotesResponse {
  quotes: Quote[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Get all quotes with optional filtering and pagination
 */
export async function getQuotes(pagination?: PaginationQueryDto, filter?: FilterQuoteDto): Promise<QuotesResponse> {
  const params = new URLSearchParams();
  
  // Add pagination parameters with validation
  if (pagination?.page && !isNaN(pagination.page) && pagination.page > 0) {
    params.append('page', pagination.page.toString());
  }
  if (pagination?.limit && !isNaN(pagination.limit) && pagination.limit > 0 && pagination.limit <= 100) {
    params.append('limit', pagination.limit.toString());
  }
  
  // Helper function to validate and add string parameters
  const addStringParam = (key: string, value: string | undefined) => {
    if (value && typeof value === 'string' && value.trim() !== '' && value.trim() !== 'undefined' && value.trim() !== 'null') {
      params.append(key, value.trim());
    }
  };
  
  // Add filter parameters with validation - matching backend API exactly
  addStringParam('warehouseUuid', filter?.warehouseUuid);
  addStringParam('customerUuid', filter?.customerUuid);
  addStringParam('status', filter?.status);
  addStringParam('quoteNumber', filter?.quoteNumber);
  addStringParam('createdFrom', filter?.createdFrom);
  addStringParam('createdTo', filter?.createdTo);
  addStringParam('quoteDateFrom', filter?.quoteDateFrom);
  addStringParam('quoteDateTo', filter?.quoteDateTo);
  
  // Validate and add numeric filters
  if (filter?.minAmount !== undefined && filter.minAmount !== null && !isNaN(filter.minAmount) && filter.minAmount >= 0) {
    params.append('minAmount', filter.minAmount.toString());
  }
  if (filter?.maxAmount !== undefined && filter.maxAmount !== null && !isNaN(filter.maxAmount) && filter.maxAmount >= 0) {
    params.append('maxAmount', filter.maxAmount.toString());
  }
  
  // Add boolean filter
  if (filter?.useTax !== undefined) {
    params.append('useTax', filter.useTax.toString());
  }

  const url = `${API_BASE}?${params.toString()}`;
  const headers = getAxiosAuthHeaders();
  
  try {
    const res = await axios.get(url, {
      headers,
    });
    return res.data;
  } catch (error: any) {
    throw error;
  }
}

/**
 * Get a single quote by UUID
 */
export async function getQuote(uuid: string): Promise<Quote> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new quote
 */
export async function createQuote(createQuoteDto: CreateQuoteDto): Promise<Quote> {
  const res = await axios.post(API_BASE, createQuoteDto, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update an existing quote
 */
export async function updateQuote(uuid: string, updateQuoteDto: UpdateQuoteDto): Promise<Quote> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, updateQuoteDto, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Add items to an existing quote
 */
export async function addItemsToQuote(uuid: string, addItemsDto: AddItemsToQuoteDto): Promise<Quote> {
  const res = await axios.post(`${API_BASE}/${uuid}/add-items`, addItemsDto, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update quote status
 */
export async function updateQuoteStatus(uuid: string, status: string, userUuid: string): Promise<Quote> {
  const res = await axios.patch(`${API_BASE}/${uuid}/status`, { status, userUuid }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Cancel a quote
 */
export async function cancelQuote(uuid: string, userUuid: string): Promise<Quote> {
  const res = await axios.patch(`${API_BASE}/${uuid}/status`, { status: 'cancelled', userUuid }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete a quote
 */
export async function deleteQuote(uuid: string, userUuid: string): Promise<{ message: string }> {
  const res = await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
    data: { userUuid }
  });
  return res.data;
}
