// DTO utility for Product output, following UUID_USAGE_GUIDELINES.md
import { Product } from "../product.entity";

export interface ProductDto {
  uuid: string; // from virtual
  warehouseUuidString?: string; // from virtual
  productCategoryUuidString?: string; // from virtual
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  retailPrice: number;
  wholesalePrice: number;
  midWholesalePrice: number;
  institutionalPrice: number;
  cost?: number;
  isDeleted: boolean;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  
  // Pack information fields
  isPack?: boolean; // true if this product is an assembly (has components)
  isComponent?: boolean; // true if this product is part of a pack
  packInfo?: {
    packUuid?: string; // UUID of the pack this product belongs to
    packName?: string; // Name of the pack this product belongs to
    packSku?: string; // SKU of the pack this product belongs to
    quantityInPack?: number; // How many of this product are in the pack
  };
  componentCount?: number; // Number of components in this pack (if isPack is true)
  // Add other fields as needed, but do NOT include _id, warehouseUuid (Buffer), or any Buffer fields
}

export function toProductDto(product: any): ProductDto {
  // Accepts a TypeORM entity or plain object
  return {
    uuid: product.id, // TypeORM uses 'id' instead of virtual 'uuid'
    warehouseUuidString: product.warehouseUuid, // Direct UUID string in TypeORM
    productCategoryUuidString: product.productCategoryUuid, // Direct UUID string in TypeORM
    name: product.name,
    description: product.description ?? "",
    sku: product.sku ?? "",
    barcode: product.barcode ?? "",
    retailPrice: product.retailPrice,
    wholesalePrice: product.wholesalePrice,
    midWholesalePrice: product.midWholesalePrice,
    institutionalPrice: product.institutionalPrice,
    cost: product.cost ?? null,
    isDeleted: product.isDeleted,
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
    
    // Pack information
    isPack: product.isPack ?? false,
    isComponent: product.isComponent ?? false,
    packInfo: product.packInfo ? {
      packUuid: product.packInfo.packUuid,
      packName: product.packInfo.packName,
      packSku: product.packInfo.packSku,
      quantityInPack: product.packInfo.quantityInPack,
    } : undefined,
    componentCount: product.componentCount ?? 0,
  };
}

export function toProductDtoArray(products: any[]): ProductDto[] {
  return products.map(toProductDto);
}
