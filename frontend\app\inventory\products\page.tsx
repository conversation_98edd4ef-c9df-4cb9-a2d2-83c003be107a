"use client";
import React from "react";
import ItemsTable from "@/components/itemsTable/ItemsTable";
import TableActionButtons from "@/components/itemsTable/TableActionButtons";
import ProductForm, { ProductFormValues, PackComponent } from "./ProductForm";
import { useAuth } from "../../../contexts/AuthContext";
import { createProductStructure } from "./productStructureApi";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { filterProducts, deleteProduct, createProduct, updateProduct, Product, PaginatedResponseDto, getProductCategories, getProductsByWarehouse } from "./productsApi";
import { getAllProductCategoriesRaw, ProductCategory } from "./productCategoriesApi";

/**
 * Products Page - Updated to use latest backend endpoints with Product Pack Features
 * 
 * CHANGES MADE:
 * - Updated productsApi.ts to use unified /filter endpoint (replaces old /by-warehouse, /categories, /pricing, /search endpoints)
 * - Simplified API calls to use single filterProducts function
 * - Maintained backward compatibility with existing function names
 * - Fixed useAuth hook usage in handleSubmit function
 * - FIXED: Updated search to use 'search' parameter instead of 'name' to enable SKU and barcode searching
 * 
 * NEW PRODUCT PACK FEATURES ADDED:
 * - Product Structure Management: Create, edit, delete pack relationships
 * - Bill of Materials (BOM): View components that make up a product pack
 * - Where Used Analysis: Find all assemblies that use a specific component
 * - Pack Type Indicators: Visual indicators showing if a product is a pack, component, or both
 * - Pack Management Button: Quick access to manage pack relationships for each product
 * - Optimized API calls: Limited pack info fetching to first 10 products for performance
 * 
 * POTENTIAL ISSUES TO MONITOR:
 * - Verify that the unified /filter endpoint returns the expected data structure
 * - Check that category filtering works correctly with the new endpoint
 * - Ensure pagination works properly with the new response format
 * - Monitor for any performance issues with the unified endpoint
 * - VERIFIED: Search now works across name, SKU, and barcode fields
 * - Monitor pack info API calls for performance impact
 * 
 * MISSING ENDPOINTS (if any issues arise):
 * - All old endpoints have been replaced by the unified /filter endpoint
 * - If specific functionality is missing, it should be added to the backend /filter endpoint
 * - Current implementation supports: filtering, search, customer pricing, category listing
 * - Product structure endpoints are now fully integrated
 */



// Modal component with Escape-to-close support
const Modal: React.FC<{ open: boolean; onClose: () => void; title: string; children: React.ReactNode }> = ({ open, onClose, title, children }) => {
  React.useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);
  if (!open) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/40 backdrop-blur-sm" role="dialog" aria-modal="true">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-sm p-0 relative border border-gray-200">
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-600 mt-1">
              {title === "Add Product" ? "Create a new product for your inventory" : "Update product information"}
            </p>
          </div>
          <button
            onClick={onClose}
            aria-label="Close"
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-400 transition"
            tabIndex={0}
          >
            <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
        <div className="px-6 py-6">{children}</div>
      </div>
    </div>
  );
};

export default function ProductsPage() {
  React.useEffect(() => {
    const handleF2 = (e: KeyboardEvent) => {
      if (e.key === "F2") {
        e.preventDefault();
        handleAdd();
      }
    };
    window.addEventListener("keydown", handleF2);
    return () => window.removeEventListener("keydown", handleF2);
  }, []);
  
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid || "";

  const [search, setSearch] = React.useState("");
  const [selectedCategory, setSelectedCategory] = React.useState<string>("");
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showAdditionalPrices, setShowAdditionalPrices] = React.useState(false);
  
  // Store previous pagination data to preserve it during loading
  const [previousPaginationData, setPreviousPaginationData] = React.useState<{
    total: number;
    page: number;
  } | null>(null);

  // DEBUG: Log warehouse UUID and filter states
  React.useEffect(() => {
    console.log('[ProductsPage] Debug Info:', {
      warehouseUuid,
      search,
      selectedCategory,
      currentPage,
      pageSize
    });
  }, [warehouseUuid, search, selectedCategory, currentPage, pageSize]);

  const { data: paginatedData, isLoading, isError, refetch } = useQuery<PaginatedResponseDto<Product>>({
    queryKey: ["products", warehouseUuid, search, selectedCategory, currentPage, pageSize],
    queryFn: async () => {
      try {
        console.log('[ProductsPage] Fetching products with filters:', {
          warehouseUuid,
          search: search.trim(),
          selectedCategory,
          currentPage,
          pageSize
        });

        // Use unified filtering endpoint for better performance and consistency
        const filters = {
          search: search.trim() || undefined,
          productCategoryUuid: selectedCategory || undefined,
        };

        console.log('[ProductsPage] Unified filter options:', filters);

        const result = await filterProducts(
          warehouseUuid,
          filters,
          { page: currentPage, limit: pageSize }
        );

        console.log('[ProductsPage] API Response:', {
          totalProducts: result.total,
          returnedProducts: result.data.length,
          page: result.page,
          limit: result.limit,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
          searchTerm: search.trim() || 'none'
        });

        // DEBUG: Log category UUIDs in returned products
        if (result.data.length > 0) {
          console.log('[ProductsPage] Sample product category UUIDs:', 
            result.data.slice(0, 3).map(p => ({
              name: p.name,
              productCategoryUuid: p.productCategoryUuid,
              productCategoryUuidString: p.productCategoryUuidString
            }))
          );
          
          // DEBUG: Check if any products have the selected category
          if (selectedCategory) {
            const productsWithSelectedCategory = result.data.filter(p => 
              p.productCategoryUuid === selectedCategory || p.productCategoryUuidString === selectedCategory
            );
            console.log('[ProductsPage] Products with selected category:', {
              selectedCategory,
              totalProducts: result.data.length,
              productsWithSelectedCategory: productsWithSelectedCategory.length,
              matchingProducts: productsWithSelectedCategory.map(p => ({
                name: p.name,
                productCategoryUuid: p.productCategoryUuid,
                productCategoryUuidString: p.productCategoryUuidString
              }))
            });
          }
        }

        return result;
      } catch (error) {
        console.error('[ProductsPage] API Error:', error);
        throw error;
      }
    },
    enabled: !!warehouseUuid,
  });

  // Fetch product categories for display
  const { data: categories = [] } = useQuery<ProductCategory[]>({
    queryKey: ['productCategories', warehouseUuid],
    queryFn: async () => {
      const cats = await getAllProductCategoriesRaw(warehouseUuid);
      console.log('[ProductsPage] Fetched categories:', cats.map(c => ({ uuid: c.uuid, name: c.name })));
      
      // DEBUG: Check if any categories have the same UUID as selectedCategory
      if (selectedCategory) {
        const matchingCategory = cats.find(c => c.uuid === selectedCategory);
        console.log('[ProductsPage] Category filter debug:', {
          selectedCategory,
          matchingCategory: matchingCategory ? { uuid: matchingCategory.uuid, name: matchingCategory.name } : null,
          allCategoryUuids: cats.map(c => c.uuid)
        });
      }
      
      return cats;
    },
    enabled: !!warehouseUuid,
  });

  // DEBUG: Log pagination data changes and preserve pagination metadata
  React.useEffect(() => {
    console.log('[ProductsPage] Pagination data changed:', {
      paginatedData: paginatedData ? {
        page: paginatedData.page,
        total: paginatedData.total,
        dataLength: paginatedData.data.length,
        totalPages: Math.ceil((paginatedData.total || 0) / pageSize)
      } : null,
      previousPaginationData,
      isLoading,
      currentPage,
      pageSize
    });
    
    // Preserve pagination metadata when new data arrives
    if (paginatedData && !isLoading) {
      setPreviousPaginationData({
        total: paginatedData.total,
        page: paginatedData.page
      });
    }
  }, [paginatedData, isLoading, currentPage, pageSize]);

  // DEBUG: Log whenever selectedCategory changes
  React.useEffect(() => {
    if (selectedCategory) {
      const matchingCategory = categories.find(c => c.uuid === selectedCategory);
      console.log('[ProductsPage] Selected category changed:', {
        selectedCategory,
        matchingCategory: matchingCategory ? { uuid: matchingCategory.uuid, name: matchingCategory.name } : null,
        totalCategories: categories.length
      });
    } else {
      console.log('[ProductsPage] Selected category cleared');
    }
  }, [selectedCategory, categories]);

  const [modalOpen, setModalOpen] = React.useState(false);
  const [editing, setEditing] = React.useState<Product | null>(null);

  const [error, setError] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(false);

  // Fetch pack information for products (optimized to avoid too many API calls)
  const { data: packInfo, isLoading: packInfoLoading } = useQuery({
    queryKey: ['packInfo', warehouseUuid, paginatedData?.data?.map(p => p.uuid).join(',')],
    queryFn: async () => {
      if (!paginatedData?.data || paginatedData.data.length === 0) return {};
      
      const packData: Record<string, { isPack: boolean; isComponent: boolean }> = {};
      
      // Only fetch pack info for the first 10 products to avoid performance issues
      const productsToCheck = paginatedData.data.slice(0, 10);
      
      // Check each product to see if it's a pack or component
      for (const product of productsToCheck) {
        try {
          const [bomData, whereUsedData] = await Promise.all([
            getBillOfMaterials(product.uuid),
            getWhereUsed(product.uuid)
          ]);
          
          packData[product.uuid] = {
            isPack: bomData.components && bomData.components.length > 0,
            isComponent: whereUsedData.assemblies && whereUsedData.assemblies.length > 0
          };
        } catch (error) {
          // If API call fails, assume it's not a pack/component
          packData[product.uuid] = { isPack: false, isComponent: false };
        }
      }
      
      return packData;
    },
    enabled: !!paginatedData?.data && paginatedData.data.length > 0,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const deleteMutation = useMutation({
    mutationFn: async (uuid: string) => {
      await deleteProduct(uuid);
    },
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["products"] }),
  });

  const handleAdd = () => {
    setEditing(null);
    setModalOpen(true);
    setError(null);
  };

  const handleEdit = (product: Product) => {
    setEditing(product);
    setModalOpen(true);
    setError(null);
  };

  const handleDelete = (product: Product) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      deleteMutation.mutate(product.uuid);
    }
  };



  const handleClose = () => {
    setModalOpen(false);
    setEditing(null);
    setError(null);
  };

  const handlePageChange = (page: number) => {
    console.log('[ProductsPage] Page change requested:', {
      from: currentPage,
      to: page,
      paginatedData: paginatedData ? {
        page: paginatedData.page,
        total: paginatedData.total,
        totalPages: Math.ceil((paginatedData.total || 0) / pageSize)
      } : null,
      previousPaginationData,
      willPreserveData: !paginatedData && previousPaginationData
    });
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    console.log('[ProductsPage] Search input changed:', {
      from: search,
      to: value,
      willSearch: value.trim() !== ''
    });
    setSearch(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleCategoryChange = (categoryUuid: string) => {
    setSelectedCategory(categoryUuid);
    setCurrentPage(1); // Reset to first page when changing category
  };

  const handleSubmit = async (values: ProductFormValues, packComponents?: PackComponent[]) => {
    setLoading(true);
    setError(null);
    
    try {
      // Use warehouseUuid and userUuid from the component scope (already available from useAuth above)
      if (!warehouseUuid || !user?.uuid) {
        throw new Error("Missing warehouse or user context");
      }

      const productData = {
        ...values,
        warehouseUuid,
        userUuid: user.uuid,
      };

      if (editing) {
        await updateProduct(editing.uuid, productData);
      } else {
        const createdProduct = await createProduct(productData);
        
        // If this is a pack and has components, create the product structures
        if (values.isPack && packComponents && packComponents.length > 0) {
          console.log('[Products Page] Creating pack structures for:', packComponents);
          for (const component of packComponents) {
            await createProductStructure({
              warehouseUuid,
              assemblyProductUuid: createdProduct.uuid,
              componentProductUuid: component.productUuid,
              quantity: component.quantity,
            });
          }
        }
      }

      queryClient.invalidateQueries({ queryKey: ["products"] });
      handleClose();
    } catch (err: any) {
      console.error("Error saving product:", err);
      setError(err.response?.data?.message || err.message || "Failed to save product");
    } finally {
      setLoading(false);
    }
  };

  const getCategoryName = (categoryUuid: string | undefined) => {
    if (!categoryUuid) return "No Category";
    const category = categories.find(c => c.uuid === categoryUuid);
    return category?.name || "Unknown Category";
  };

  const getCategoryUuid = (product: Product) => {
    return product.productCategoryUuid || product.productCategoryUuidString;
  };

  return (
    <div className="p-4 sm:p-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">Products</h1>
          <p className="text-sm text-gray-600 mt-1">
            {categories.length} categories available • Manage product packs and components
          </p>
        </div>
        <div className="flex gap-2">
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            onClick={handleAdd}
          >
            Add Product (F2)
          </button>
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
        <input
          type="text"
          placeholder="Search by name, SKU, or barcode..."
          className="border rounded px-4 py-3 w-full sm:w-96 text-base"
          value={search}
          onChange={e => handleSearchChange(e.target.value)}
        />
        <select
          value={selectedCategory}
          onChange={e => handleCategoryChange(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Categories</option>
          {categories.map((category) => (
            <option key={category.uuid} value={category.uuid}>
              {category.name}
            </option>
          ))}
        </select>
        <select
          value={pageSize}
          onChange={e => {
            setPageSize(Number(e.target.value));
            setCurrentPage(1);
          }}
          className="border rounded px-3 py-2"
        >
          <option value={10}>10 per page</option>
          <option value={25}>25 per page</option>
          <option value={50}>50 per page</option>
          <option value={100}>100 per page</option>
        </select>
        <label className="flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer">
          <input
            type="checkbox"
            checked={showAdditionalPrices}
            onChange={e => setShowAdditionalPrices(e.target.checked)}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          Show Customer Pricing
        </label>
      </div>
      
      {error && <div className="text-red-600 mb-2">{error}</div>}
      {isLoading && <div>Loading...</div>}
      {isError && <div className="text-red-600">Failed to load products.</div>}
      
      {paginatedData && (
        <div className="mb-4 text-sm text-gray-600 flex items-center justify-between">
          <span>Showing {((paginatedData.page - 1) * pageSize) + 1}-{Math.min(paginatedData.page * pageSize, paginatedData.total)} of {paginatedData.total} products</span>
          {selectedCategory && (
            <div className="flex items-center gap-2">
              <span>Filtered by:</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {getCategoryName(selectedCategory)}
              </span>
              <button
                onClick={() => handleCategoryChange("")}
                className="text-gray-400 hover:text-gray-600"
                title="Clear filter"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}
        </div>
      )}
      
      <div className="overflow-x-auto py-2 max-w-7xl mx-auto w-full">
        {/* DEBUG: Log pagination values being passed */}
        {(() => {
          const paginationValues = {
            currentPage: paginatedData?.page || currentPage,
            totalPages: Math.ceil(((paginatedData?.total || previousPaginationData?.total || 0)) / pageSize),
            totalItems: paginatedData?.total || previousPaginationData?.total || 0,
            itemsPerPage: pageSize,
          };
          console.log('[ProductsPage] Pagination values being passed to ItemsTable:', paginationValues);
          return null;
        })()}
        <ItemsTable
          columns={[
            { 
              key: "name", 
              header: "Name", 
              cellClassName: "text-gray-900 font-medium",
              render: (value, product) => {
                const productPackInfo = packInfo?.[product.uuid];
                return (
                  <div>
                    <div className="font-medium text-gray-900">{value}</div>
                    {productPackInfo && (productPackInfo.isPack || productPackInfo.isComponent) && (
                      <div className="flex items-center gap-1 mt-1">
                        {productPackInfo.isPack && (
                          <span className="inline-flex items-center text-xs text-slate-500">
                            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            pack
                          </span>
                        )}
                        {productPackInfo.isComponent && (
                          <span className="inline-flex items-center text-xs text-slate-500">
                            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            component
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                );
              }
            },
            { key: "sku", header: "SKU" },
            { key: "barcode", header: "Barcode" },
            { 
              key: "category", 
              header: "Category",
              render: (value, product) => {
                const categoryUuid = getCategoryUuid(product);
                const categoryName = getCategoryName(categoryUuid);
                return categoryName ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {categoryName}
                  </span>
                ) : (
                  <span className="text-gray-400 text-sm">No category</span>
                );
              }
            },

            { key: "cost", header: "Cost", render: (value) => value != null ? `$${Number(value).toFixed(2)}` : "" },
            // Conditionally include additional price columns
            ...(showAdditionalPrices ? [
              { key: "retailPrice", header: "Retail Price", render: (value: any) => value != null ? `$${Number(value).toFixed(2)}` : "-" },
              { key: "wholesalePrice", header: "Wholesale Price", render: (value: any) => value != null ? `$${Number(value).toFixed(2)}` : "-" },
              { key: "midWholesalePrice", header: "Mid-Wholesale Price", render: (value: any) => value != null ? `$${Number(value).toFixed(2)}` : "-" },
              { key: "institutionalPrice", header: "Institutional Price", render: (value: any) => value != null ? `$${Number(value).toFixed(2)}` : "-" },
            ] : []),
            {
              key: "actions",
              header: <span className="block text-center">Actions</span>,
              cellClassName: "text-center",
              render: (_: any, product: Product) => (
                <div className="flex items-center justify-center gap-1">
                  <TableActionButtons
                    onEdit={() => handleEdit(product)}
                    onDelete={() => handleDelete(product)}
                    editDisabled={false}
                    deleteDisabled={deleteMutation.isPending}
                  />

                </div>
              ),
            },
          ]}
          data={paginatedData?.data || []}
          isLoading={isLoading}
          noDataText="No products found."
          containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
          tableBodyHeight="600px"
          pagination={{
            currentPage: paginatedData?.page || currentPage,
            totalPages: Math.ceil(((paginatedData?.total || previousPaginationData?.total || 0)) / pageSize),
            onPageChange: handlePageChange,
            totalItems: paginatedData?.total || previousPaginationData?.total || 0,
            itemsPerPage: pageSize,
          }}
        />
      </div>
      
      <Modal open={modalOpen} onClose={handleClose} title={editing ? "Edit Product" : "Add Product"}>
        <ProductForm
          initialValues={editing ? {
            ...editing,
            // Fixed: Use productCategoryUuid instead of productCategoryUuidString
            productCategoryUuid: editing.productCategoryUuid || editing.productCategoryUuidString || '',
            // Handle simple price vs custom pricing initialization
            price: (!showAdditionalPrices && editing.retailPrice) ? editing.retailPrice.toString() : '',
            // Convert number price fields to strings for form
            cost: editing.cost?.toString() ?? '',
            retailPrice: editing.retailPrice?.toString() ?? '',
            wholesalePrice: editing.wholesalePrice?.toString() ?? '',
            midWholesalePrice: editing.midWholesalePrice?.toString() ?? '',
            institutionalPrice: editing.institutionalPrice?.toString() ?? '',
          } : {}}
          onSubmit={handleSubmit}
          onCancel={handleClose}
          submitLabel={editing ? "Update" : "Create"}
          loading={loading}
          warehouseUuid={warehouseUuid}
        />
      </Modal>


    </div>
  );
}


