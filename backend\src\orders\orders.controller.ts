import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseUUIDPipe,
  BadRequestException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import { OrdersService } from "./orders.service";
import { CreateOrderDto } from "./dto/create-order.dto";
import { UpdateOrderDto } from "./dto/update-order.dto";
import { CancelOrderDto } from "./dto/cancel-order.dto";
import { UpdateOrderStatusDto } from "./dto/update-order-status.dto";
import { DeleteOrderDto } from "./dto/delete-order.dto";
import { AddProductsToOrderDto } from "./dto/add-products-to-order.dto";
import {
  OrderResponseDto,
  OrdersListResponseDto,
} from "./dto/order-response.dto";
import { OrderStatus } from "./order.entity";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./order.constants";
import { LogsService } from "../logs/logs.service";
import { LogsUtilityService } from "../logs/logs-utility.service";
import { In } from "typeorm";

// Utility function for debugging delay
const debugDelay = (ms: number = 3000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

@ApiTags("orders")
@Controller("orders")
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly logsService: LogsService,
    private readonly logsUtilityService: LogsUtilityService,
  ) {}

  /**
   * Utility method to get entity names for logging
   */
  private async getEntityNames(data: {
    customerUuid?: string;
    warehouseUuid?: string;
    userUuid?: string;
    productUuids?: string[];
  }) {
    const entityNames: any = {};
    
    try {
      // Get customer name
      if (data.customerUuid) {
        const customer = await this.ordersService["customerRepository"].findOne({
          where: { id: data.customerUuid, isDeleted: false },
        });
        entityNames.customerName = customer?.name || "Unknown Customer";
      }

      // Get warehouse name
      if (data.warehouseUuid) {
        const warehouse = await this.ordersService["warehouseRepository"]?.findOne({
          where: { id: data.warehouseUuid, isDeleted: false },
        });
        entityNames.warehouseName = warehouse?.name || "Unknown Warehouse";
      }

      // Get user name
      if (data.userUuid) {
        try {
          const user = await this.ordersService["usersService"].findOne(data.userUuid);
          entityNames.userName = user?.name || "Unknown User";
        } catch (error) {
          entityNames.userName = "Unknown User";
        }
      }

      // Get product names
      if (data.productUuids && data.productUuids.length > 0) {
        const products = await this.ordersService["productRepository"].find({
          where: { id: In(data.productUuids), isDeleted: false },
        });
        entityNames.productNames = products.map(p => p.name);
      }
    } catch (error) {
      console.error("Error getting entity names:", error);
    }

    return entityNames;
  }

  @Post()
  @ApiOperation({ summary: "Create a new order" })
  @ApiResponse({
    status: 201,
    description: "Order created successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({
    status: 404,
    description: "User, warehouse, or customer not found",
  })
  async create(@Body() createOrderDto: CreateOrderDto): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.create(createOrderDto);
      
      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: createOrderDto.customerUuid,
        warehouseUuid: createOrderDto.warehouseUuid,
        userUuid: createOrderDto.userUuid,
        productUuids: createOrderDto.items?.map(item => item.productUuid),
      });

      // Log the creation
      await this.logsService.create({
        entityType: "order",
        entity: order.uuid,
        operation: "create",
        userUuid: createOrderDto.userUuid,
        description: `Order ${order.orderNumber} created`,
        data: {
          orderNumber: order.orderNumber,
          customerName: entityNames.customerName,
          warehouseName: entityNames.warehouseName,
          userName: entityNames.userName,
          productNames: entityNames.productNames,
          totalAmount: order.totalAmount,
          status: order.status,
        },
      });

      return order;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  }

  @Post("from-quote/:quoteUuid")
  @ApiOperation({ summary: "Create order from quote" })
  @ApiParam({
    name: "quoteUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Quote UUID",
  })
  @ApiResponse({
    status: 201,
    description: "Order created from quote successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Quote not found" })
  async createFromQuote(
    @Param("quoteUuid", ParseUUIDPipe) quoteUuid: string,
    @Body() body: { userUuid: string },
  ): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.createFromQuote(quoteUuid, body.userUuid);
      
      // Log the creation from quote
      await this.logsService.create({
        entityType: "order",
        entity: order.uuid,
        operation: "create_from_quote",
        userUuid: body.userUuid,
        description: `Order ${order.orderNumber} created from quote`,
        data: {
          orderNumber: order.orderNumber,
          quoteUuid: quoteUuid,
          totalAmount: order.totalAmount,
          status: order.status,
        },
      });

      return order;
    } catch (error) {
      console.error("Error creating order from quote:", error);
      throw error;
    }
  }

  @Post(":uuid/products")
  @ApiOperation({ summary: "Add products to an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Products added successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async addProductsToOrder(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() addProductsDto: AddProductsToOrderDto,
  ): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.addProducts(uuid, addProductsDto.items);
      
      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        productUuids: addProductsDto.items.map(item => item.productUuid),
      });

      // Log the product addition (without userUuid for now)
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "add_products",
        description: `Products added to order ${order.orderNumber}`,
        data: {
          orderNumber: order.orderNumber,
          productNames: entityNames.productNames,
          itemsAdded: addProductsDto.items.length,
          newTotalAmount: order.totalAmount,
        },
      });

      return order;
    } catch (error) {
      console.error("Error adding products to order:", error);
      throw error;
    }
  }

  @Get()
  @ApiOperation({ summary: "Get all orders with pagination and filtering" })
  @ApiQuery({
    name: "customerUuid",
    required: false,
    type: String,
    description: "Filter by customer UUID (optional)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID (optional)",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by order status (optional)",
  })
  @ApiQuery({
    name: "priority",
    required: false,
    type: String,
    description: "Filter by order priority (optional)",
  })
  @ApiQuery({
    name: "orderNumber",
    required: false,
    type: String,
    description: "Filter by order number (optional)",
  })
  @ApiQuery({
    name: "createdFrom",
    required: false,
    type: String,
    description: "Filter orders created after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "createdTo",
    required: false,
    type: String,
    description: "Filter orders created before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "orderDateFrom",
    required: false,
    type: String,
    description: "Filter by order date from (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "orderDateTo",
    required: false,
    type: String,
    description: "Filter by order date to (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "minAmount",
    required: false,
    type: String,
    description: "Filter by minimum total amount (optional)",
  })
  @ApiQuery({
    name: "maxAmount",
    required: false,
    type: String,
    description: "Filter by maximum total amount (optional)",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Orders retrieved successfully",
    type: OrdersListResponseDto,
  })
  async findAll(
    @Query("customerUuid") customerUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("priority") priority?: string,
    @Query("orderNumber") orderNumber?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("orderDateFrom") orderDateFrom?: string,
    @Query("orderDateTo") orderDateTo?: string,
    @Query("minAmount") minAmount?: string,
    @Query("maxAmount") maxAmount?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<OrdersListResponseDto> {
    // Use constants for default values
    const customerUuidFilter = customerUuid ?? EMPTY_UUID_FILTER;
    const warehouseUuidFilter = warehouseUuid ?? EMPTY_UUID_FILTER;
    const statusFilter = status ?? EMPTY_STRING_FILTER;
    const priorityFilter = priority ?? EMPTY_STRING_FILTER;
    const orderNumberFilter = orderNumber ?? EMPTY_STRING_FILTER;

    // Use wide date range if not specified
    const createdFromDate = createdFrom
      ? new Date(createdFrom)
      : MIN_DATE_FILTER;
    const createdToDate = createdTo ? new Date(createdTo) : MAX_DATE_FILTER;
    const orderDateFromDate = orderDateFrom
      ? new Date(orderDateFrom)
      : MIN_DATE_FILTER;
    const orderDateToDate = orderDateTo ? new Date(orderDateTo) : MAX_DATE_FILTER;

    // Parse amount filters
    const minAmountFilter = minAmount ? parseFloat(minAmount) : MIN_NUMBER_FILTER;
    const maxAmountFilter = maxAmount ? parseFloat(maxAmount) : MAX_NUMBER_FILTER;

    return this.ordersService.findAll({
      customerUuid: customerUuidFilter,
      warehouseUuid: warehouseUuidFilter,
      status: statusFilter,
      priority: priorityFilter,
      createdFrom: createdFromDate,
      createdTo: createdToDate,
      page: page || 1,
      limit: limit || 10,
    });
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get an order by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order retrieved successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.findOne(uuid);
      
      // Log the view (without userUuid for now)
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "view",
        description: `Order ${order.orderNumber} viewed`,
        data: {
          orderNumber: order.orderNumber,
        },
      });

      return order;
    } catch (error) {
      console.error("Error finding order:", error);
      throw error;
    }
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order updated successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.update(uuid, updateOrderDto);
      
      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: updateOrderDto.customerUuid,
      });

      // Log the update (without userUuid for now)
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "update",
        description: `Order ${order.orderNumber} updated`,
        data: {
          orderNumber: order.orderNumber,
          customerName: entityNames.customerName,
          updatedFields: Object.keys(updateOrderDto),
        },
      });

      return order;
    } catch (error) {
      console.error("Error updating order:", error);
      throw error;
    }
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update order status" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order status updated successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  @ApiResponse({ status: 400, description: "Invalid status" })
  async updateStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateOrderStatusDto: UpdateOrderStatusDto,
  ): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.updateStatus(uuid, updateOrderStatusDto.status);
      
      // Log the status update
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "update_status",
        userUuid: updateOrderStatusDto.userUuid,
        description: `Order ${order.orderNumber} status updated to ${updateOrderStatusDto.status}`,
        data: {
          orderNumber: order.orderNumber,
          oldStatus: order.status, // This would need to be captured before update
          newStatus: updateOrderStatusDto.status,
        },
      });

      return order;
    } catch (error) {
      console.error("Error updating order status:", error);
      throw error;
    }
  }

  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order cancelled successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async cancelOrder(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() cancelOrderDto: CancelOrderDto,
  ): Promise<OrderResponseDto> {
    try {
      const order = await this.ordersService.cancelOrder(uuid);
      
      // Log the cancellation
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "cancel",
        userUuid: cancelOrderDto.userUuid,
        description: `Order ${order.orderNumber} cancelled`,
        data: {
          orderNumber: order.orderNumber,
          reason: cancelOrderDto.reason,
        },
      });

      return order;
    } catch (error) {
      console.error("Error cancelling order:", error);
      throw error;
    }
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({ status: 200, description: "Order deleted successfully" })
  @ApiResponse({ status: 404, description: "Order not found" })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() deleteOrderDto: DeleteOrderDto,
  ): Promise<{ message: string }> {
    try {
      const result = await this.ordersService.remove(uuid);
      
      // Log the deletion
      await this.logsService.create({
        entityType: "order",
        entity: uuid,
        operation: "delete",
        userUuid: deleteOrderDto.userUuid,
        description: "Order deleted",
        data: {
          message: "Order deleted",
        },
      });

      return result;
    } catch (error) {
      console.error("Error deleting order:", error);
      throw error;
    }
  }

  @Get("list-by-warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get all orders for a specific warehouse" })
  @ApiParam({
    name: "warehouseUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Orders retrieved successfully",
    type: [OrderResponseDto],
  })
  async listByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string) {
    try {
      const orders = await this.ordersService.findByWarehouse(warehouseUuid);
      
      // Get warehouse name for logging
      const warehouse = await this.ordersService["warehouseRepository"]?.findOne({
        where: { id: warehouseUuid, isDeleted: false },
      });

      // Log the warehouse listing (without userUuid for now)
      await this.logsService.create({
        entityType: "order",
        entity: warehouseUuid,
        operation: "list_by_warehouse",
        description: `Orders listed for warehouse ${warehouse?.name || "Unknown Warehouse"}`,
        data: {
          warehouseName: warehouse?.name || "Unknown Warehouse",
          ordersCount: orders.length,
        },
      });

      return orders;
    } catch (error) {
      console.error("Error listing orders by warehouse:", error);
      throw error;
    }
  }
}
