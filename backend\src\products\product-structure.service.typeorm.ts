import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ProductStructure } from "./product-structure.entity";
import { Product } from "./product.entity";
import { CreateProductStructureDto } from "./dto/create-product-structure.dto";
import { UpdateProductStructureDto } from "./dto/update-product-structure.dto";

@Injectable()
export class ProductStructureService {
  constructor(
    @InjectRepository(ProductStructure)
    private productStructureRepository: Repository<ProductStructure>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async create(createProductStructureDto: CreateProductStructureDto): Promise<ProductStructure> {
    // Validate that both assembly and component products exist
    const [assemblyProduct, componentProduct] = await Promise.all([
      this.productRepository.findOne({
        where: { 
          id: createProductStructureDto.assemblyProductUuid, 
          isDeleted: false 
        }
      }),
      this.productRepository.findOne({
        where: { 
          id: createProductStructureDto.componentProductUuid, 
          isDeleted: false 
        }
      })
    ]);

    if (!assemblyProduct) {
      throw new NotFoundException(
        `Assembly product with UUID ${createProductStructureDto.assemblyProductUuid} not found`
      );
    }

    if (!componentProduct) {
      throw new NotFoundException(
        `Component product with UUID ${createProductStructureDto.componentProductUuid} not found`
      );
    }

    // Validate that assembly and component are in the same warehouse
    if (assemblyProduct.warehouseUuid !== createProductStructureDto.warehouseUuid) {
      throw new BadRequestException(
        "Assembly product must belong to the specified warehouse"
      );
    }

    if (componentProduct.warehouseUuid !== createProductStructureDto.warehouseUuid) {
      throw new BadRequestException(
        "Component product must belong to the specified warehouse"
      );
    }

    // Prevent circular references (a product cannot be a component of itself)
    if (createProductStructureDto.assemblyProductUuid === createProductStructureDto.componentProductUuid) {
      throw new BadRequestException(
        "A product cannot be a component of itself"
      );
    }

    // Check if this structure already exists
    const existingStructure = await this.productStructureRepository.findOne({
      where: {
        assemblyProductUuid: createProductStructureDto.assemblyProductUuid,
        componentProductUuid: createProductStructureDto.componentProductUuid,
        isDeleted: false,
      }
    });

    if (existingStructure) {
      throw new BadRequestException(
        "This product structure already exists"
      );
    }

    // Create new product structure
    const productStructure = this.productStructureRepository.create({
      id: ProductStructure.generateId(),
      ...createProductStructureDto,
    });

    return this.productStructureRepository.save(productStructure);
  }

  async findAll(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.productStructureRepository.find({
        where: { isDeleted: false },
        relations: ['assemblyProduct', 'componentProduct'],
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productStructureRepository.count({
        where: { isDeleted: false },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findByWarehouse(warehouseUuid: string, page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.productStructureRepository.find({
        where: { 
          warehouseUuid,
          isDeleted: false 
        },
        relations: ['assemblyProduct', 'componentProduct'],
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productStructureRepository.count({
        where: { 
          warehouseUuid,
          isDeleted: false 
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findByAssemblyProduct(assemblyProductUuid: string, page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.productStructureRepository.find({
        where: { 
          assemblyProductUuid,
          isDeleted: false 
        },
        relations: ['assemblyProduct', 'componentProduct'],
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productStructureRepository.count({
        where: { 
          assemblyProductUuid,
          isDeleted: false 
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findByComponentProduct(componentProductUuid: string, page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.productStructureRepository.find({
        where: { 
          componentProductUuid,
          isDeleted: false 
        },
        relations: ['assemblyProduct', 'componentProduct'],
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productStructureRepository.count({
        where: { 
          componentProductUuid,
          isDeleted: false 
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findOne(uuid: string): Promise<ProductStructure> {
    const productStructure = await this.productStructureRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['assemblyProduct', 'componentProduct'],
    });

    if (!productStructure) {
      throw new NotFoundException(`Product structure with UUID ${uuid} not found`);
    }

    return productStructure;
  }

  async update(uuid: string, updateProductStructureDto: UpdateProductStructureDto): Promise<ProductStructure> {
    const productStructure = await this.findOne(uuid);

    // If updating product references, validate they exist
    if (updateProductStructureDto.assemblyProductUuid) {
      const assemblyProduct = await this.productRepository.findOne({
        where: { 
          id: updateProductStructureDto.assemblyProductUuid, 
          isDeleted: false 
        }
      });
      if (!assemblyProduct) {
        throw new NotFoundException(
          `Assembly product with UUID ${updateProductStructureDto.assemblyProductUuid} not found`
        );
      }
    }

    if (updateProductStructureDto.componentProductUuid) {
      const componentProduct = await this.productRepository.findOne({
        where: { 
          id: updateProductStructureDto.componentProductUuid, 
          isDeleted: false 
        }
      });
      if (!componentProduct) {
        throw new NotFoundException(
          `Component product with UUID ${updateProductStructureDto.componentProductUuid} not found`
        );
      }
    }

    // Prevent circular references
    const finalAssemblyUuid = updateProductStructureDto.assemblyProductUuid || productStructure.assemblyProductUuid;
    const finalComponentUuid = updateProductStructureDto.componentProductUuid || productStructure.componentProductUuid;
    
    if (finalAssemblyUuid === finalComponentUuid) {
      throw new BadRequestException(
        "A product cannot be a component of itself"
      );
    }

    // Update the product structure
    Object.assign(productStructure, updateProductStructureDto);
    return this.productStructureRepository.save(productStructure);
  }

  async softDelete(uuid: string): Promise<ProductStructure> {
    const productStructure = await this.findOne(uuid);
    productStructure.isDeleted = true;
    return this.productStructureRepository.save(productStructure);
  }

  async count(): Promise<number> {
    return this.productStructureRepository.count({
      where: { isDeleted: false },
    });
  }

  async countByWarehouse(warehouseUuid: string): Promise<number> {
    return this.productStructureRepository.count({
      where: { 
        warehouseUuid,
        isDeleted: false 
      },
    });
  }

  // Get all components for a specific assembly product (Bill of Materials)
  async getBillOfMaterials(assemblyProductUuid: string): Promise<ProductStructure[]> {
    return this.productStructureRepository.find({
      where: { 
        assemblyProductUuid,
        isDeleted: false 
      },
      relations: ['componentProduct'],
      order: { createdAt: 'ASC' },
    });
  }

  // Get all assemblies that use a specific component
  async getWhereUsed(componentProductUuid: string): Promise<ProductStructure[]> {
    return this.productStructureRepository.find({
      where: { 
        componentProductUuid,
        isDeleted: false 
      },
      relations: ['assemblyProduct'],
      order: { createdAt: 'ASC' },
    });
  }
}
